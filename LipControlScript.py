import maya.cmds as mc
ctrlCount = 5 
stride = 1/(ctrlCount-1) * 12
ribbon = mc.ls(sl=True)[0]
for i in range(0, ctrlCount):
    mc.select(cl=True)
    drvJntName = "jnt_drv_upper_lip_" + str(i+1).zfill(2)
    mc.joint(n=drvJntName)
    jntGrpName = drvJntName + "_grp"
    mc.group(drvJntName, n=jntGrpName)
    
    ctrlPos = mc.pointOnSurface(ribbon, p=True, u=i*stride, v=0.5)
    mc.setAttr(jntGrpName+".tx", ctrlPos[0])
    mc.setAttr(jntGrpName+".ty", ctrlPos[1])
    mc.setAttr(jntGrpName+".tz", ctrlPos[2])
    
    ctrlName = drvJntName.replace("jnt", "ac")
    ctrlGrpName = ctrlName + "_grp"
    mc.sphere(n=ctrlName,r=0.24)
    mc.group(ctrlName, n=ctrlGrpName)
    mc.setAttr(ctrlGrpName+".tx", ctrlPos[0])
    mc.setAttr(ctrlGrpName+".ty", ctrlPos[1])
    mc.setAttr(ctrlGrpName+".tz", ctrlPos[2])
    
    mc.connectAttr(ctrlName+".translate", drvJntName+".translate")
    mc.connectAttr(ctrlName+".rotate", drvJntName+".rotate")
    mc.connectAttr(ctrlName+".scale", drvJntName+".scale")