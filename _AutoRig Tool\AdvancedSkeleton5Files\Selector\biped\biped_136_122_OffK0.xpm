/* XPM */
static char *biped_136_122_OffK0[] = {
/* columns rows colors chars-per-pixel */
"48 18 131 2",
"   c #8B8A8A",
".  c #8B8B8B",
"X  c gray55",
"o  c #8D8D8D",
"O  c #8E8E8E",
"+  c gray56",
"@  c gray57",
"#  c #929292",
"$  c #949394",
"%  c gray59",
"&  c #979797",
"*  c #989898",
"=  c #999998",
"-  c #9A9A9A",
";  c #9B9B9B",
":  c gray61",
">  c #9D9D9D",
",  c gray62",
"<  c #9F9F9F",
"1  c gray63",
"2  c #A2A2A2",
"3  c #A2A3A3",
"4  c #A4A4A5",
"5  c #A4A5A5",
"6  c #ACADAC",
"7  c #AEADAE",
"8  c #AEAEAE",
"9  c gray69",
"0  c #B0B0B1",
"q  c gray73",
"w  c #BBBABA",
"e  c #BBBCBB",
"r  c #BEBFBE",
"t  c gray75",
"y  c #C6C6C6",
"u  c gray78",
"i  c #CDCDCD",
"p  c #CDCECD",
"a  c #D2D2D2",
"s  c #D2D3D2",
"d  c #D9D9DA",
"f  c #DBDBDC",
"g  c #DCDBDA",
"h  c gainsboro",
"j  c #E2E2E2",
"k  c #E3E2E3",
"l  c gray89",
"z  c #E5E4E4",
"x  c #E5E5E6",
"c  c #E6E5E7",
"v  c #E7E9E9",
"b  c gray91",
"n  c #E9E8E9",
"m  c #EAEAEA",
"M  c #EBEBEA",
"N  c gray92",
"B  c #EBECEC",
"V  c #EDEDEC",
"C  c #EDECEE",
"Z  c #EEEEEE",
"A  c #EFF0EF",
"S  c #F0EFF0",
"D  c #F1EFF0",
"F  c #F0F1EF",
"G  c #F0F2F1",
"H  c #F3F3F1",
"J  c #F3F4F4",
"K  c #F4F4F4",
"L  c #F6F6F5",
"P  c #F6F6F7",
"I  c #F8F9F7",
"U  c #F8F8F8",
"Y  c #F9FAF9",
"T  c gray98",
"R  c #FBFCFA",
"E  c #FCFBFC",
"W  c gray99",
"Q  c #FCFDFD",
"!  c #FDFCFD",
"~  c #FDFDFC",
"^  c #FDFDFD",
"/  c #FCFDFE",
"(  c #FCFDFF",
")  c #FDFCFE",
"_  c #FDFCFF",
"`  c #FDFDFE",
"'  c #FDFDFF",
"]  c #FCFEFC",
"[  c #FCFEFD",
"{  c #FCFFFC",
"}  c #FCFFFD",
"|  c #FDFEFC",
" . c #FDFEFD",
".. c #FDFFFD",
"X. c #FCFEFE",
"o. c #FCFEFF",
"O. c #FCFFFE",
"+. c #FCFFFF",
"@. c #FDFEFE",
"#. c #FDFEFF",
"$. c #FDFFFE",
"%. c #FDFFFF",
"&. c #FEFCFD",
"*. c #FEFDFC",
"=. c #FEFDFD",
"-. c #FFFCFD",
";. c #FFFDFC",
":. c #FFFDFD",
">. c #FEFCFE",
",. c #FEFCFF",
"<. c #FEFDFE",
"1. c #FEFDFF",
"2. c #FFFCFE",
"3. c #FFFCFF",
"4. c #FFFDFE",
"5. c #FFFDFF",
"6. c #FEFEFC",
"7. c #FEFEFD",
"8. c #FEFFFD",
"9. c #FFFEFC",
"0. c #FFFEFD",
"q. c #FFFFFC",
"w. c #FFFFFD",
"e. c #FEFEFE",
"r. c #FEFEFF",
"t. c #FEFFFE",
"y. c #FEFFFF",
"u. c #FFFEFE",
"i. c #FFFEFF",
"p. c #FFFFFE",
"a. c gray100",
/* pixels */
"t t t t t t t t r r t r r r r r t r r r r r r r r r r r r r r r r r r r r r r r r r r r r r r r ",
"t , , , & 3 c _ R _ _ _ _ _ R R _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ R _ b 2 = : < < r ",
"t , , , # 8 M _ _ R R R _ _ R _ R _ _ _ _ _ _ R _ _ _ _ _ _ _ _ _ R _ _ _ _ _ _ _ M 7 $ : < < r ",
"t , , , O q A _ _ R _ _ _ _ _ _ _ _ _ R _ _ _ _ _ _ _ R _ _ R _ _ _ _ _ _ R R _ _ Z w @ < < < r ",
"r , , , X y J _ R _ _ _ _ R _ _ _ _ R _ R _ R _ _ _ _ _ _ _ R _ _ _ _ _ _ R R _ R L u X < < < r ",
"r , , , X s I _ _ R R _ _ _ _ R _ _ _ _ R _ _ _ _ _ _ R _ R _ _ _ _ R R R _ _ _ _ I s X < : < r ",
"r < , , + h R _ _ _ _ R R _ _ _ R R _ _ _ _ _ R _ R _ _ _ _ _ _ _ _ _ R _ R R _ _ R h + < : < r ",
"r < , : % l _ _ _ R _ R _ _ R _ _ _ _ _ _ _ _ _ _ _ _ _ R _ _ R _ R _ _ _ _ _ _ _ R l $ : < < r ",
"r < < = 1 b _ _ _ _ _ _ R _ _ _ _ _ _ _ _ R _ _ R _ R _ _ _ _ R _ _ _ _ R _ R _ R _ M < * , , r ",
"r < , # 7 F R _ _ R _ R _ R R _ _ _ R _ R _ R _ _ R _ _ _ _ _ _ _ R _ _ _ _ _ _ R E C 7 # , , r ",
"r , , X r F R _ _ _ R _ _ _ _ _ _ _ R _ R _ R _ _ _ _ _ _ R _ _ _ _ _ R _ _ R R _ _ F e o , < r ",
"r , <   p I R R _ R _ _ _ R _ _ _ R _ _ R _ _ _ _ _ R _ _ _ R _ _ _ _ _ R _ _ _ _ R P i   , < r ",
"r < >   d T R _ R _ R _ _ _ _ _ _ R R _ _ _ R R _ _ _ _ R _ R _ _ _ _ _ _ _ _ _ R R R h X < < r ",
"r < < @ z T _ _ _ _ _ _ R _ R _ _ _ R _ _ R _ _ _ _ R R _ R _ R _ R R R R _ R _ _ _ R z + , < r ",
"r < ; ; n _ R _ R _ _ _ R R _ _ R _ _ R _ _ _ R R _ R _ R _ _ R _ _ R R R _ _ R _ _ _ M ; ; < r ",
"r < % 4 C _ _ R _ R R R _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ R _ _ _ _ R _ _ _ R _ _ R _ _ _ Z 5 % < r ",
"r < $ 0 F R _ _ _ _ _ R _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ R _ _ _ _ _ _ F 0 $ < r ",
"r r r t r r r r r r r r r r r r r r r r t r r r t r t t r r r r t t r r r r t r r r r r t r r r "
};
