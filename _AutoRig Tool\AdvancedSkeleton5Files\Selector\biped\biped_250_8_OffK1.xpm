/* XPM */
static char *biped_250_8_OffK1[] = {
/* columns rows colors chars-per-pixel */
"23 23 34 1",
"  c #FF4444",
". c #FF4747",
"X c #FF4848",
"o c #FF4949",
"O c #FF4A4A",
"+ c #FF4C4C",
"@ c #FF5555",
"# c #FF5858",
"$ c #FF5959",
"% c #FF5A5A",
"& c #FF5B5B",
"* c #EB7F7F",
"= c #EC7E7E",
"- c #EC7F7F",
"; c #FF6060",
": c #FF6161",
"> c #FF6565",
", c #FF6B6B",
"< c #FF6C6C",
"1 c #F77777",
"2 c #F87676",
"3 c #F87777",
"4 c #F97676",
"5 c #FF7070",
"6 c gray75",
"7 c #E38484",
"8 c #E58383",
"9 c #E68383",
"0 c #E78383",
"q c #E48484",
"w c #E58484",
"e c #E98080",
"r c #E98181",
"t c #EA8080",
/* pixels */
"66666666666666666666666",
"6777q8-3,>&@&>,2tqq7776",
"677qt3>@XXX..XX@>1tq776",
"67q*5&X.. . . ..+&5tq76",
"67*5&.           X&<*76",
"683&X             X&286",
"6*>+.             .X>*6",
"63@.               .@36",
"6<X                 X<6",
"6>X                 X>6",
"6&.                 X&6",
"6@.                 .&6",
"6&.                 X@6",
"6>.                 X>6",
"6,X.               .+<6",
"63@.               .@36",
"6->X.             .+>-6",
"683&X.            X&386",
"68*5&X           X&5tq6",
"67q-5&X.   .   .X&<tq86",
"67q8t3>@+XX...X@>3tq776",
"6778q8-3<&&&&><3-8q7776",
"66666666666666666666666"
};
