import maya.cmds as MC

#ask maya to list all the selected objects 
#and put them in a new variable called selection
#selection is of the type list 
selection = MC.ls(sl=True)
#loop through all selected joints, for every one
#go through the body of this for loop, use jnt
#as their name
for jnt in selection:
    controllerName = jnt.replace("jnt_drv_", "ac_fk_")
    #asking maya to make a circle 
    #by calling a circle function
    MC.circle(n=controllerName, r=1, nr=(1,0,0))
    grpName = controllerName + "_grp"
    MC.group(controllerName, name=grpName)
    #do the matchtransform step
    MC.matchTransform(grpName, jnt)
    #do orient constrant
    MC.orientConstraint(controllerName, jnt)
