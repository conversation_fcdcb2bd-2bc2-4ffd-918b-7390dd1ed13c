import maya.cmds as mc
#######################################################################
#                          UI                                         #
#######################################################################
myWindow = mc.window(title = "create joint chain along curve selected")
mc.rowColumnLayout(nc = 2)
mc.text(l = "joint amount:")
jntAmountIF = mc.intField(v = 5, min = 2)
mc.button(l = "create", w = 150, c = "createJntAlongCurve()")
mc.button(l = "cancel", w = 150, c = "mc.deleteUI(myWindow)")
mc.showWindow()

#######################################################################
#                          Method                                     #
#######################################################################
def createJntAlongCurve():
    curveSelected = mc.ls(sl = True) [0]
    jointAmount = mc.intField(jntAmountIF, q = True, v = True)
    previousJnt = ""
    rootJnt = ""
    for i in range(0, jointAmount):
        mc.select(cl = True)
        newJnt = mc.joint()
        motionPath = mc.pathAnimation(newJnt, c = curveSelected, fractionMode = True)
        mc.cutKey(motionPath + ".u", time = ())
        mc.setAttr(motionPath + ".u", i * (1.0/(jointAmount - 1)))
        #delete motion path
        mc.delete(newJnt + ".tx", icn = True)
        mc.delete(newJnt + ".ty", icn = True)
        mc.delete(newJnt + ".tz", icn = True)
        mc.delete(motionPath)
        
        if i == 0:
            previousJnt = newJnt
            rootJnt = newJnt
            continue
        
        mc.parent(newJnt, previousJnt)
        previosJnt = newJnt
    mc.joint(rootJnt, e = True, oj = "xyz", sao = "yup", ch = True, zso = True)
