/* XPM */
static char *biped_71_47_OffK0[] = {
/* columns rows colors chars-per-pixel */
"32 15 148 2",
"   c #8B8B8B",
".  c gray55",
"X  c #8D8D8D",
"o  c #8D8E8D",
"O  c gray56",
"+  c #8F8F90",
"@  c #909090",
"#  c #909091",
"$  c #919090",
"%  c #929292",
"&  c #939393",
"*  c gray58",
"=  c #959695",
"-  c gray59",
";  c gray60",
":  c #999A99",
">  c #9A9999",
",  c #9A9A9A",
"<  c #9B9B9B",
"1  c #9D9D9D",
"2  c #9E9E9D",
"3  c gray62",
"4  c #A3A3A2",
"5  c #A7A7A7",
"6  c #AAA9AA",
"7  c #ACADAC",
"8  c gray71",
"9  c #B9B9B9",
"0  c #BCBCBC",
"q  c #BEBDBE",
"w  c gray75",
"e  c #C0C0C1",
"r  c #C0C1C1",
"t  c #C1C0C0",
"y  c #C1C1C1",
"u  c #C1C2C1",
"i  c #C5C5C5",
"p  c #C7C6C6",
"a  c #C7C7C6",
"s  c #CACACA",
"d  c #CBCACA",
"f  c #CECECE",
"g  c #CFCECE",
"h  c #D2D2D0",
"j  c #D2D3D2",
"k  c #D3D2D3",
"l  c #D3D3D2",
"z  c #D5D5D4",
"x  c #D6D5D5",
"c  c gray84",
"v  c #D6D7D6",
"b  c #D7D7D7",
"n  c #D9DAD9",
"m  c #DAD9D9",
"M  c #DBDADA",
"N  c #DBDADB",
"B  c #DBDCDB",
"V  c #DDDDDD",
"C  c #DDDDDE",
"Z  c #DDDEDD",
"A  c #DEDEDD",
"S  c gray87",
"D  c #E1E0E0",
"F  c #E2E2E2",
"G  c #E5E4E4",
"H  c #E4E6E4",
"J  c #E6E5E6",
"K  c #E7E7E7",
"L  c #E8E7E7",
"P  c #EAE7E8",
"I  c #E9E9E9",
"U  c #E9E9EA",
"Y  c #E9EAEA",
"T  c #EAE9E9",
"R  c #EEEEEF",
"E  c #EFEFEF",
"W  c #F0F1F1",
"Q  c gray95",
"!  c #F3F3F4",
"~  c #F4F3F2",
"^  c #F4F4F5",
"/  c #F5F6F6",
"(  c #F5F6F7",
")  c #F6F5F5",
"_  c #F6F6F6",
"`  c #F6F6F7",
"'  c #F7F7F6",
"]  c gray97",
"[  c #F7F7F8",
"{  c #F7F8F8",
"}  c #F8F7F7",
"|  c #F9F8F9",
" . c #F9F9F9",
".. c #F9F9FA",
"X. c #F9FAFA",
"o. c #F9FBFA",
"O. c #FAF9F9",
"+. c #FAF9FA",
"@. c #FAF9FB",
"#. c #FAFAF9",
"$. c gray98",
"%. c #FAFAFB",
"&. c #FBFAFB",
"*. c #FBFBFC",
"=. c #FBFDFB",
"-. c #FDFCFB",
";. c #FDFCFD",
":. c #FDFDFC",
">. c #FDFDFD",
",. c #FDFCFF",
"<. c #FDFDFE",
"1. c #FDFDFF",
"2. c #FDFEFC",
"3. c #FDFEFD",
"4. c #FDFFFC",
"5. c #FDFFFD",
"6. c #FCFEFE",
"7. c #FCFFFE",
"8. c #FDFEFE",
"9. c #FDFEFF",
"0. c #FDFFFE",
"q. c #FDFFFF",
"w. c #FEFCFD",
"e. c #FEFDFC",
"r. c #FEFDFD",
"t. c #FFFCFD",
"y. c #FFFDFD",
"u. c #FEFCFE",
"i. c #FEFCFF",
"p. c #FEFDFE",
"a. c #FEFDFF",
"s. c #FFFCFE",
"d. c #FFFDFE",
"f. c #FFFDFF",
"g. c #FEFEFC",
"h. c #FEFEFD",
"j. c #FEFFFC",
"k. c #FEFFFD",
"l. c #FFFEFD",
"z. c #FFFFFD",
"x. c #FEFEFE",
"c. c #FEFEFF",
"v. c #FEFFFE",
"b. c #FEFFFF",
"n. c #FFFEFE",
"m. c #FFFEFF",
"M. c #FFFFFE",
"N. c gray100",
/* pixels */
"w w t w w w w w w w w w w w w w w w w w w w w t w w q q q q t t ",
"w ; ; 3 3 3 3 3 3 3 3 3 3 3 3 ; $ o .     o o $ - ; 2 4 6 7 7 w ",
"w 3 ; - $ $ $ $ + $ + + + + * 3 5 8 t a s f l c n n V D F H H w ",
"w l g d a t t t t e e q 9 0 a l M J W Q ) ) @.g.q.q.q.q.g.g.q.w ",
"q q.-.-.o.' ) ) ^ ) ) Q R Q @.g.q.q.q.g.t.t.g.g.g.q.t.-.q.g.q.w ",
"q g.q.g.q.q.q.q.q.t.q.q.t.g.g.q.q.t.g.q.t.q.q.g.g.q.q.q.q.g.t.w ",
"q q.q.q.q.q.q.g.q.t.q.t.q.t.q.t.t.q.q.g.q.q.q.q.q.t.q.q.q.q.q.w ",
"q q.q.q.g.g.q.g.t.t.q.q.t.q.q.t.t.q.-.q.g.q.t.q.q.t.q.t.t.g.g.w ",
"t q.g.q.q.t.t.t.t.,.q.q.g.q.-.t.t.t.q.q.q.-.t.t.q.t.q.q.t.q.-.q ",
"q q.g.g.t.t.q.q.q.t.q.*.t.q.t.q.*.t.t.t.-.q.q.q.g.q.q.q.t.t.q.q ",
"w q.-.q.-.t.g.q.q.g.t.q.q.t.g.q.q.q.t.t.q.q.q.g.q.q.q.-.q.q.q.q ",
"w E -.t.q.t.g.g.g.g.q.q.q.q.q.q.t.-.t.q.t.-.q.g.-.-.q.t.q.q.q.q ",
"w l ^ @.@.@.@.@.' ' ) } @.@.@.o.@.@.@.@.@.-.q.q.-.q.q.q.-.q.q.q ",
"w 6 c N V V V M c c c c m Z Z Z V V Z Z V F F L L L L T L T T q ",
"w w w w w w w w q q t t w w w w w w w w t q t q q q t t q t e q "
};
