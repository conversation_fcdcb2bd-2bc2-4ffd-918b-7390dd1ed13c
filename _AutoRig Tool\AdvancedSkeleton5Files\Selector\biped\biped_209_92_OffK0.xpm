/* XPM */
static char *biped_209_92_OffK0[] = {
/* columns rows colors chars-per-pixel */
"15 15 55 1",
"  c #0E00EF",
". c #0F00EE",
"X c #0F00EF",
"o c blue",
"O c #0B00F2",
"+ c #0B00F3",
"@ c #0C02F3",
"# c #1207EF",
"$ c #1B05E4",
"% c #1C06E4",
"& c #5130C0",
"* c #6B45AF",
"= c #6D47AE",
"- c #6E48AD",
"; c #714BAB",
": c #714CAB",
"> c #6B6BBD",
", c #6B6BBE",
"< c #6C69BC",
"1 c #6F6CBA",
"2 c #6F6CBB",
"3 c #6D6DBC",
"4 c #7260B3",
"5 c #7266B5",
"6 c #7367B4",
"7 c #776DB2",
"8 c #706DB9",
"9 c #716CB8",
"0 c #716DB8",
"q c #716EB9",
"w c #786DB2",
"e c #796EB1",
"r c #786EB2",
"t c #7A6FB0",
"y c #554AC7",
"u c #564BC6",
"i c gray62",
"p c #9E9E9F",
"a c #9183A2",
"s c #918EA5",
"d c #918EA6",
"f c #958AA1",
"g c #958BA0",
"h c #968BA0",
"j c #9695A3",
"k c #9696A3",
"l c #9796A3",
"z c #9595A4",
"x c #9595A5",
"c c #9695A4",
"v c #9696A4",
"b c #9696A5",
"n c #9896A1",
"m c #9996A1",
"M c gray75",
/* pixels */
"MMMMMMMMMMMMMMM",
"Miiiib8#8kiiiiM",
"Miiiib,o<sppiiM",
"Miiipb,o<sppiiM",
"Mpiipb3o3lipiiM",
"Mpplls<o<sppllM",
"M67ww5you6ttwtM",
"M$XXXX+o+XXXX$M",
"M;===*&o&*==*;M",
"Mgffha4o4aghhhM",
"Miiipb8o8liiiiM",
"Miiipb<o,biiiiM",
"Miiiiz,o,liiiiM",
"Miiiiz8+1liiiiM",
"MMMMMMMMMMMMMMM"
};
