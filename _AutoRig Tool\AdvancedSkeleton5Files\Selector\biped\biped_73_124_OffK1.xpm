/* XPM */
static char *biped_73_124_OffK1[] = {
/* columns rows colors chars-per-pixel */
"17 12 56 1",
"  c #D47E7E",
". c #D57E7E",
"X c #D67F7F",
"o c #D77F7F",
"O c #D8807F",
"+ c gray75",
"@ c #D98080",
"# c #DB8080",
"$ c #DB8081",
"% c #DB8181",
"& c #DC8181",
"* c #DD8282",
"= c #DE8182",
"- c #DE8282",
"; c #DF8282",
": c #E08282",
"> c #E08383",
", c #E18383",
"< c #E28383",
"1 c #E38484",
"2 c #E48484",
"3 c #E58585",
"4 c #EA8786",
"5 c #EB8787",
"6 c #EC8888",
"7 c #F38A8A",
"8 c #F48B8A",
"9 c #F88C8C",
"0 c #FC8D8E",
"q c #FC8E8E",
"w c #FF8F8F",
"e c #FF9291",
"r c #FF9392",
"t c #FF9494",
"y c #FF9595",
"u c #FF9696",
"i c #FF9797",
"p c #FF9899",
"a c #FF9A9A",
"s c #FF9B9A",
"d c #FF9C9B",
"f c #FF9C9C",
"g c #FF9C9D",
"h c #FF9D9D",
"j c #FF9F9F",
"k c #FFA1A0",
"l c #FFA4A4",
"z c #FFA6A6",
"x c #FFA6A7",
"c c #FFA7A6",
"v c #FFA7A7",
"b c #FFA7A8",
"n c #FFA8A8",
"m c #FFA9A9",
"M c #FFAAA9",
"N c #FFAAAA",
/* pixels */
"+++++++++++++++++",
"+1111<111<;&&<<1+",
"+111<3<;Xo131<o&+",
"+1113;&&5qyyyy8o+",
"+111&&<qfcNNNNf4+",
"+111O3icNNNNNNci+",
"+1&.4acNNNNNNNba+",
"+&o8dcNNNNNNNNce+",
"+X9fbNNNNNNNNNf1+",
"+6fNNNNNNNNNbjq.+",
"+icNNNNNNNNbjq$$+",
"+++++++++++++++++"
};
