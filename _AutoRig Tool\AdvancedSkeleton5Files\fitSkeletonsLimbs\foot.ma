//Maya ASCII 2012 scene
//Name: foot.ma
//Last modified: Sat, Apr 22, 2017 10:06:46 AM
//Codeset: 1252
requires maya "2008";
currentUnit -l centimeter -a degree -t pal;
fileInfo "application" "maya";
fileInfo "product" "Maya 2012";
fileInfo "version" "2012 x64";
fileInfo "cutIdentifier" "201201172029-821146";
fileInfo "osv" "Microsoft Business Edition, 64-bit  (Build 9200)\n";
createNode transform -n "FitSkeleton";
	addAttr -ci true -sn "visCylinders" -ln "visCylinders" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "visBoxes" -ln "visBoxes" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "visBones" -ln "visBones" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "lockCenterJoints" -ln "lockCenterJoints" -dv 1 -min 0 -max 
		1 -at "bool";
	addAttr -ci true -sn "visGap" -ln "visGap" -dv 0.75 -min 0 -max 1 -at "double";
	addAttr -ci true -m -im false -sn "drivingSystem" -ln "drivingSystem" -at "message";
	addAttr -ci true -m -sn "drivingSystem_ToeCurl_R" -ln "drivingSystem_ToeCurl_R" 
		-dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -m -sn "drivingSystem_ToeCurl_L" -ln "drivingSystem_ToeCurl_L" 
		-dv 1 -min 0 -max 1 -at "bool";
	setAttr ".ove" yes;
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr ".visBoxes" yes;
	setAttr ".visGap" 1;
	setAttr -s 38 ".drivingSystem";
	setAttr -s 19 ".drivingSystem_ToeCurl_R";
	setAttr -s 19 ".drivingSystem_ToeCurl_R";
	setAttr -s 19 ".drivingSystem_ToeCurl_L";
	setAttr -s 19 ".drivingSystem_ToeCurl_L";
createNode nurbsCurve -n "FitSkeletonShape" -p "FitSkeleton";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 29;
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		2.3508348746736751 1.4394712022965405e-016 -2.3508348746736716
		-3.7929511823487981e-016 2.035719696933274e-016 -3.3245825626631635
		-2.3508348746736729 1.4394712022965413e-016 -2.3508348746736729
		-3.3245825626631635 5.899006384856358e-032 -9.6338085217116898e-016
		-2.3508348746736734 -1.4394712022965408e-016 2.350834874673672
		-1.0017616090771558e-015 -2.0357196969332745e-016 3.3245825626631644
		2.3508348746736716 -1.4394712022965413e-016 2.3508348746736729
		3.3245825626631635 -1.0933890203714376e-031 1.7856397797841755e-015
		2.3508348746736751 1.4394712022965405e-016 -2.3508348746736716
		-3.7929511823487981e-016 2.035719696933274e-016 -3.3245825626631635
		-2.3508348746736729 1.4394712022965413e-016 -2.3508348746736729
		;
createNode joint -n "Ankle" -p "FitSkeleton";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "worldOrient" -ln "worldOrient" -min 0 -max 5 -en "xUp:yUp:zUp:xDown:yDown:zDown" 
		-at "enum";
	setAttr ".t" -type "double3" -1.0867024199147359 0.84983532411015528 -0.068643641764130958 ;
	setAttr ".ro" 3;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 92.461691535588884 -64.182207669002523 -92.216232863019272 ;
	setAttr ".pa" -type "double3" 3.1147589914174403 -1.2104724556304993 -11.405913270501992 ;
	setAttr ".dl" yes;
	setAttr ".typ" 4;
	setAttr -k on ".fat" 0.36999999999999988;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
	setAttr -k on ".worldOrient" 3;
createNode joint -n "Heel" -p "Ankle";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.36999999999999988 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -0.21390472772036562 -1.0359533632655671 0.012037760201835557 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 6.6990432978325351e-014 1.4250412077426056e-013 -64.202940265923459 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Heel";
	setAttr -k on ".fat";
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "FootSideOuter" -p "Ankle";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 1.0786521911225035 -0.4111887643724737 0.47385875407494571 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 6.6990432978325351e-014 1.4250412077426056e-013 -64.202940265923459 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "PinkyToe";
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "FootSideInner" -p "Ankle";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 1.0646362038028099 -0.41796347004945028 -0.35822004751468572 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 6.6990432978325351e-014 1.4250412077426056e-013 -64.202940265923459 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "BigToe";
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Toes" -p "Ankle";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 1.5306530265296578 1.1102230246251565e-016 -2.2204460492503131e-016 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -0.27504658416028155 1.0358575801884511 10.925820599927832 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "ToesEnd" -p "Toes";
	setAttr ".t" -type "double3" 0.71522852250715285 0 1.3472447601969861e-009 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -4.1676687238326216e-007 3.0704561875420486e-006 7.138969551653072 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "ToesEnd";
createNode joint -n "PinkyToe1" -p "Toes";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -0.16847082402470615 -0.044728251237514449 0.40392606173837198 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0 3.0230341620404083e-006 14.869 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyToe2" -p "PinkyToe1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.22551338571312973 7.2164496600635175e-016 -1.8664736423090744e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyToe3" -p "PinkyToe2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.14000000000000035 6.6613381477509392e-016 1.1102230246251565e-015 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 5.9443701196145432e-016 1.8425193597275258e-015 3.180554681463516e-015 ;
	setAttr ".typ" 5;
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyToe4" -p "PinkyToe3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.06999999999999984 2.2204460492503131e-016 6.6613381477509392e-016 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 5.9443701196145432e-016 1.8425193597275258e-015 3.180554681463516e-015 ;
	setAttr ".typ" 5;
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "RingToe1" -p "Toes";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -0.057747150571215267 -0.015331610526349682 0.28032198185197088 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0 3.0230341620404083e-006 14.869 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "RingToe2" -p "RingToe1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.23730413361723057 5.5511151231257827e-017 -1.9640933324183152e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "RingToe3" -p "RingToe2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.15999999999999948 1.2212453270876722e-015 1.3322676295501878e-015 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 5.9443701196145432e-016 1.8425193597275258e-015 3.180554681463516e-015 ;
	setAttr ".typ" 5;
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "RingToe4" -p "RingToe3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.089999999999999192 4.4408920985006262e-016 8.8817841970012523e-016 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 5.9443701196145432e-016 1.8425193597275258e-015 3.180554681463516e-015 ;
	setAttr ".typ" 5;
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleToe1" -p "Toes";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.045692034280639948 0.01213103100010815 0.13712216810915834 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0 3.0230341620404083e-006 14.869 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleToe2" -p "MiddleToe1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.27494201662014151 2.2204460492503131e-016 -2.2755952677755431e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleToe3" -p "MiddleToe2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.17000000000000037 4.4408920985006262e-016 1.7763568394002505e-015 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.1211106769460389e-014 -4.3935245519340728e-015 
		6.361109362927032e-015 ;
	setAttr ".typ" 5;
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleToe4" -p "MiddleToe3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.10000000000000053 -1.1102230246251565e-016 0 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.1211106769460389e-014 -4.3935245519340728e-015 
		6.361109362927032e-015 ;
	setAttr ".typ" 5;
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "LongToe1" -p "Toes";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.04569178777134586 0.012130965552992312 -0.050651630936645597 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0 3.0230341620404083e-006 14.869 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "LongToe2" -p "LongToe1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.34999999999999587 2.7755575615628914e-016 -2.8968316634347957e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "LongToe3" -p "LongToe2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.18000000000000016 4.4408920985006262e-016 -4.4408920985006262e-016 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -5.7667057163120052e-015 -4.3936216147600438e-015 
		-1.2722218725854061e-014 ;
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "LongToe4" -p "LongToe3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.11999999999999988 -3.3306690738754696e-016 4.4408920985006262e-016 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -5.7667057163120052e-015 -4.3936216147600438e-015 
		-1.2722218725854061e-014 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "ToesEnd";
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "BigToe1" -p "Toes";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.04569148811321444 0.012130885995095375 -0.27891055264572384 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0 7.782368962709335e-005 14.868752760813617 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "BigToe2" -p "BigToe1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.34999999999999609 -5.5511151231257827e-017 -2.8968472065571405e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "BigToe3" -p "BigToe2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.27000000000000157 9.9920072216264089e-016 5.5511151231257827e-016 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -8.9472239992157827e-015 -4.3935730833470591e-015 
		-9.5416640443905471e-015 ;
	setAttr ".typ" 5;
	setAttr -k on ".fat" 0.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode transform -s -n "persp";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -4.1831990948504885 3.4196682467300756 4.5572343062860394 ;
	setAttr ".r" -type "double3" -33.938352729604212 -45.000000000000099 -2.2489917831974757e-015 ;
createNode camera -s -n "perspShape" -p "persp";
	setAttr -k off ".v" no;
	setAttr ".fl" 34.999999999999986;
	setAttr ".ncp" 1;
	setAttr ".coi" 5.5256551682852511;
	setAttr ".imn" -type "string" "persp";
	setAttr ".den" -type "string" "persp_depth";
	setAttr ".man" -type "string" "persp_mask";
	setAttr ".tp" -type "double3" 0 0.04895648861971813 4.4408920985006262e-016 ;
	setAttr ".hc" -type "string" "viewSet -p %camera";
createNode transform -s -n "top";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -1.0021558588903394 100.1 1.5572086278848016 ;
	setAttr ".r" -type "double3" -89.999999999999986 0 0 ;
	setAttr ".rp" -type "double3" 0 2.2204460492503131e-016 -1.4210854715202004e-014 ;
	setAttr ".rpt" -type "double3" 0 -1.4432899320127038e-014 1.3988810110276976e-014 ;
createNode camera -s -n "topShape" -p "top";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 100.0472196639698;
	setAttr ".ow" 2.5841184387617777;
	setAttr ".imn" -type "string" "top";
	setAttr ".den" -type "string" "top_depth";
	setAttr ".man" -type "string" "top_mask";
	setAttr ".tp" -type "double3" -0.91284748309541697 0.05278033603019594 1.6115702479338623 ;
	setAttr ".hc" -type "string" "viewSet -t %camera";
	setAttr ".o" yes;
createNode transform -s -n "front";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 0 0 100.1 ;
createNode camera -s -n "frontShape" -p "front";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 100.1;
	setAttr ".ow" 30;
	setAttr ".imn" -type "string" "front";
	setAttr ".den" -type "string" "front_depth";
	setAttr ".man" -type "string" "front_mask";
	setAttr ".hc" -type "string" "viewSet -f %camera";
	setAttr ".o" yes;
createNode transform -s -n "side";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 100.1 0.42439436681133191 0.84463115891536267 ;
	setAttr ".r" -type "double3" 0 89.999999999999986 0 ;
	setAttr ".rp" -type "double3" 0 1.3877787807814457e-017 -1.4210854715202004e-014 ;
	setAttr ".rpt" -type "double3" -1.4210854715202007e-014 0 1.4210854715202007e-014 ;
createNode camera -s -n "sideShape" -p "side";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 101.26098379004145;
	setAttr ".ow" 2.6252826434989904;
	setAttr ".imn" -type "string" "side";
	setAttr ".den" -type "string" "side_depth";
	setAttr ".man" -type "string" "side_mask";
	setAttr ".tp" -type "double3" -1.1609837900414561 0.078888054094666066 0.98046581517653608 ;
	setAttr ".hc" -type "string" "viewSet -s %camera";
	setAttr ".o" yes;
createNode animCurveUA -n "SDK2FKBigToe1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "bigCurl" -ln "bigCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999892711639 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKBigToe2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "bigCurl" -ln "bigCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999892711639 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKLongToe1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "longCurl" -ln "longCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999892711639 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKLongToe2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "longCurl" -ln "longCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999892711639 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKLongToe3_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "longCurl" -ln "longCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKMiddleToe1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999892711639 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999892711639 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe3_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKRingToe1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999892711639 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingToe2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999892711639 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingToe3_R_rotateZ1";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyToe1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999892711639 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999892711639 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe3_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 1.9999998807907111 0 0 10 -10;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKLongToe1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -0.19999998807907102 0 0 10 1;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKBigToe1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -6.5999996066093445 0 0 10 33;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingToe1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 6.5999996066093445 0 0 10 -33;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 9.9999994039535522 0 0 10 -50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKBigToe1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "bigCurl" -ln "bigCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999893 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKBigToe2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "bigCurl" -ln "bigCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999893 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKLongToe1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "longCurl" -ln "longCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999893 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKLongToe2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "longCurl" -ln "longCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999893 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKLongToe3_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "longCurl" -ln "longCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKMiddleToe1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999893 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999893 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe3_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKRingToe1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999893 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingToe2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999893 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingToe3_L_rotateZ1";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyToe1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999893 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 17.99999893 0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe3_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 1.9999998809999999 0 0 10 -10;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKLongToe1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -0.1999999881 0 0 10 1;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKBigToe1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -6.599999607 0 0 10 33;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingToe1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 6.599999607 0 0 10 -33;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 9.9999994040000004 0 0 10 -50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode multiplyDivide -n "AnkleFat";
createNode multiplyDivide -n "HeelFat";
createNode multiplyDivide -n "FootSideOuterFat";
createNode multiplyDivide -n "FootSideInnerFat";
createNode multiplyDivide -n "ToesFat";
createNode multiplyDivide -n "PinkyToe1Fat";
createNode multiplyDivide -n "PinkyToe2Fat";
createNode multiplyDivide -n "PinkyToe3Fat";
createNode multiplyDivide -n "PinkyToe4Fat";
createNode multiplyDivide -n "RingToe1Fat";
createNode multiplyDivide -n "RingToe2Fat";
createNode multiplyDivide -n "RingToe3Fat";
createNode multiplyDivide -n "RingToe4Fat";
createNode multiplyDivide -n "MiddleToe1Fat";
createNode multiplyDivide -n "MiddleToe2Fat";
createNode multiplyDivide -n "MiddleToe3Fat";
createNode multiplyDivide -n "MiddleToe4Fat";
createNode multiplyDivide -n "LongToe1Fat";
createNode multiplyDivide -n "LongToe2Fat";
createNode multiplyDivide -n "LongToe3Fat";
createNode multiplyDivide -n "LongToe4Fat";
createNode multiplyDivide -n "BigToe1Fat";
createNode multiplyDivide -n "BigToe2Fat";
createNode multiplyDivide -n "BigToe3Fat";
createNode lightLinker -s -n "lightLinker1";
	setAttr -s 2 ".lnk";
	setAttr -s 2 ".slnk";
createNode displayLayerManager -n "layerManager";

connectAttr "SDK2FKBigToe1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKBigToe2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKLongToe1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKLongToe2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKLongToe3_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKMiddleToe1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe3_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKRingToe1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingToe2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingToe3_R_rotateZ1.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyToe1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe3_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKLongToe1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKBigToe1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingToe1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKBigToe1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKBigToe2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKLongToe1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKLongToe2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKLongToe3_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKMiddleToe1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe3_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKRingToe1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingToe2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingToe3_L_rotateZ1.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyToe1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe3_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKLongToe1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKBigToe1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingToe1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "AnkleFat.oy" "Ankle.fatYabs";
connectAttr "AnkleFat.oz" "Ankle.fatZabs";
connectAttr "Ankle.s" "Heel.is";
connectAttr "HeelFat.oy" "Heel.fatYabs";
connectAttr "HeelFat.oz" "Heel.fatZabs";
connectAttr "Ankle.s" "FootSideOuter.is";
connectAttr "FootSideOuterFat.oy" "FootSideOuter.fatYabs";
connectAttr "FootSideOuterFat.oz" "FootSideOuter.fatZabs";
connectAttr "Ankle.s" "FootSideInner.is";
connectAttr "FootSideInnerFat.oy" "FootSideInner.fatYabs";
connectAttr "FootSideInnerFat.oz" "FootSideInner.fatZabs";
connectAttr "Ankle.s" "Toes.is";
connectAttr "ToesFat.oy" "Toes.fatYabs";
connectAttr "ToesFat.oz" "Toes.fatZabs";
connectAttr "Toes.s" "ToesEnd.is";
connectAttr "PinkyToe1Fat.oy" "PinkyToe1.fatYabs";
connectAttr "PinkyToe1Fat.oz" "PinkyToe1.fatZabs";
connectAttr "Toes.s" "PinkyToe1.is";
connectAttr "PinkyToe1.s" "PinkyToe2.is";
connectAttr "PinkyToe2Fat.oy" "PinkyToe2.fatYabs";
connectAttr "PinkyToe2Fat.oz" "PinkyToe2.fatZabs";
connectAttr "PinkyToe2.s" "PinkyToe3.is";
connectAttr "PinkyToe3Fat.oy" "PinkyToe3.fatYabs";
connectAttr "PinkyToe3Fat.oz" "PinkyToe3.fatZabs";
connectAttr "PinkyToe3.s" "PinkyToe4.is";
connectAttr "PinkyToe4Fat.oy" "PinkyToe4.fatYabs";
connectAttr "PinkyToe4Fat.oz" "PinkyToe4.fatZabs";
connectAttr "RingToe1Fat.oy" "RingToe1.fatYabs";
connectAttr "RingToe1Fat.oz" "RingToe1.fatZabs";
connectAttr "Toes.s" "RingToe1.is";
connectAttr "RingToe1.s" "RingToe2.is";
connectAttr "RingToe2Fat.oy" "RingToe2.fatYabs";
connectAttr "RingToe2Fat.oz" "RingToe2.fatZabs";
connectAttr "RingToe2.s" "RingToe3.is";
connectAttr "RingToe3Fat.oy" "RingToe3.fatYabs";
connectAttr "RingToe3Fat.oz" "RingToe3.fatZabs";
connectAttr "RingToe3.s" "RingToe4.is";
connectAttr "RingToe4Fat.oy" "RingToe4.fatYabs";
connectAttr "RingToe4Fat.oz" "RingToe4.fatZabs";
connectAttr "MiddleToe1Fat.oy" "MiddleToe1.fatYabs";
connectAttr "MiddleToe1Fat.oz" "MiddleToe1.fatZabs";
connectAttr "Toes.s" "MiddleToe1.is";
connectAttr "MiddleToe1.s" "MiddleToe2.is";
connectAttr "MiddleToe2Fat.oy" "MiddleToe2.fatYabs";
connectAttr "MiddleToe2Fat.oz" "MiddleToe2.fatZabs";
connectAttr "MiddleToe2.s" "MiddleToe3.is";
connectAttr "MiddleToe3Fat.oy" "MiddleToe3.fatYabs";
connectAttr "MiddleToe3Fat.oz" "MiddleToe3.fatZabs";
connectAttr "MiddleToe3.s" "MiddleToe4.is";
connectAttr "MiddleToe4Fat.oy" "MiddleToe4.fatYabs";
connectAttr "MiddleToe4Fat.oz" "MiddleToe4.fatZabs";
connectAttr "LongToe1Fat.oy" "LongToe1.fatYabs";
connectAttr "LongToe1Fat.oz" "LongToe1.fatZabs";
connectAttr "Toes.s" "LongToe1.is";
connectAttr "LongToe1.s" "LongToe2.is";
connectAttr "LongToe2Fat.oy" "LongToe2.fatYabs";
connectAttr "LongToe2Fat.oz" "LongToe2.fatZabs";
connectAttr "LongToe2.s" "LongToe3.is";
connectAttr "LongToe3Fat.oy" "LongToe3.fatYabs";
connectAttr "LongToe3Fat.oz" "LongToe3.fatZabs";
connectAttr "LongToe3.s" "LongToe4.is";
connectAttr "LongToe4Fat.oy" "LongToe4.fatYabs";
connectAttr "LongToe4Fat.oz" "LongToe4.fatZabs";
connectAttr "BigToe1Fat.oy" "BigToe1.fatYabs";
connectAttr "BigToe1Fat.oz" "BigToe1.fatZabs";
connectAttr "Toes.s" "BigToe1.is";
connectAttr "BigToe1.s" "BigToe2.is";
connectAttr "BigToe2Fat.oy" "BigToe2.fatYabs";
connectAttr "BigToe2Fat.oz" "BigToe2.fatZabs";
connectAttr "BigToe2.s" "BigToe3.is";
connectAttr "BigToe3Fat.oy" "BigToe3.fatYabs";
connectAttr "BigToe3Fat.oz" "BigToe3.fatZabs";
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[14]" "SDK2FKBigToe1_R_rotateZ.bigCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[15]" "SDK1FKBigToe2_R_rotateZ.bigCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[16]" "SDK2FKLongToe1_R_rotateZ.longCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[17]" "SDK1FKLongToe2_R_rotateZ.longCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[18]" "SDK1FKLongToe3_R_rotateZ.longCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[5]" "SDK2FKMiddleToe1_R_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[6]" "SDK1FKMiddleToe2_R_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[7]" "SDK1FKMiddleToe3_R_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[8]" "SDK2FKRingToe1_R_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[9]" "SDK1FKRingToe2_R_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[10]" "SDK1FKRingToe3_R_rotateZ1.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[11]" "SDK2FKPinkyToe1_R_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[12]" "SDK1FKPinkyToe2_R_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[13]" "SDK1FKPinkyToe3_R_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[0]" "SDK1FKMiddleToe1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[1]" "SDK1FKLongToe1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[2]" "SDK1FKBigToe1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[3]" "SDK1FKRingToe1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_R[4]" "SDK1FKPinkyToe1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[14]" "SDK2FKBigToe1_L_rotateZ.bigCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[15]" "SDK1FKBigToe2_L_rotateZ.bigCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[16]" "SDK2FKLongToe1_L_rotateZ.longCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[17]" "SDK1FKLongToe2_L_rotateZ.longCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[18]" "SDK1FKLongToe3_L_rotateZ.longCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[5]" "SDK2FKMiddleToe1_L_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[6]" "SDK1FKMiddleToe2_L_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[7]" "SDK1FKMiddleToe3_L_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[8]" "SDK2FKRingToe1_L_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[9]" "SDK1FKRingToe2_L_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[10]" "SDK1FKRingToe3_L_rotateZ1.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[11]" "SDK2FKPinkyToe1_L_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[12]" "SDK1FKPinkyToe2_L_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[13]" "SDK1FKPinkyToe3_L_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[0]" "SDK1FKMiddleToe1_L_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[1]" "SDK1FKLongToe1_L_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[2]" "SDK1FKBigToe1_L_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[3]" "SDK1FKRingToe1_L_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_ToeCurl_L[4]" "SDK1FKPinkyToe1_L_rotateY.spread"
		;
connectAttr "Ankle.fat" "AnkleFat.i1y";
connectAttr "Ankle.fat" "AnkleFat.i1z";
connectAttr "Ankle.fatY" "AnkleFat.i2y";
connectAttr "Ankle.fatZ" "AnkleFat.i2z";
connectAttr "Heel.fat" "HeelFat.i1y";
connectAttr "Heel.fat" "HeelFat.i1z";
connectAttr "Heel.fatY" "HeelFat.i2y";
connectAttr "Heel.fatZ" "HeelFat.i2z";
connectAttr "FootSideOuter.fat" "FootSideOuterFat.i1y";
connectAttr "FootSideOuter.fat" "FootSideOuterFat.i1z";
connectAttr "FootSideOuter.fatY" "FootSideOuterFat.i2y";
connectAttr "FootSideOuter.fatZ" "FootSideOuterFat.i2z";
connectAttr "FootSideInner.fat" "FootSideInnerFat.i1y";
connectAttr "FootSideInner.fat" "FootSideInnerFat.i1z";
connectAttr "FootSideInner.fatY" "FootSideInnerFat.i2y";
connectAttr "FootSideInner.fatZ" "FootSideInnerFat.i2z";
connectAttr "Toes.fat" "ToesFat.i1y";
connectAttr "Toes.fat" "ToesFat.i1z";
connectAttr "Toes.fatY" "ToesFat.i2y";
connectAttr "Toes.fatZ" "ToesFat.i2z";
connectAttr "PinkyToe1.fat" "PinkyToe1Fat.i1y";
connectAttr "PinkyToe1.fat" "PinkyToe1Fat.i1z";
connectAttr "PinkyToe1.fatY" "PinkyToe1Fat.i2y";
connectAttr "PinkyToe1.fatZ" "PinkyToe1Fat.i2z";
connectAttr "PinkyToe2.fat" "PinkyToe2Fat.i1y";
connectAttr "PinkyToe2.fat" "PinkyToe2Fat.i1z";
connectAttr "PinkyToe2.fatY" "PinkyToe2Fat.i2y";
connectAttr "PinkyToe2.fatZ" "PinkyToe2Fat.i2z";
connectAttr "PinkyToe3.fat" "PinkyToe3Fat.i1y";
connectAttr "PinkyToe3.fat" "PinkyToe3Fat.i1z";
connectAttr "PinkyToe3.fatY" "PinkyToe3Fat.i2y";
connectAttr "PinkyToe3.fatZ" "PinkyToe3Fat.i2z";
connectAttr "PinkyToe4.fat" "PinkyToe4Fat.i1y";
connectAttr "PinkyToe4.fat" "PinkyToe4Fat.i1z";
connectAttr "PinkyToe4.fatY" "PinkyToe4Fat.i2y";
connectAttr "PinkyToe4.fatZ" "PinkyToe4Fat.i2z";
connectAttr "RingToe1.fat" "RingToe1Fat.i1y";
connectAttr "RingToe1.fat" "RingToe1Fat.i1z";
connectAttr "RingToe1.fatY" "RingToe1Fat.i2y";
connectAttr "RingToe1.fatZ" "RingToe1Fat.i2z";
connectAttr "RingToe2.fat" "RingToe2Fat.i1y";
connectAttr "RingToe2.fat" "RingToe2Fat.i1z";
connectAttr "RingToe2.fatY" "RingToe2Fat.i2y";
connectAttr "RingToe2.fatZ" "RingToe2Fat.i2z";
connectAttr "RingToe3.fat" "RingToe3Fat.i1y";
connectAttr "RingToe3.fat" "RingToe3Fat.i1z";
connectAttr "RingToe3.fatY" "RingToe3Fat.i2y";
connectAttr "RingToe3.fatZ" "RingToe3Fat.i2z";
connectAttr "RingToe4.fat" "RingToe4Fat.i1y";
connectAttr "RingToe4.fat" "RingToe4Fat.i1z";
connectAttr "RingToe4.fatY" "RingToe4Fat.i2y";
connectAttr "RingToe4.fatZ" "RingToe4Fat.i2z";
connectAttr "MiddleToe1.fat" "MiddleToe1Fat.i1y";
connectAttr "MiddleToe1.fat" "MiddleToe1Fat.i1z";
connectAttr "MiddleToe1.fatY" "MiddleToe1Fat.i2y";
connectAttr "MiddleToe1.fatZ" "MiddleToe1Fat.i2z";
connectAttr "MiddleToe2.fat" "MiddleToe2Fat.i1y";
connectAttr "MiddleToe2.fat" "MiddleToe2Fat.i1z";
connectAttr "MiddleToe2.fatY" "MiddleToe2Fat.i2y";
connectAttr "MiddleToe2.fatZ" "MiddleToe2Fat.i2z";
connectAttr "MiddleToe3.fat" "MiddleToe3Fat.i1y";
connectAttr "MiddleToe3.fat" "MiddleToe3Fat.i1z";
connectAttr "MiddleToe3.fatY" "MiddleToe3Fat.i2y";
connectAttr "MiddleToe3.fatZ" "MiddleToe3Fat.i2z";
connectAttr "MiddleToe4.fat" "MiddleToe4Fat.i1y";
connectAttr "MiddleToe4.fat" "MiddleToe4Fat.i1z";
connectAttr "MiddleToe4.fatY" "MiddleToe4Fat.i2y";
connectAttr "MiddleToe4.fatZ" "MiddleToe4Fat.i2z";
connectAttr "LongToe1.fat" "LongToe1Fat.i1y";
connectAttr "LongToe1.fat" "LongToe1Fat.i1z";
connectAttr "LongToe1.fatY" "LongToe1Fat.i2y";
connectAttr "LongToe1.fatZ" "LongToe1Fat.i2z";
connectAttr "LongToe2.fat" "LongToe2Fat.i1y";
connectAttr "LongToe2.fat" "LongToe2Fat.i1z";
connectAttr "LongToe2.fatY" "LongToe2Fat.i2y";
connectAttr "LongToe2.fatZ" "LongToe2Fat.i2z";
connectAttr "LongToe3.fat" "LongToe3Fat.i1y";
connectAttr "LongToe3.fat" "LongToe3Fat.i1z";
connectAttr "LongToe3.fatY" "LongToe3Fat.i2y";
connectAttr "LongToe3.fatZ" "LongToe3Fat.i2z";
connectAttr "LongToe4.fat" "LongToe4Fat.i1y";
connectAttr "LongToe4.fat" "LongToe4Fat.i1z";
connectAttr "LongToe4.fatY" "LongToe4Fat.i2y";
connectAttr "LongToe4.fatZ" "LongToe4Fat.i2z";
connectAttr "BigToe1.fat" "BigToe1Fat.i1y";
connectAttr "BigToe1.fat" "BigToe1Fat.i1z";
connectAttr "BigToe1.fatY" "BigToe1Fat.i2y";
connectAttr "BigToe1.fatZ" "BigToe1Fat.i2z";
connectAttr "BigToe2.fat" "BigToe2Fat.i1y";
connectAttr "BigToe2.fat" "BigToe2Fat.i1z";
connectAttr "BigToe2.fatY" "BigToe2Fat.i2y";
connectAttr "BigToe2.fatZ" "BigToe2Fat.i2z";
connectAttr "BigToe3.fat" "BigToe3Fat.i1y";
connectAttr "BigToe3.fat" "BigToe3Fat.i1z";
connectAttr "BigToe3.fatY" "BigToe3Fat.i2y";
connectAttr "BigToe3.fatZ" "BigToe3Fat.i2z";
relationship "link" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
connectAttr "layerManager.dli[0]" "defaultLayer.id";
connectAttr "defaultRenderLayer.msg" ":defaultRenderingList1.r" -na;
// End of foot.ma
