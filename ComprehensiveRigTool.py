"""
Comprehensive Maya Rigging Tool
Based on 4SchoolV1 rigging system
Supports FK/IK switching for arms and legs
Author: <PERSON> Rigging Tool
"""

import maya.cmds as mc

class RigUtilities:
    """Utility functions for rigging operations"""
    
    @staticmethod
    def create_controller_shape(name, shape_type="circle", size=1.0, normal=(1,0,0)):
        """Create different controller shapes"""
        if shape_type == "circle":
            ctrl = mc.circle(n=name, r=size, nr=normal)[0]
        elif shape_type == "square":
            ctrl = mc.curve(n=name, d=1, p=[
                (-size, 0, -size), (-size, 0, size), (size, 0, size), 
                (size, 0, -size), (-size, 0, -size)
            ])
        elif shape_type == "arrow":
            ctrl = mc.curve(n=name, d=1, p=[
                (-1*size, 0, -1*size), (-1*size, 0, -3*size), (-2*size, 0, -3*size),
                (0, 0, -5*size), (2*size, 0, -3*size), (1*size, 0, -3*size),
                (1*size, 0, -1*size), (3*size, 0, -1*size), (3*size, 0, -2*size),
                (5*size, 0, 0), (3*size, 0, 2*size), (3*size, 0, 1*size),
                (1*size, 0, 1*size), (1*size, 0, 3*size), (2*size, 0, 3*size),
                (0, 0, 5*size), (-2*size, 0, 3*size), (-1*size, 0, 3*size),
                (-1*size, 0, 1*size), (-3*size, 0, 1*size), (-3*size, 0, 2*size),
                (-5*size, 0, 0), (-3*size, 0, -2*size), (-3*size, 0, -1*size),
                (-1*size, 0, -1*size)
            ])
        elif shape_type == "cube":
            ctrl = mc.curve(n=name, d=1, p=[
                (-0.5*size, 0.5*size, 0.5*size), (0.5*size, 0.5*size, 0.5*size),
                (0.5*size, 0.5*size, -0.5*size), (-0.5*size, 0.5*size, -0.5*size),
                (-0.5*size, 0.5*size, 0.5*size), (-0.5*size, -0.5*size, 0.5*size),
                (-0.5*size, -0.5*size, -0.5*size), (-0.5*size, 0.5*size, -0.5*size),
                (0.5*size, 0.5*size, -0.5*size), (0.5*size, -0.5*size, -0.5*size),
                (-0.5*size, -0.5*size, -0.5*size), (-0.5*size, -0.5*size, 0.5*size),
                (0.5*size, -0.5*size, 0.5*size), (0.5*size, 0.5*size, 0.5*size),
                (0.5*size, -0.5*size, 0.5*size), (0.5*size, -0.5*size, -0.5*size)
            ])
        else:
            ctrl = mc.circle(n=name, r=size, nr=normal)[0]
        
        return ctrl
    
    @staticmethod
    def lock_and_hide_attributes(obj, attrs):
        """Lock and hide specified attributes"""
        for attr in attrs:
            mc.setAttr(f"{obj}.{attr}", l=True, k=False, cb=False)
    
    @staticmethod
    def create_group_above(obj, suffix="_grp"):
        """Create a group above the object"""
        grp_name = obj + suffix
        grp = mc.group(obj, n=grp_name)
        return grp
    
    @staticmethod
    def match_transform_to_joint(obj, joint):
        """Match object transform to joint"""
        mc.matchTransform(obj, joint)

class FKIKSwitcher:
    """Handles FK/IK switching functionality"""
    
    def __init__(self, limb_name, orig_joints):
        self.limb_name = limb_name
        self.orig_joints = orig_joints
        self.fk_joints = []
        self.ik_joints = []
        self.switch_ctrl = None
        
    def create_duplicate_chains(self):
        """Create FK and IK joint chains"""
        # Create FK joints
        self.fk_joints = mc.duplicate(self.orig_joints, po=True, f=True)
        for i, joint in enumerate(self.orig_joints):
            fk_name = joint.replace("jnt", "jnt_drv_fk")
            mc.rename(self.fk_joints[i], fk_name)
            self.fk_joints[i] = fk_name
        
        # Create IK joints
        self.ik_joints = mc.duplicate(self.orig_joints, po=True, f=True)
        for i, joint in enumerate(self.orig_joints):
            ik_name = joint.replace("jnt", "jnt_drv_ik")
            mc.rename(self.ik_joints[i], ik_name)
            self.ik_joints[i] = ik_name
    
    def create_switch_control(self):
        """Create the FK/IK switch controller"""
        switch_name = f"{self.limb_name}_ikfkBlend_ctrl"
        self.switch_ctrl = RigUtilities.create_controller_shape(
            switch_name, "square", 2.0
        )
        
        # Add switch attribute
        mc.addAttr(self.switch_ctrl, ln="ikfkBlend", min=0, max=1, k=True)
        
        # Lock unnecessary attributes
        lock_attrs = ["translateX", "translateY", "translateZ", 
                     "rotateX", "rotateY", "rotateZ",
                     "scaleX", "scaleY", "scaleZ", "visibility"]
        RigUtilities.lock_and_hide_attributes(self.switch_ctrl, lock_attrs)
        
        # Position the control
        if self.orig_joints:
            mc.matchTransform(self.switch_ctrl, self.orig_joints[-1])
            mc.move(0, 0, 10, self.switch_ctrl, r=True)
    
    def create_blend_setup(self):
        """Create blend color nodes for FK/IK switching"""
        for i, orig_joint in enumerate(self.orig_joints):
            blend_name = orig_joint.replace("jnt", "blendColors")
            blend_node = mc.createNode("blendColors", n=blend_name)
            
            # Connect IK to color1, FK to color2
            mc.connectAttr(f"{self.ik_joints[i]}.rotate", f"{blend_node}.color1")
            mc.connectAttr(f"{self.fk_joints[i]}.rotate", f"{blend_node}.color2")
            mc.connectAttr(f"{blend_node}.output", f"{orig_joint}.rotate")
            mc.connectAttr(f"{self.switch_ctrl}.ikfkBlend", f"{blend_node}.blender")

class LimbRigger:
    """Handles rigging of limbs (arms and legs)"""
    
    def __init__(self, limb_name, joints, limb_type="arm"):
        self.limb_name = limb_name
        self.joints = joints
        self.limb_type = limb_type
        self.fk_ik_switcher = None
        self.fk_controllers = []
        self.ik_handle = None
        self.ik_controller = None
        self.pole_vector_ctrl = None
    
    def create_fk_ik_system(self):
        """Create the complete FK/IK system"""
        if len(self.joints) < 2:
            mc.warning(f"Need at least 2 joints for {self.limb_name}")
            return
        
        # Create FK/IK switcher
        self.fk_ik_switcher = FKIKSwitcher(self.limb_name, self.joints)
        self.fk_ik_switcher.create_duplicate_chains()
        self.fk_ik_switcher.create_switch_control()
        self.fk_ik_switcher.create_blend_setup()
        
        # Create FK controllers
        self.create_fk_controllers()
        
        # Create IK system
        self.create_ik_system()
    
    def create_fk_controllers(self):
        """Create FK controllers for the limb"""
        for i, fk_joint in enumerate(self.fk_ik_switcher.fk_joints):
            ctrl_name = fk_joint.replace("jnt_drv_fk", "ac_fk")
            ctrl = RigUtilities.create_controller_shape(ctrl_name, "circle", 3.0)
            
            # Create group and match transform
            grp = RigUtilities.create_group_above(ctrl)
            mc.matchTransform(grp, fk_joint)
            
            # Create constraint
            mc.orientConstraint(ctrl, fk_joint)
            
            self.fk_controllers.append(ctrl)
    
    def create_ik_system(self):
        """Create IK system for the limb"""
        if len(self.fk_ik_switcher.ik_joints) < 2:
            return
        
        # Create IK handle
        ik_start = self.fk_ik_switcher.ik_joints[-1]  # Start joint
        ik_end = self.fk_ik_switcher.ik_joints[0]     # End joint
        
        ik_handle_name = f"{self.limb_name}_ikHandle"
        self.ik_handle = mc.ikHandle(
            n=ik_handle_name, 
            sj=ik_start, 
            ee=ik_end, 
            sol="ikRPsolver"
        )[0]
        
        # Create IK controller
        ik_ctrl_name = f"{self.limb_name}_ik_ctrl"
        self.ik_controller = RigUtilities.create_controller_shape(
            ik_ctrl_name, "arrow", 4.0
        )
        
        # Position and constrain IK controller
        ik_grp = RigUtilities.create_group_above(self.ik_controller)
        mc.matchTransform(ik_grp, ik_end)
        mc.parentConstraint(self.ik_controller, self.ik_handle)
        
        # Create pole vector controller if we have 3+ joints
        if len(self.fk_ik_switcher.ik_joints) >= 3:
            self.create_pole_vector_control()
    
    def create_pole_vector_control(self):
        """Create pole vector control for IK"""
        if len(self.fk_ik_switcher.ik_joints) < 3:
            return
        
        pv_ctrl_name = f"{self.limb_name}_pv_ctrl"
        self.pole_vector_ctrl = RigUtilities.create_controller_shape(
            pv_ctrl_name, "cube", 2.0
        )
        
        # Position pole vector control
        middle_joint = self.fk_ik_switcher.ik_joints[1]
        pv_grp = RigUtilities.create_group_above(self.pole_vector_ctrl)
        
        # Get middle joint position and offset it
        pos = mc.xform(middle_joint, q=True, t=True, ws=True)
        mc.setAttr(f"{pv_grp}.translateX", pos[0])
        mc.setAttr(f"{pv_grp}.translateY", pos[1])
        mc.setAttr(f"{pv_grp}.translateZ", pos[2] + 20)  # Offset in Z
        
        # Create pole vector constraint
        mc.poleVectorConstraint(self.pole_vector_ctrl, self.ik_handle)

class SpineRigger:
    """Handles spine rigging"""
    
    def __init__(self, spine_joints):
        self.spine_joints = spine_joints
        self.controllers = []
    
    def create_spine_rig(self):
        """Create spine rig with FK controllers"""
        for joint in self.spine_joints:
            ctrl_name = joint.replace("jnt", "ac_spine")
            ctrl = RigUtilities.create_controller_shape(ctrl_name, "circle", 5.0)
            
            grp = RigUtilities.create_group_above(ctrl)
            mc.matchTransform(grp, joint)
            mc.orientConstraint(ctrl, joint)
            
            self.controllers.append(ctrl)

class ComprehensiveRigBuilder:
    """Main class that builds the complete rig system"""
    
    def __init__(self):
        self.bone_selections = {
            'left_arm': [],
            'right_arm': [],
            'left_leg': [],
            'right_leg': [],
            'spine': [],
            'hips': [],
            'chest': [],
            'left_clavicle': [],
            'right_clavicle': []
        }
        
        self.riggers = {}
    
    def set_bone_selection(self, category, joints):
        """Set bone selection for a category"""
        if category in self.bone_selections:
            self.bone_selections[category] = joints
        else:
            mc.warning(f"Unknown category: {category}")
    
    def build_complete_rig(self):
        """Build the complete rig system"""
        # Create limb rigs
        if self.bone_selections['left_arm']:
            left_arm_rigger = LimbRigger("left_arm", self.bone_selections['left_arm'], "arm")
            left_arm_rigger.create_fk_ik_system()
            self.riggers['left_arm'] = left_arm_rigger
        
        if self.bone_selections['right_arm']:
            right_arm_rigger = LimbRigger("right_arm", self.bone_selections['right_arm'], "arm")
            right_arm_rigger.create_fk_ik_system()
            self.riggers['right_arm'] = right_arm_rigger
        
        if self.bone_selections['left_leg']:
            left_leg_rigger = LimbRigger("left_leg", self.bone_selections['left_leg'], "leg")
            left_leg_rigger.create_fk_ik_system()
            self.riggers['left_leg'] = left_leg_rigger
        
        if self.bone_selections['right_leg']:
            right_leg_rigger = LimbRigger("right_leg", self.bone_selections['right_leg'], "leg")
            right_leg_rigger.create_fk_ik_system()
            self.riggers['right_leg'] = right_leg_rigger
        
        # Create spine rig
        if self.bone_selections['spine']:
            spine_rigger = SpineRigger(self.bone_selections['spine'])
            spine_rigger.create_spine_rig()
            self.riggers['spine'] = spine_rigger
        
        # Create simple FK controllers for other parts
        self.create_simple_controllers('hips')
        self.create_simple_controllers('chest')
        self.create_simple_controllers('left_clavicle')
        self.create_simple_controllers('right_clavicle')
        
        mc.confirmDialog(title="Rig Complete", message="Comprehensive rig system created successfully!")
    
    def create_simple_controllers(self, category):
        """Create simple FK controllers for specified category"""
        joints = self.bone_selections.get(category, [])
        controllers = []
        
        for joint in joints:
            ctrl_name = joint.replace("jnt", f"ac_{category}")
            ctrl = RigUtilities.create_controller_shape(ctrl_name, "circle", 4.0)
            
            grp = RigUtilities.create_group_above(ctrl)
            mc.matchTransform(grp, joint)
            mc.orientConstraint(ctrl, joint)
            
            controllers.append(ctrl)
        
        if controllers:
            self.riggers[category] = controllers
