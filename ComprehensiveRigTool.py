"""
Comprehensive Maya Rigging Tool
Based on 4SchoolV1 rigging system
Supports FK/IK switching for arms and legs
Author: <PERSON> Rigging Tool
"""

import maya.cmds as mc

class RigUtilities:
    """Utility functions for rigging operations"""

    @staticmethod
    def validate_joint_chain(joints):
        """Validate that joints form a proper chain"""
        if not joints:
            return False, "No joints provided"

        if len(joints) < 2:
            return False, "Need at least 2 joints for rigging"

        # Check if all objects exist and are joints
        for joint in joints:
            if not mc.objExists(joint):
                return False, f"Joint {joint} does not exist"
            if mc.nodeType(joint) != 'joint':
                return False, f"{joint} is not a joint"

        return True, "Valid joint chain"

    @staticmethod
    def get_joint_chain_from_selection():
        """Get a proper joint chain from current selection"""
        selected = mc.ls(sl=True, type='joint')
        if not selected:
            return []

        # Sort joints by hierarchy if possible
        try:
            # Try to sort by hierarchy depth
            joint_depths = []
            for joint in selected:
                parents = mc.listRelatives(joint, ap=True, f=True) or []
                depth = len([p for p in parents if mc.nodeType(p) == 'joint'])
                joint_depths.append((depth, joint))

            # Sort by depth (root first)
            joint_depths.sort(reverse=True)
            return [joint for depth, joint in joint_depths]
        except:
            return selected

    @staticmethod
    def create_controller_shape(name, shape_type="circle", size=1.0, normal=(1,0,0)):
        """Create different controller shapes"""
        if shape_type == "circle":
            ctrl = mc.circle(n=name, r=size, nr=normal)[0]
        elif shape_type == "square":
            ctrl = mc.curve(n=name, d=1, p=[
                (-size, 0, -size), (-size, 0, size), (size, 0, size), 
                (size, 0, -size), (-size, 0, -size)
            ])
        elif shape_type == "arrow":
            ctrl = mc.curve(n=name, d=1, p=[
                (-1*size, 0, -1*size), (-1*size, 0, -3*size), (-2*size, 0, -3*size),
                (0, 0, -5*size), (2*size, 0, -3*size), (1*size, 0, -3*size),
                (1*size, 0, -1*size), (3*size, 0, -1*size), (3*size, 0, -2*size),
                (5*size, 0, 0), (3*size, 0, 2*size), (3*size, 0, 1*size),
                (1*size, 0, 1*size), (1*size, 0, 3*size), (2*size, 0, 3*size),
                (0, 0, 5*size), (-2*size, 0, 3*size), (-1*size, 0, 3*size),
                (-1*size, 0, 1*size), (-3*size, 0, 1*size), (-3*size, 0, 2*size),
                (-5*size, 0, 0), (-3*size, 0, -2*size), (-3*size, 0, -1*size),
                (-1*size, 0, -1*size)
            ])
        elif shape_type == "cube":
            ctrl = mc.curve(n=name, d=1, p=[
                (-0.5*size, 0.5*size, 0.5*size), (0.5*size, 0.5*size, 0.5*size),
                (0.5*size, 0.5*size, -0.5*size), (-0.5*size, 0.5*size, -0.5*size),
                (-0.5*size, 0.5*size, 0.5*size), (-0.5*size, -0.5*size, 0.5*size),
                (-0.5*size, -0.5*size, -0.5*size), (-0.5*size, 0.5*size, -0.5*size),
                (0.5*size, 0.5*size, -0.5*size), (0.5*size, -0.5*size, -0.5*size),
                (-0.5*size, -0.5*size, -0.5*size), (-0.5*size, -0.5*size, 0.5*size),
                (0.5*size, -0.5*size, 0.5*size), (0.5*size, 0.5*size, 0.5*size),
                (0.5*size, -0.5*size, 0.5*size), (0.5*size, -0.5*size, -0.5*size)
            ])
        else:
            ctrl = mc.circle(n=name, r=size, nr=normal)[0]
        
        return ctrl
    
    @staticmethod
    def lock_and_hide_attributes(obj, attrs):
        """Lock and hide specified attributes"""
        for attr in attrs:
            mc.setAttr(f"{obj}.{attr}", l=True, k=False, cb=False)
    
    @staticmethod
    def create_group_above(obj, suffix="_grp"):
        """Create a group above the object"""
        grp_name = obj + suffix
        grp = mc.group(obj, n=grp_name)
        return grp
    
    @staticmethod
    def match_transform_to_joint(obj, joint):
        """Match object transform to joint"""
        mc.matchTransform(obj, joint)

class FKIKSwitcher:
    """Handles FK/IK switching functionality"""
    
    def __init__(self, limb_name, orig_joints):
        self.limb_name = limb_name
        self.orig_joints = orig_joints
        self.fk_joints = []
        self.ik_joints = []
        self.switch_ctrl = None
        
    def create_duplicate_chains(self):
        """Create FK and IK joint chains"""
        # Create FK joints
        self.fk_joints = mc.duplicate(self.orig_joints, po=True, f=True)
        for i, joint in enumerate(self.orig_joints):
            fk_name = joint.replace("jnt", "jnt_drv_fk")
            mc.rename(self.fk_joints[i], fk_name)
            self.fk_joints[i] = fk_name
        
        # Create IK joints
        self.ik_joints = mc.duplicate(self.orig_joints, po=True, f=True)
        for i, joint in enumerate(self.orig_joints):
            ik_name = joint.replace("jnt", "jnt_drv_ik")
            mc.rename(self.ik_joints[i], ik_name)
            self.ik_joints[i] = ik_name
    
    def create_switch_control(self):
        """Create the FK/IK switch controller"""
        switch_name = f"{self.limb_name}_ikfkBlend_ctrl"
        self.switch_ctrl = RigUtilities.create_controller_shape(
            switch_name, "square", 2.0
        )
        
        # Add switch attribute
        mc.addAttr(self.switch_ctrl, ln="ikfkBlend", min=0, max=1, k=True)
        
        # Lock unnecessary attributes
        lock_attrs = ["translateX", "translateY", "translateZ", 
                     "rotateX", "rotateY", "rotateZ",
                     "scaleX", "scaleY", "scaleZ", "visibility"]
        RigUtilities.lock_and_hide_attributes(self.switch_ctrl, lock_attrs)
        
        # Position the control
        if self.orig_joints:
            mc.matchTransform(self.switch_ctrl, self.orig_joints[-1])
            mc.move(0, 0, 10, self.switch_ctrl, r=True)
    
    def create_blend_setup(self):
        """Create blend color nodes for FK/IK switching"""
        for i, orig_joint in enumerate(self.orig_joints):
            blend_name = orig_joint.replace("jnt", "blendColors")
            blend_node = mc.createNode("blendColors", n=blend_name)
            
            # Connect IK to color1, FK to color2
            mc.connectAttr(f"{self.ik_joints[i]}.rotate", f"{blend_node}.color1")
            mc.connectAttr(f"{self.fk_joints[i]}.rotate", f"{blend_node}.color2")
            mc.connectAttr(f"{blend_node}.output", f"{orig_joint}.rotate")
            mc.connectAttr(f"{self.switch_ctrl}.ikfkBlend", f"{blend_node}.blender")

class LimbRigger:
    """Handles rigging of limbs (arms and legs)"""
    
    def __init__(self, limb_name, joints, limb_type="arm"):
        self.limb_name = limb_name
        self.joints = joints
        self.limb_type = limb_type
        self.fk_ik_switcher = None
        self.fk_controllers = []
        self.ik_handle = None
        self.ik_controller = None
        self.pole_vector_ctrl = None
    
    def create_fk_ik_system(self):
        """Create the complete FK/IK system"""
        # Validate joints first
        is_valid, message = RigUtilities.validate_joint_chain(self.joints)
        if not is_valid:
            mc.warning(f"Cannot create {self.limb_name} rig: {message}")
            return False

        try:
            # Create FK/IK switcher
            self.fk_ik_switcher = FKIKSwitcher(self.limb_name, self.joints)
            self.fk_ik_switcher.create_duplicate_chains()
            self.fk_ik_switcher.create_switch_control()
            self.fk_ik_switcher.create_blend_setup()

            # Create FK controllers
            self.create_fk_controllers()

            # Create IK system
            self.create_ik_system()

            print(f"Successfully created {self.limb_name} FK/IK rig")
            return True

        except Exception as e:
            mc.warning(f"Error creating {self.limb_name} rig: {str(e)}")
            return False
    
    def create_fk_controllers(self):
        """Create FK controllers for the limb"""
        for i, fk_joint in enumerate(self.fk_ik_switcher.fk_joints):
            ctrl_name = fk_joint.replace("jnt_drv_fk", "ac_fk")
            ctrl = RigUtilities.create_controller_shape(ctrl_name, "circle", 3.0)
            
            # Create group and match transform
            grp = RigUtilities.create_group_above(ctrl)
            mc.matchTransform(grp, fk_joint)
            
            # Create constraint
            mc.orientConstraint(ctrl, fk_joint)
            
            self.fk_controllers.append(ctrl)
    
    def create_ik_system(self):
        """Create IK system for the limb"""
        if len(self.fk_ik_switcher.ik_joints) < 2:
            return
        
        # Create IK handle
        ik_start = self.fk_ik_switcher.ik_joints[-1]  # Start joint
        ik_end = self.fk_ik_switcher.ik_joints[0]     # End joint
        
        ik_handle_name = f"{self.limb_name}_ikHandle"
        self.ik_handle = mc.ikHandle(
            n=ik_handle_name, 
            sj=ik_start, 
            ee=ik_end, 
            sol="ikRPsolver"
        )[0]
        
        # Create IK controller
        ik_ctrl_name = f"{self.limb_name}_ik_ctrl"
        self.ik_controller = RigUtilities.create_controller_shape(
            ik_ctrl_name, "arrow", 4.0
        )
        
        # Position and constrain IK controller
        ik_grp = RigUtilities.create_group_above(self.ik_controller)
        mc.matchTransform(ik_grp, ik_end)
        mc.parentConstraint(self.ik_controller, self.ik_handle)
        
        # Create pole vector controller if we have 3+ joints
        if len(self.fk_ik_switcher.ik_joints) >= 3:
            self.create_pole_vector_control()
    
    def create_pole_vector_control(self):
        """Create pole vector control for IK"""
        if len(self.fk_ik_switcher.ik_joints) < 3:
            return
        
        pv_ctrl_name = f"{self.limb_name}_pv_ctrl"
        self.pole_vector_ctrl = RigUtilities.create_controller_shape(
            pv_ctrl_name, "cube", 2.0
        )
        
        # Position pole vector control
        middle_joint = self.fk_ik_switcher.ik_joints[1]
        pv_grp = RigUtilities.create_group_above(self.pole_vector_ctrl)
        
        # Get middle joint position and offset it
        pos = mc.xform(middle_joint, q=True, t=True, ws=True)
        mc.setAttr(f"{pv_grp}.translateX", pos[0])
        mc.setAttr(f"{pv_grp}.translateY", pos[1])
        mc.setAttr(f"{pv_grp}.translateZ", pos[2] + 20)  # Offset in Z
        
        # Create pole vector constraint
        mc.poleVectorConstraint(self.pole_vector_ctrl, self.ik_handle)

class SpineRigger:
    """Handles spine rigging"""
    
    def __init__(self, spine_joints):
        self.spine_joints = spine_joints
        self.controllers = []
    
    def create_spine_rig(self):
        """Create spine rig with FK controllers"""
        for joint in self.spine_joints:
            ctrl_name = joint.replace("jnt", "ac_spine")
            ctrl = RigUtilities.create_controller_shape(ctrl_name, "circle", 5.0)
            
            grp = RigUtilities.create_group_above(ctrl)
            mc.matchTransform(grp, joint)
            mc.orientConstraint(ctrl, joint)
            
            self.controllers.append(ctrl)

class ComprehensiveRigBuilder:
    """Main class that builds the complete rig system"""
    
    def __init__(self):
        self.bone_selections = {
            'left_arm': [],
            'right_arm': [],
            'left_leg': [],
            'right_leg': [],
            'spine': [],
            'hips': [],
            'chest': [],
            'left_clavicle': [],
            'right_clavicle': []
        }
        
        self.riggers = {}
    
    def set_bone_selection(self, category, joints):
        """Set bone selection for a category"""
        if category in self.bone_selections:
            self.bone_selections[category] = joints
        else:
            mc.warning(f"Unknown category: {category}")
    
    def build_complete_rig(self):
        """Build the complete rig system"""
        success_count = 0
        total_count = 0
        errors = []

        # Create limb rigs with error handling
        limb_configs = [
            ('left_arm', 'arm'),
            ('right_arm', 'arm'),
            ('left_leg', 'leg'),
            ('right_leg', 'leg')
        ]

        for limb_name, limb_type in limb_configs:
            if self.bone_selections[limb_name]:
                total_count += 1
                try:
                    rigger = LimbRigger(limb_name, self.bone_selections[limb_name], limb_type)
                    if rigger.create_fk_ik_system():
                        self.riggers[limb_name] = rigger
                        success_count += 1
                    else:
                        errors.append(f"Failed to create {limb_name} rig")
                except Exception as e:
                    errors.append(f"Error creating {limb_name}: {str(e)}")

        # Create spine rig
        if self.bone_selections['spine']:
            total_count += 1
            try:
                spine_rigger = SpineRigger(self.bone_selections['spine'])
                spine_rigger.create_spine_rig()
                self.riggers['spine'] = spine_rigger
                success_count += 1
            except Exception as e:
                errors.append(f"Error creating spine rig: {str(e)}")

        # Create simple FK controllers for other parts
        simple_parts = ['hips', 'chest', 'left_clavicle', 'right_clavicle']
        for part in simple_parts:
            if self.bone_selections[part]:
                total_count += 1
                try:
                    self.create_simple_controllers(part)
                    success_count += 1
                except Exception as e:
                    errors.append(f"Error creating {part} controllers: {str(e)}")

        # Create master control group
        self.create_master_control()

        # Report results
        if errors:
            error_msg = f"Rig completed with {success_count}/{total_count} successful.\n\nErrors:\n" + "\n".join(errors)
            mc.confirmDialog(title="Rig Complete with Errors", message=error_msg)
        else:
            mc.confirmDialog(title="Rig Complete",
                           message=f"Comprehensive rig system created successfully!\n{success_count} components built.")

    def create_master_control(self):
        """Create a master control for the entire rig"""
        try:
            master_ctrl = RigUtilities.create_controller_shape("master_ctrl", "square", 10.0)

            # Color the master control
            mc.setAttr(f"{master_ctrl}.overrideEnabled", 1)
            mc.setAttr(f"{master_ctrl}.overrideColor", 17)  # Yellow

            # Group all rig components under master
            rig_group = mc.group(em=True, n="RIG_GRP")
            mc.parent(master_ctrl, rig_group)

            print("Master control created successfully")

        except Exception as e:
            print(f"Warning: Could not create master control: {str(e)}")
    
    def create_simple_controllers(self, category):
        """Create simple FK controllers for specified category"""
        joints = self.bone_selections.get(category, [])
        controllers = []
        
        for joint in joints:
            ctrl_name = joint.replace("jnt", f"ac_{category}")
            ctrl = RigUtilities.create_controller_shape(ctrl_name, "circle", 4.0)
            
            grp = RigUtilities.create_group_above(ctrl)
            mc.matchTransform(grp, joint)
            mc.orientConstraint(ctrl, joint)
            
            controllers.append(ctrl)
        
        if controllers:
            self.riggers[category] = controllers


class ComprehensiveRigUI:
    """UI for the comprehensive rigging tool"""

    def __init__(self):
        self.window_id = "ComprehensiveRigTool_UI"
        self.rig_builder = ComprehensiveRigBuilder()
        self.bone_selection_fields = {}

    def create_ui(self):
        """Create the main UI window"""
        # Delete existing window if it exists
        if mc.window(self.window_id, q=True, exists=True):
            mc.deleteUI(self.window_id)

        # Create main window
        self.window = mc.window(
            self.window_id,
            title="Comprehensive Rig Tool - 4SchoolV1 Style",
            rtf=True,
            w=400,
            h=600
        )

        # Main layout
        main_layout = mc.columnLayout(adj=True, rs=5)

        # Title
        mc.text(label="Comprehensive Maya Rigging Tool",
                font="boldLabelFont", h=30)
        mc.separator(h=10)

        # Instructions
        mc.text(label="Select bones for each category, then build rig:",
                align="left", ww=True)
        mc.separator(h=5)

        # Bone selection section
        self.create_bone_selection_ui()

        mc.separator(h=10)

        # Build rig section
        mc.frameLayout(label="Build Rig", cll=True, cl=False)
        mc.columnLayout(adj=True, rs=5)

        mc.button(label="Validate Selections",
                 command=self.validate_selections,
                 h=30, bgc=[0.3, 0.5, 0.7])

        mc.button(label="BUILD COMPLETE RIG",
                 command=self.build_rig,
                 h=40, bgc=[0.2, 0.7, 0.2])

        mc.separator(h=5)

        # Utility buttons
        mc.text(label="Utilities:", font="boldLabelFont")
        mc.rowColumnLayout(nc=2, cw=[(1, 195), (2, 195)])

        mc.button(label="Clear All Selections",
                 command=self.clear_all_selections,
                 bgc=[0.7, 0.3, 0.3])

        mc.button(label="Select All Controllers",
                 command=self.select_all_controllers,
                 bgc=[0.5, 0.5, 0.7])

        mc.setParent('..')
        mc.setParent('..')
        mc.setParent('..')

        # Show window
        mc.showWindow(self.window)

    def create_bone_selection_ui(self):
        """Create the bone selection UI section"""
        mc.frameLayout(label="Bone Selection", cll=True, cl=False)
        mc.columnLayout(adj=True, rs=3)

        # Define categories and their display names
        categories = [
            ('left_arm', 'Left Arm Bones'),
            ('right_arm', 'Right Arm Bones'),
            ('left_leg', 'Left Leg Bones'),
            ('right_leg', 'Right Leg Bones'),
            ('spine', 'Spine Bones'),
            ('hips', 'Hip Bones'),
            ('chest', 'Chest Bones'),
            ('left_clavicle', 'Left Clavicle'),
            ('right_clavicle', 'Right Clavicle')
        ]

        for category, display_name in categories:
            # Create row layout for each category
            mc.rowColumnLayout(nc=3, cw=[(1, 120), (2, 200), (3, 70)])

            # Label
            mc.text(label=display_name + ":", align="right")

            # Text field for bone names
            text_field = mc.textField(
                placeholderText="Select bones and click 'Set'",
                editable=False
            )
            self.bone_selection_fields[category] = text_field

            # Set button
            mc.button(
                label="Set",
                command=lambda x, cat=category: self.set_bone_selection(cat),
                bgc=[0.4, 0.6, 0.4]
            )

            mc.setParent('..')

        mc.setParent('..')
        mc.setParent('..')

    def set_bone_selection(self, category):
        """Set bone selection for a category from Maya selection"""
        selected = mc.ls(sl=True, type='joint')

        if not selected:
            mc.warning(f"Please select joints for {category}")
            return

        # Store selection in rig builder
        self.rig_builder.set_bone_selection(category, selected)

        # Update UI field
        joint_names = ", ".join([joint.split('|')[-1] for joint in selected])
        if len(joint_names) > 40:
            joint_names = joint_names[:37] + "..."

        mc.textField(self.bone_selection_fields[category],
                    edit=True, text=joint_names)

        print(f"Set {len(selected)} joints for {category}: {selected}")

    def validate_selections(self, *args):
        """Validate bone selections"""
        issues = []

        # Check for required selections
        required_categories = ['left_arm', 'right_arm', 'left_leg', 'right_leg']
        for category in required_categories:
            if not self.rig_builder.bone_selections[category]:
                issues.append(f"Missing selection for {category}")
            elif len(self.rig_builder.bone_selections[category]) < 2:
                issues.append(f"{category} needs at least 2 joints for FK/IK")

        if issues:
            message = "Validation Issues:\n" + "\n".join(issues)
            mc.confirmDialog(title="Validation Failed", message=message, button="OK")
        else:
            mc.confirmDialog(title="Validation Passed",
                           message="All selections are valid!", button="OK")

    def build_rig(self, *args):
        """Build the complete rig"""
        # Validate first
        issues = []
        required_categories = ['left_arm', 'right_arm', 'left_leg', 'right_leg']
        for category in required_categories:
            if not self.rig_builder.bone_selections[category]:
                issues.append(f"Missing selection for {category}")

        if issues:
            message = "Cannot build rig:\n" + "\n".join(issues)
            mc.confirmDialog(title="Build Failed", message=message, button="OK")
            return

        # Confirm build
        result = mc.confirmDialog(
            title="Build Rig",
            message="This will create a complete rig system. Continue?",
            button=["Yes", "No"],
            defaultButton="Yes"
        )

        if result == "Yes":
            try:
                self.rig_builder.build_complete_rig()
            except Exception as e:
                mc.confirmDialog(
                    title="Build Error",
                    message=f"Error building rig: {str(e)}",
                    button="OK"
                )

    def clear_all_selections(self, *args):
        """Clear all bone selections"""
        for category in self.rig_builder.bone_selections:
            self.rig_builder.bone_selections[category] = []
            mc.textField(self.bone_selection_fields[category],
                        edit=True, text="")
        print("All selections cleared")

    def select_all_controllers(self, *args):
        """Select all created controllers"""
        controllers = []
        for rigger_name, rigger in self.rig_builder.riggers.items():
            if hasattr(rigger, 'fk_controllers'):
                controllers.extend(rigger.fk_controllers)
            if hasattr(rigger, 'ik_controller') and rigger.ik_controller:
                controllers.append(rigger.ik_controller)
            if hasattr(rigger, 'pole_vector_ctrl') and rigger.pole_vector_ctrl:
                controllers.append(rigger.pole_vector_ctrl)
            if isinstance(rigger, list):  # Simple controllers
                controllers.extend(rigger)

        if controllers:
            mc.select(controllers, r=True)
            print(f"Selected {len(controllers)} controllers")
        else:
            mc.warning("No controllers found. Build rig first.")


# Main execution function
def show_comprehensive_rig_ui():
    """Show the comprehensive rig UI"""
    ui = ComprehensiveRigUI()
    ui.create_ui()


# Execute if run directly
if __name__ == "__main__":
    show_comprehensive_rig_ui()
