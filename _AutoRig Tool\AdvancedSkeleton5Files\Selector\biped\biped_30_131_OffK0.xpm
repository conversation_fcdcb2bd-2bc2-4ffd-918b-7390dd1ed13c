/* XPM */
static char *biped_30_131_OffK0[] = {
/* columns rows colors chars-per-pixel */
"38 46 160 2",
"   c gray54",
".  c #8B8B8B",
"X  c #8C8B8C",
"o  c gray55",
"O  c #8E8E8D",
"+  c #8E8E8E",
"@  c #8F8E8F",
"#  c gray56",
"$  c #909090",
"%  c #909091",
"&  c gray57",
"*  c #929292",
"=  c #939393",
"-  c gray58",
";  c #959595",
":  c gray59",
">  c #979897",
",  c #9A9B9A",
"<  c gray61",
"1  c gray62",
"2  c #A0A0A0",
"3  c #A1A1A0",
"4  c #A7A6A6",
"5  c #A9A9A8",
"6  c #A9A9A9",
"7  c #AAAAAA",
"8  c #B7B7B6",
"9  c #BBBABB",
"0  c gray75",
"q  c #C1C0C0",
"w  c #C3C3C3",
"e  c gray77",
"r  c #C7C7C6",
"t  c #C7C7C8",
"y  c #CAC9CA",
"u  c gray80",
"i  c gray81",
"p  c #D0D0D0",
"a  c #D0D0D1",
"s  c #D0D1D1",
"d  c #D1D1D0",
"f  c #D4D4D5",
"g  c #D8D8D7",
"h  c #DADAD9",
"j  c #DBDBDC",
"k  c #DDDDDE",
"l  c gray87",
"z  c #DFDFDF",
"x  c #E1DFE0",
"c  c #E1E1E1",
"v  c #E3E1E1",
"b  c #E4E2E3",
"n  c #EAEAEB",
"m  c #EBECEB",
"M  c #EFF0F1",
"N  c gray94",
"B  c #F2F0F2",
"V  c #F3F1F2",
"C  c #F2F2F1",
"Z  c #F2F3F1",
"A  c gray95",
"S  c #F4F2F4",
"D  c #F4F3F4",
"F  c #F5F6F6",
"G  c #F7F5F5",
"H  c #F6F4F6",
"J  c #F7F7F4",
"K  c #F7F6F7",
"L  c #F6F6F8",
"P  c #F7F7F9",
"I  c #F7F8F6",
"U  c #F7F8F8",
"Y  c #F8F7F7",
"T  c #F9F7F6",
"R  c #F8F8F6",
"E  c #F8F8F8",
"W  c #F8F9FA",
"Q  c #F9FBFB",
"!  c #FAF8FA",
"~  c #FBF9FA",
"^  c gray98",
"/  c #FAFBFA",
"(  c #FAFBFB",
")  c #FBFAFA",
"_  c #FBFBFA",
"`  c #FBFBFB",
"'  c #FBFBFC",
"]  c #FBFCFA",
"[  c #FBFCFB",
"{  c #FBFDFB",
"}  c #FBFCFC",
"|  c #FCFAFA",
" . c #FCFAFB",
".. c #FCFBFC",
"X. c #FCFBFD",
"o. c #FCFDFB",
"O. c #FDFCFB",
"+. c gray99",
"@. c #FCFCFD",
"#. c #FCFDFC",
"$. c #FCFDFD",
"%. c #FDFCFC",
"&. c #FDFCFD",
"*. c #FDFDFC",
"=. c #FDFDFD",
"-. c #FCFCFE",
";. c #FCFDFE",
":. c #FCFDFF",
">. c #FDFCFE",
",. c #FDFCFF",
"<. c #FDFDFE",
"1. c #FDFDFF",
"2. c #FCFEFC",
"3. c #FCFEFD",
"4. c #FCFFFC",
"5. c #FCFFFD",
"6. c #FDFEFC",
"7. c #FDFEFD",
"8. c #FDFFFC",
"9. c #FDFFFD",
"0. c #FCFEFE",
"q. c #FCFEFF",
"w. c #FCFFFE",
"e. c #FCFFFF",
"r. c #FDFEFE",
"t. c #FDFEFF",
"y. c #FDFFFE",
"u. c #FDFFFF",
"i. c #FEFCFC",
"p. c #FEFCFD",
"a. c #FEFDFC",
"s. c #FEFDFD",
"d. c #FFFCFC",
"f. c #FFFCFD",
"g. c #FFFDFC",
"h. c #FFFDFD",
"j. c #FEFCFE",
"k. c #FEFCFF",
"l. c #FEFDFE",
"z. c #FEFDFF",
"x. c #FFFCFE",
"c. c #FFFCFF",
"v. c #FFFDFE",
"b. c #FFFDFF",
"n. c #FEFEFC",
"m. c #FEFEFD",
"M. c #FEFFFC",
"N. c #FEFFFD",
"B. c #FFFEFC",
"V. c #FFFEFD",
"C. c #FFFFFC",
"Z. c #FFFFFD",
"A. c #FEFEFE",
"S. c #FEFEFF",
"D. c #FEFFFE",
"F. c #FEFFFF",
"G. c #FFFEFE",
"H. c #FFFEFF",
"J. c #FFFFFE",
"K. c gray100",
/* pixels */
"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 ",
"0 1.C.1.C.1.C.1.C.1.` 1.1.1.1.1.C.1.C.1.C.C.1.1.C.C.{ 1.1.C.1.{ C.1.{ 1.C.0 ",
"0 1.1.1.1.1.C.C.| 1.C.C.1.1.C.1.C.1.1.C.C.C.1.1.C.1.C.{ C.1.C.{ C.C.C.C.{ 0 ",
"0 1.1.C.1.1.` C.C.1.C.1.1.C.` 1.1.C.1.1.1.C.1.1.` 1.1.C.C.1.{ 1.1.C.1.1.' 0 ",
"0 | 1.1.C.1.C.C.1.C.C.C.| 1.1.C.1.1.1.C.1.1.C.{ 1.1.1.C.{ 1.1.C.1.1.1.1.{ 0 ",
"0 C.C.C.1.1.1.1.1.C.1.C.1.C.C.1.1.C.1.C.1.C.C.1.1.` C.C.1.1.1.1.1.1.1.C.' 0 ",
"0 1.1.C.` C.1.1.C.1.1.C.1.1.| 1.C.C.1.1.C.1.1.1.1.1.1.1.1.1.1.C.C.C.1.1.' 0 ",
"0 C.1.1.1.1.{ C.1.1.C.1.C.1.1.C.C.1.1.C.C.{ 1.C.1.1.C.1.C.C.C.1.1.1.{ 1.' 0 ",
"0 R 1.1.1.1.C.R 1.1.1.1.` 1.1.1.C.C.1.C.C.C.C.1.C.C.{ 1.1.1.C.1.1.1.1.1.R 0 ",
"0 R 1.1.1.C.C.C.1.1.C.1.C.C.1.C.C.1.C.1.{ 1.| C.1.1.1.1.C.{ { C.1.1.1.C.Y 0 ",
"0 C.1.1.1.C.1.| 1.C.1.1.1.C.1.1.{ ` 1.1.1.1.1.1.C.1.{ 1.{ C.1.1.1.C.1.1.L 0 ",
"0 C.C.C.C.C.1.1.1.1.1.C.C.1.1.` ` 1.C.1.1.1.1.1.1.C.1.1.1.1.1.C.C.C.1.1.R 0 ",
"0 C.1.C.C.1.1.1.C.C.1.C.' 1.1.C.1.1.1.1.{ 1.1.C.1.` C.1.1.1.C.{ 1.1.1.{ C.0 ",
"0 | 1.R 1.C.C.1.C.1.1.C.C.1.1.C.1.` 1.1.C.1.1.C.1.C.1.1.C.C.C.1.1.1.C.C.C.0 ",
"0 1.C.C.C.1.C.1.C.C.1.1.C.1.C.1.1.1.1.C.C.{ 1.1.1.1.C.| C.1.{ ` ` 1.1.| 1.0 ",
"0 1.1.C.| 1.` 1.` 1.C.| ` 1.1.C.C.1.C.1.1.C.1.1.1.C.1.C.1.1.1.1.C.1.C.1.1.0 ",
"0 C.1.1.1.1.1.1.| C.C.C.{ { C.1.C.1.1.C.1.1.1.1.1.C.C.C.| 1.1.1.1.1.1.1.1.0 ",
"0 1.1.1.1.C.1.{ 1.C.1.C.1.C.1.C.C.1.C.1.1.1.1.C.1.C.1.1.` 1.1.` 1.` C.C.C.0 ",
"0 C.1.1.1.C.C.C.1.1.1.1.1.C.{ 1.C.1.{ 1.1.` C.C.C.| 1.` ` C.1.1.` 1.C.1.1.0 ",
"0 | { C.1.1.1.1.1.1.1.1.1.C.1.1.C.1.C.C.C.C.1.1.C.1.C.C.1.C.1.C.1.| C.C.1.0 ",
"0 R 1.1.{ 1.1.1.C.C.1.1.1.1.1.1.1.1.C.C.C.R 1.1.C.1.C.C.1.1.1.1.C.1.C.1.C.0 ",
"0 J 1.1.1.` 1.1.C.1.` C.1.1.C.C.1.{ 1.C.1.1.1.1.1.C.1.1.C.1.C.C.1.1.C.C.C.0 ",
"0 J 1.1.1.C.1.1.C.1.1.1.C.{ 1.C.1.1.1.C.1.1.1.1.1.1.1.C.C.1.1.C.1.1.1.1.1.0 ",
"0 C 1.C.1.{ C.1.C.1.1.1.1.C.1.1.1.1.C.1.1.1.1.1.1.C.1.1.C.C.1.C.C.1.1.C.1.0 ",
"0 n C.1.1.1.{ ' C.C.1.1.1.C.C.C.C.1.1.1.1.C.{ C.1.C.1.C.C.1.{ 1.1.C.C.1.C.0 ",
"0 l | 1.C.C.1.1.1.1.C.C.{ { 1.1.1.C.1.1.1.1.1.1.1.C.1.C.C.C.1.1.C.1.1.C.1.0 ",
"0 u T C.C.C.1.1.1.1.1.1.1.1.1.C.1.1.C.1.1.1.1.1.1.C.` 1.1.C.1.1.1.C.1.1.C.0 ",
"0 9 G 1.C.1.1.1.C.1.C.` 1.1.1.1.1.C.C.1.1.1.1.1.C.1.1.1.C.C.1.C.C.1.1.1.1.0 ",
"0 5 C 1.C.1.1.1.1.1.C.C.C.1.` C.1.1.1.C.C.1.1.{ 1.1.1.1.1.1.C.1.1.C.1.1.1.0 ",
"0 , N 1.| 1.{ 1.{ C.{ 1.1.1.1.C.C.C.C.1.C.1.1.1.1.C.1.1.1.C.1.C.C.C.{ 1.1.0 ",
"0 $ n 1.C.1.1.C.1.1.1.1.1.C.1.1.1.C.1.C.1.C.1.C.C.C.C.{ C.1.C.C.1.1.1.C.1.0 ",
"0   x R 1.1.C.1.1.` C.{ C.1.1.C.1.1.C.1.{ 1.C.1.1.1.1.1.1.1.{ { 1.1.C.C.1.0 ",
"0 . w D 1.1.1.C.1.1.1.C.1.C.1.1.1.C.1.1.C.1.C.1.1.1.C.C.1.C.1.R 1.1.C.1.1.0 ",
"0 : < g G | 1.1.1.C.1.1.{ 1.C.1.1.1.1.C.{ C.1.C.1.1.1.` 1.1.1.C.1.1.1.C.C.0 ",
"0 1 & 5 v | C.1.1.` 1.C.C.1.1.C.1.1.1.C.{ 1.1.1.1.1.1.C.C.1.1.1.{ C.1.C.1.0 ",
"0 1 < O r C 1.1.C.C.1.C.C.1.1.{ { C.C.1.1.1.1.1.1.| 1.C.1.1.1.1.{ 1.1.C.1.0 ",
"0 1 1 o 0 C 1.C.1.' 1.C.{ C.{ { 1.C.1.C.1.1.1.1.1.C.1.1.C.C.` ` 1.1.1.C.R 0 ",
"0 1 1 o i Y 1.C.1.C.C.C.1.1.1.1.1.| 1.C.C.C.C.1.1.` C.C.C.1.C.C.C.1.1.N h 0 ",
"0 1 1 & k ` 1.1.C.C.C.1.C.1.1.1.C.C.1.` ` 1.1.C.1.1.1.1.1.1.C.C.R G b e 3 0 ",
"0 1 1 = x 1.1.1.{ ' C.1.C.C.1.1.1.C.1.C.1.1.1.1.1.1.1.1.C.1.1.C.N s 5 & : 0 ",
"0 1 1 # h C.1.1.1.1.C.C.C.C.C.1.1.C.1.1.C.1.1.| C.{ C.1.{ 1.1.Z y > & < 1 0 ",
"0 1 1 o f T 1.1.C.1.1.1.C.1.1.C.1.1.C.1.1.1.1.| C.C.1.C.1.1.{ j 2 : 1 1 1 0 ",
"0 1 1 o i T { 1.1.C.{ | C.1.C.1.C.1.1.1.C.1.C.C.C.' C.1.1.1.J t O 1 1 1 1 0 ",
"0 1 1 o i J C.C.C.C.{ 1.C.1.1.C.1.1.1.1.C.1.C.1.1.` 1.1.1.1.n 8 # 1 1 1 1 0 ",
"0 1 1 . d T C.1.1.C.1.1.` 1.1.1.1.1.C.C.1.` C.C.1.1.1.1.1.1.z 4 - 1 1 1 1 0 ",
"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 "
};
