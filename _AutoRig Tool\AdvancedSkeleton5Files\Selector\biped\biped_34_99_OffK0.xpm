/* XPM */
static char *biped_34_99_OffK0[] = {
/* columns rows colors chars-per-pixel */
"10 13 48 1",
"  c #AFAFAF",
". c #B7B6B7",
"X c gray74",
"o c gray75",
"O c gray77",
"+ c gray80",
"@ c LightGray",
"# c #DADADA",
"$ c gray87",
"% c #DEDFDE",
"& c #E2E3E2",
"* c #E4E3E4",
"= c #E4E4E4",
"- c #E7E7E7",
"; c #E9E9E9",
": c #ECECEC",
"> c #EEEEEE",
", c #F3F3F2",
"< c #F4F4F4",
"1 c #F8F9F9",
"2 c #F9F8F8",
"3 c #FAFBFB",
"4 c #FBFBFB",
"5 c #FCFDFD",
"6 c #FDFDFC",
"7 c #FDFDFD",
"8 c #FDFDFE",
"9 c #FDFDFF",
"0 c #FCFEFD",
"q c #FDFEFD",
"w c #FDFFFD",
"e c #FCFEFE",
"r c #FDFEFE",
"t c #FDFEFF",
"y c #FDFFFE",
"u c #FEFDFD",
"i c #FFFDFD",
"p c #FEFCFE",
"a c #FEFDFE",
"s c #FEFDFF",
"d c #FEFEFD",
"f c #FEFFFD",
"g c #FFFEFD",
"h c #FEFEFE",
"j c #FEFEFF",
"k c #FEFFFE",
"l c #FFFEFE",
"z c #FFFFFE",
/* pixels */
"oooooooooo",
"o:aa6aa3#o",
"o-aaa3aa=o",
"o&aaaa0a:o",
"o%00a6a6,o",
"o#3aaaaa1o",
"o@100a000o",
"o+<aaaa00o",
"oO:aaaaaao",
"oo;aaaaaao",
"o.=aa0aaao",
"o %aa0aaao",
"oooooooooo"
};
