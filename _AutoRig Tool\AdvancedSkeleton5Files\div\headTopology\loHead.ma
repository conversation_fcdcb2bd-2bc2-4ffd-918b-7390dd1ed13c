//Maya ASCII 2012 scene
//Name: loHead.ma
//Last modified: Wed, Nov 08, 2017 10:17:14 PM
//Codeset: 1252
requires maya "2012";
requires "stereoCamera" "10.0";
currentUnit -l centimeter -a degree -t pal;
fileInfo "application" "maya";
fileInfo "product" "Maya 2012";
fileInfo "version" "2012 x64";
fileInfo "cutIdentifier" "201201172029-821146";
fileInfo "osv" "Microsoft Business Edition, 64-bit  (Build 9200)\n";
fileInfo "comment" "";
fileInfo "incPath" "C:/Users/<USER>/Documents/maya/2015-x64/scripts/AdvancedSkeleton5/AdvancedSkeleton5Files/div/headTopology/incrementalSave/loHead.ma/loHead.0014.ma";
fileInfo "path" "C:/Users/<USER>/Documents/maya/2015-x64/scripts/AdvancedSkeleton5/AdvancedSkeleton5Files/div/headTopology/loHead.ma";
fileInfo "user" "me";
fileInfo "date" "2017/11/08";
fileInfo "time" "22:07:35";
fileInfo "shotInfo" "";
createNode transform -s -n "persp";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -0.95810785478866289 0.71500680689229124 1.6504502838521968 ;
	setAttr ".r" -type "double3" -7.5383525746801165 -37.800000000004211 0 ;
	setAttr ".rp" -type "double3" -2.7755575615628914e-017 1.1102230246251565e-016 0 ;
	setAttr ".rpt" -type "double3" -3.9075188777972731e-018 -1.7481387370475386e-018 
		4.0206580339147164e-017 ;
createNode camera -s -n "perspShape" -p "persp";
	setAttr -k off ".v" no;
	setAttr ".fl" 34.999999999999979;
	setAttr ".ncp" 0.001;
	setAttr ".coi" 1.7912251604062317;
	setAttr ".imn" -type "string" "persp";
	setAttr ".den" -type "string" "persp_depth";
	setAttr ".man" -type "string" "persp_mask";
	setAttr ".tp" -type "double3" 0 0.32908019423484802 0.36139658093452454 ;
	setAttr ".hc" -type "string" "viewSet -p %camera";
createNode transform -s -n "top";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 0 1000.1015121397991 0.047457814216835731 ;
	setAttr ".r" -type "double3" -89.999999999999986 0 0 ;
createNode camera -s -n "topShape" -p "top";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 999.708760133434;
	setAttr ".ow" 1.901676978835646;
	setAttr ".imn" -type "string" "top";
	setAttr ".den" -type "string" "top_depth";
	setAttr ".man" -type "string" "top_mask";
	setAttr ".tp" -type "double3" 3.5017728805541992e-006 0.39124151319265366 0.057378515601158142 ;
	setAttr ".hc" -type "string" "viewSet -t %camera";
	setAttr ".o" yes;
createNode transform -s -n "front";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -0.17186766686475874 0.58233594689047286 1000.3290710592632 ;
createNode camera -s -n "frontShape" -p "front";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 999.97916768601044;
	setAttr ".ow" 0.39610224851190401;
	setAttr ".imn" -type "string" "front";
	setAttr ".den" -type "string" "front_depth";
	setAttr ".man" -type "string" "front_mask";
	setAttr ".tp" -type "double3" 0.048586874767294619 0.57313127403378339 0.34988301119449261 ;
	setAttr ".hc" -type "string" "viewSet -f %camera";
	setAttr ".o" yes;
createNode transform -s -n "side";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 1000.3283119039558 0.55517191461808779 0.31997714352076023 ;
	setAttr ".r" -type "double3" 0 89.999999999999972 0 ;
	setAttr ".rpt" -type "double3" 5.5466782398352393e-032 0 -6.1629758220391547e-033 ;
createNode camera -s -n "sideShape" -p "side";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 999.6309954336698;
	setAttr ".ow" 2.0109785011998111;
	setAttr ".imn" -type "string" "side";
	setAttr ".den" -type "string" "side_depth";
	setAttr ".man" -type "string" "side_mask";
	setAttr ".tp" -type "double3" 0.69731647028595489 0.49500454864433074 0.48196620575735521 ;
	setAttr ".hc" -type "string" "viewSet -s %camera";
	setAttr ".o" yes;
createNode transform -n "headTopology";
	setAttr ".ove" yes;
createNode mesh -n "headTopologyShape" -p "headTopology";
	setAttr -k off ".v";
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr -s 380 ".uvst[0].uvsp";
	setAttr ".uvst[0].uvsp[0:249]" -type "float2" 0.375 0.44582501 0.375 0.5
		 0.45833299 0.5 0.45833299 0.44582501 0.54166698 0.5 0.54166698 0.44582501 0.625 0.5
		 0.625 0.44582501 0.375 0.58333302 0.45833299 0.58333302 0.54166698 0.58333302 0.625
		 0.58333302 0.375 0.63046098 0.45833299 0.62325102 0.54166698 0.66666698 0.51105899
		 0.624035 0.625 0.66666698 0.73409498 0.351677 0.875 0.118481 0.589692 0.66666698
		 0.375 0.72318399 0.375 0.75 0.45833299 0.75 0.45833299 0.71798599 0.54166698 0.75
		 0.54166698 0.71752697 0.625 0.75 0.625 0.72051901 0.54166698 0.80417502 0.57244301
		 0.80417502 0.82082498 0.25 0.875 0.25 0.875 0.166667 0.82082498 0.166667 0.125 0
		 0.125 0.026815999 0.161117 0.029648 0.179175 0 0.125 0.119539 0.125 0.166667 0.179175
		 0.166667 0.179175 0.117799 0.125 0.25 0.179175 0.25 0.82082498 0.024018999 0.875
		 0.029480999 0.875 0 0.82082498 0 0.125 0.083333001 0.179175 0.083333001 0.375 0.66666698
		 0.45833299 0.66666698 0.625 0.68773597 0.82082498 0.060892001 0.82082498 0.071425997
		 0.845451 0.076839 0.375 0.375 0.45833299 0.375 0.54166698 0.375 0.625 0.375 0.75
		 0.166667 0.75 0.25 0.80564803 0.022489 0.79710901 0 0.60737002 0.85029697 0.625 0.82789099
		 0.625 0.80417502 0.59065598 0.80417502 0.25 0.035778001 0.25 0 0.25 0.083333001 0.25
		 0.166667 0.25 0.118057 0.25 0.25 0.625 0.63151902 0.77708203 0.117253 0.807769 0.062492002
		 0.82082498 0.046333998 0.80145699 0.046101 0.79478103 0.056814998 0.875 0.062263999
		 0.875 0.049598999 0.625 0.70040101 0.77170599 0.083333001 0.78085399 0.083333001
		 0.875 0.083333001 0.82082498 0.10126 0.75 0.11341 0.75 0.083333001 0.76323098 0.083333001
		 0.75 0.069572002 0.789877 0.064685002 0.75 0.045483001 0.75 0.056671999 0.79231697
		 0.060768999 0.75 0.062150002 0.75 0 0.587933 0.875 0.625 0.875 0.54166698 0.875 0.56616497
		 0.875 0.375 0.75 0.375 0.75 0.45833299 0.75 0.45833299 0.75 0.375 0.80417502 0.375
		 0.80417502 0.54166698 0.75 0.54166698 0.75 0.54166698 0.80417502 0.54166698 0.80417502
		 0.54166698 0.875 0.54166698 0.875 0.375 0.875 0.375 0.875 0.375 0.75 0.45833299 0.75
		 0.375 0.80417502 0.54166698 0.75 0.54166698 0.80417502 0.54166698 0.875 0.375 0.875
		 0.375 0.875 0.375 0.80417502 0.375 0.44582501 0.375 0.5 0.45833299 0.5 0.45833299
		 0.44582501 0.54166698 0.5 0.54166698 0.44582501 0.625 0.5 0.625 0.44582501 0.375
		 0.58333302 0.45833299 0.58333302 0.54166698 0.58333302 0.625 0.58333302 0.375 0.63046098
		 0.45833299 0.62325102 0.54166698 0.66666698 0.51105899 0.624035 0.625 0.66666698
		 0.73409498 0.351677 0.875 0.118481 0.589692 0.66666698 0.375 0.72318399 0.375 0.75
		 0.45833299 0.75 0.45833299 0.71798599 0.54166698 0.75 0.54166698 0.71752697 0.625
		 0.75 0.625 0.72051901 0.54166698 0.80417502 0.57244301 0.80417502 0.82082498 0.25
		 0.875 0.25 0.875 0.166667 0.82082498 0.166667 0.125 0 0.125 0.026815999 0.161117
		 0.029648 0.179175 0 0.125 0.119539 0.125 0.166667 0.179175 0.166667 0.179175 0.117799
		 0.125 0.25 0.179175 0.25 0.82082498 0.024018999 0.875 0.029480999 0.875 0 0.82082498
		 0 0.125 0.083333001 0.179175 0.083333001 0.375 0.66666698 0.45833299 0.66666698 0.625
		 0.68773597 0.82082498 0.060892001 0.82082498 0.071425997 0.845451 0.076839 0.375
		 0.375 0.45833299 0.375 0.54166698 0.375 0.625 0.375 0.75 0.166667 0.75 0.25 0.80564803
		 0.022489 0.79710901 0 0.60737002 0.85029697 0.625 0.82789099 0.625 0.80417502 0.59065598
		 0.80417502 0.25 0.035778001 0.25 0 0.25 0.083333001 0.25 0.166667 0.25 0.118057 0.25
		 0.25 0.625 0.63151902 0.77708203 0.117253 0.807769 0.062492002 0.82082498 0.046333998
		 0.80145699 0.046101 0.79478103 0.056814998 0.875 0.062263999 0.875 0.049598999 0.625
		 0.70040101 0.77170599 0.083333001 0.78085399 0.083333001 0.875 0.083333001 0.82082498
		 0.10126 0.75 0.11341 0.75 0.083333001 0.76323098 0.083333001 0.75 0.069572002 0.789877
		 0.064685002 0.75 0.045483001 0.75 0.056671999 0.79231697 0.060768999 0.75 0.062150002
		 0.75 0 0.587933 0.875 0.625 0.875 0.54166698 0.875 0.56616497 0.875 0.375 0.75 0.375
		 0.75 0.45833299 0.75 0.45833299 0.75 0.375 0.80417502 0.375 0.80417502 0.54166698
		 0.75 0.54166698 0.75 0.54166698 0.80417502 0.54166698 0.80417502 0.54166698 0.875
		 0.54166698 0.875 0.375 0.875 0.375 0.875 0.375 0.75 0.45833299 0.75 0.375 0.80417502
		 0.54166698 0.75 0.54166698 0.80417502 0.54166698 0.875 0.375 0.875 0.375 0.875 0.375
		 0.80417502 0.75 0.031180505 0.75 0.0084389988;
	setAttr ".uvst[0].uvsp[250:379]" 0.75 0.031180505 0.75 0.0084389979 0 0 1 0
		 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1
		 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1
		 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0
		 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1
		 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0
		 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0
		 1 0 1 1 0 1;
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr -s 26 ".pt";
	setAttr ".pt[67]" -type "float3" -9.3132257e-010 0 0 ;
	setAttr ".pt[149]" -type "float3" 9.3132257e-010 0 0 ;
	setAttr ".pt[178:189]" -type "float3" 0 0 0.0032162715  0 0 0.0032162715  
		0 0 0.0032162715  0 0 0.0032162715  0 0 0.0032162715  0 0 0.0032162715  0 0 0.0032162715  
		0 0 0.0032162715  0 0 0.0032162715  0 0 0.0032162715  0 0 0.0032162715  0 0 0.0032162715 ;
	setAttr ".pt[198:209]" -type "float3" 0 0 -0.0034079161  0 0 -0.0034079161  
		0 0 -0.0034079161  0 0 -0.0034079161  0 0 -0.0034079161  0 0 -0.0034079161  0 0 -0.0034079161  
		0 0 -0.0034079161  0 0 -0.0034079161  0 0 -0.0034079161  0 0 -0.0034079161  0 0 -0.0034079161 ;
	setAttr -s 210 ".vt";
	setAttr ".vt[0:165]"  8.0108089e-019 0.96444911 -0.12094412 -1.587057e-018 0.99217403 0.1062285
		 -5.9264199e-018 0.91634417 0.283268 -7.410281e-018 0.80081809 0.36772943 -2.8463171e-018 0.32831866 -0.2461051
		 2.9036846e-018 0.14820492 0.16949478 -9.524385e-019 0.22287153 0.38595748 -8.4191973e-018 0.5191527 0.41064543
		 -5.5721914e-018 0.69587302 0.39002153 3.3561404e-018 0.5326584 -0.32767722 1.3790701e-018 0.83136219 -0.27396825
		 3.4785898e-018 0.42540038 -0.28598222 -7.3925528e-018 0.59797472 0.40108627 1.3317482e-017 0.44588175 0.45962566
		 1.9261111e-018 0.39209124 0.40092608 1.8758477e-018 0.41226053 0.40751404 1.058241e-017 0.42446044 0.44025025
		 3.1522879e-019 0.18171702 0.36650366 0 0.16640878 0.28346846 4.0525472e-018 0.68341309 -0.32636064
		 -2.673036e-019 0.051852733 0.16724122 1.0380067e-019 0.13688654 -0.2619524 -6.2716167e-019 0 0.16479348
		 9.189745e-019 0 -0.2623226 -5.3598276e-018 0.27319723 -0.25535771 2.6009528e-018 0.12076394 0.1673788
		 -0.23272173 0.89303648 -0.10005499 -0.23069021 0.89696193 0.07689736 -0.2165311 0.84898645 0.23559491
		 -0.19029589 0.76850891 0.30388093 -0.24542969 0.80653214 -0.19678313 -0.2710067 0.77001476 -0.048916824
		 -0.2710067 0.73667783 0.12197994 -0.20733055 0.69119304 0.27253681 -0.2265767 0.54836506 -0.23784174
		 -0.2710067 0.53976214 -0.052589908 -0.24539369 0.53258133 0.13113557 -0.19909352 0.58214378 0.29660407
		 -0.16839221 0.35804924 -0.16828233 -0.19867268 0.33964336 -0.055082474 -0.20027639 0.2842747 0.070780851
		 -0.17240947 0.22867948 0.27594328 -0.18534979 0.34644067 0.24552725 -0.20590258 0.45315623 -0.20930615
		 -0.22779965 0.42631358 -0.048112661 -0.21900323 0.39448944 0.098424286 -0.1202702 0.96221316 -0.1148559
		 -0.1202702 0.97938848 0.09333057 -0.1202702 0.9141981 0.26152998 -0.1202702 0.80994469 0.34471869
		 -0.085573241 0.69645643 0.37387818 -0.1377047 0.33120266 0.31655943 -0.12654436 0.21930815 0.35874301
		 -0.1177443 0.17678891 0.15817586 -0.11743168 0.34939647 -0.2304617 -0.11743232 0.43881842 -0.26199678
		 -0.11743168 0.54084641 -0.30123883 -0.1202702 0.83839047 -0.25989616 -0.23049913 0.55204892 0.251856
		 -0.19631773 0.63876754 0.29978815 -0.081967279 0.49055645 0.35944098 -0.18652076 0.51186186 0.31164491
		 -0.098061748 0.60015285 0.33543128 -0.082967222 0.56612211 0.33013713 -0.098728806 0.5506652 0.32443285
		 -0.16327651 0.55234015 0.30799133 -0.16567445 0.60748714 0.31889746 -0.049983926 0.61393923 0.39259547
		 -0.033676066 0.52397549 0.39561006 -0.036063913 0.4446443 0.43346629 -0.12194436 0.39238876 0.35211107
		 -0.18925527 0.41494671 0.28014022 -0.073373415 0.39361608 0.3768298 -0.12003662 0.33014038 0.34887195
		 -0.047899123 0.53483415 0.36830786 -0.060169466 0.46178934 0.37564677 -0.056443352 0.41774157 0.3864415
		 -0.043925546 0.42630777 0.41631907 -0.082458012 0.21916014 0.3783887 -0.04992535 0.18430606 0.36268908
		 -0.086178146 0.1869815 0.33220813 -0.098579958 0.18426542 0.28550383 -0.26303288 0.62834936 0.07779599
		 -0.2710067 0.64283168 -0.056815248 -0.24120806 0.68643934 -0.24532159 -0.11743168 0.68183464 -0.30802357
		 -0.19788741 0.1316756 -0.16125533 -0.16872521 0.11970782 -0.069031522 -0.099765204 0.13566655 -0.25208235
		 -0.16116831 0.089205228 0.051780988 -0.10003059 0.052816447 0.13699378 -0.28719264 0 -0.15900895
		 -0.29990527 0 -0.066633716 -0.1073442 0 -0.25755918 -0.22217219 0 0.042554829 -0.10762928 0 0.13145915
		 -0.10646197 0.28027713 -0.24134843 -0.15263849 0.26334211 -0.16702645 -0.1800769 0.24458887 -0.057193898
		 -0.1759927 0.20180134 0.060386129 -0.10674527 0.12940671 0.14794327 0.1202702 0.96221316 -0.1148559
		 0.23272173 0.89303648 -0.10005499 0.23069021 0.89696193 0.07689736 0.1202702 0.97938848 0.09333057
		 0.2165311 0.84898645 0.23559491 0.1202702 0.9141981 0.26152998 0.19029589 0.76850891 0.30388093
		 0.1202702 0.80994469 0.34471869 0.24542969 0.80653214 -0.19678313 0.2710067 0.77001476 -0.048916824
		 0.2710067 0.73667783 0.12197994 0.20733055 0.69119304 0.27253681 0.24120806 0.68643934 -0.24532159
		 0.2710067 0.64283168 -0.056815248 0.24539369 0.53258133 0.13113557 0.26303288 0.62834936 0.07779599
		 0.19909352 0.58214378 0.29660407 0.16567445 0.60748714 0.31889746 0.19631773 0.63876754 0.29978815
		 0.23049913 0.55204892 0.251856 0.20590258 0.45315623 -0.20930615 0.16839221 0.35804924 -0.16828233
		 0.19867268 0.33964336 -0.055082474 0.22779965 0.42631358 -0.048112661 0.20027639 0.2842747 0.070780851
		 0.21900323 0.39448944 0.098424286 0.17240947 0.22867948 0.27594328 0.18534979 0.34644067 0.24552725
		 0.1177443 0.17678891 0.15817586 0.098579958 0.18426542 0.28550383 0.085573241 0.69645643 0.37387818
		 0.11743232 0.43881842 -0.26199678 0.11743168 0.34939647 -0.2304617 0.1202702 0.83839047 -0.25989616
		 0.11743168 0.68183464 -0.30802357 0.1377047 0.33120266 0.31655943 0.12654436 0.21930815 0.35874301
		 0.2265767 0.54836506 -0.23784174 0.11743168 0.54084641 -0.30123883 0.2710067 0.53976214 -0.052589908
		 0.18652076 0.51186186 0.31164491 0.081967279 0.49055645 0.35944098 0.098728806 0.5506652 0.32443285
		 0.16327651 0.55234015 0.30799133 0.12003662 0.33014038 0.34887195 0.082458012 0.21916014 0.3783887
		 0.04992535 0.18430606 0.36268908 0.086178146 0.1869815 0.33220813 0.049983926 0.61393923 0.39259547
		 0.060169466 0.46178934 0.37564677 0.12194436 0.39238876 0.35211107 0.073373415 0.39361608 0.3768298
		 0.056443352 0.41774157 0.3864415 0.18925527 0.41494671 0.28014022 0.047899123 0.53483415 0.36830786
		 0.082967222 0.56612211 0.33013713 0.098061748 0.60015285 0.33543128 0.033676066 0.52397549 0.39561006
		 0.036063913 0.4446443 0.43346629 0.043925546 0.42630777 0.41631907 0.15263849 0.26334211 -0.16702645
		 0.19788741 0.1316756 -0.16125533 0.16872521 0.11970782 -0.069031522 0.1800769 0.24458887 -0.057193898
		 0.10646197 0.28027713 -0.24134843;
	setAttr ".vt[166:209]" 0.099765204 0.13566655 -0.25208235 0.16116831 0.089205228 0.051780988
		 0.1759927 0.20180134 0.060386129 0.10003059 0.052816447 0.13699378 0.10674527 0.12940671 0.14794327
		 0.28719264 0 -0.15900895 0.29990527 0 -0.066633716 0.1073442 0 -0.25755918 0.22217219 0 0.042554829
		 0.10762928 0 0.13145915 -6.7640477e-019 0.36057907 0.41193688 -2.4260008e-018 0.28550005 0.40824613
		 -0.19461216 0.58177328 0.30044413 -0.16362132 0.59897918 0.31828979 -0.10059068 0.55878717 0.32003903
		 -0.16013789 0.56083798 0.30819064 -0.088338077 0.56888771 0.32347769 -0.10086034 0.59515685 0.32891724
		 0.19461216 0.58177328 0.30044413 0.16362132 0.59897918 0.31828979 0.10059068 0.55878717 0.32003903
		 0.16013789 0.56083798 0.30819064 0.088338085 0.56888765 0.32347769 0.10086035 0.59515679 0.32891724
		 -0.11463622 0.32908019 0.34975389 -2.3168558e-018 0.29110494 0.41190583 0.11463622 0.32908019 0.34975389
		 -6.4597352e-019 0.35497418 0.41504553 -0.10703363 0.32842764 0.35349393 -2.066571e-018 0.29455477 0.40256292
		 0.10703363 0.32842764 0.35349393 -5.7619044e-019 0.35152435 0.40536347 -0.19346651 0.58170801 0.300892
		 -0.16306534 0.59861118 0.31816345 -0.10141834 0.55899978 0.31989187 -0.15969911 0.56116849 0.30826241
		 -0.088771634 0.56917065 0.3235127 -0.10173952 0.59476399 0.32863608 0.19346651 0.58170801 0.300892
		 0.16306534 0.59861118 0.31816345 0.10141834 0.55899978 0.31989187 0.15969911 0.56116849 0.30826241
		 0.088771641 0.56917059 0.3235127 0.10173953 0.59476393 0.32863608;
	setAttr -s 408 ".ed";
	setAttr ".ed[0:165]"  0 1 1 1 2 1 2 3 1 3 8 1 4 11 1 11 9 1 19 10 1 10 0 1
		 8 12 1 12 7 1 7 13 1 16 15 1 13 16 1 18 5 1 17 18 1 9 19 1 25 20 1 21 24 1 20 22 1
		 23 21 1 24 4 1 5 25 1 46 26 1 26 27 1 27 47 1 47 46 1 27 28 1 28 48 1 48 47 1 28 29 1
		 29 49 1 49 48 1 26 30 1 30 31 1 31 27 1 31 32 1 32 28 1 32 33 1 33 29 1 30 84 1 84 83 1
		 83 31 1 36 33 1 32 82 1 82 36 1 37 66 1 66 59 1 59 58 1 58 37 1 43 38 1 38 39 1 39 44 1
		 44 43 1 39 40 1 40 45 1 45 44 1 40 41 1 41 42 1 42 45 1 40 53 1 53 81 1 81 41 1 33 50 1
		 50 49 1 43 55 1 55 54 1 54 38 1 30 57 1 57 85 1 85 84 1 46 57 1 51 42 1 41 52 1 52 51 1
		 43 34 1 34 56 1 56 55 1 44 35 1 35 34 1 45 36 1 36 35 1 61 60 1 60 64 1 64 65 1 65 61 1
		 0 46 1 47 1 1 48 2 1 49 3 1 50 8 1 52 80 1 80 79 1 55 11 1 4 54 1 56 9 1 57 10 1
		 19 85 1 36 58 1 59 33 1 59 67 1 67 50 1 75 60 1 60 70 1 76 75 1 61 71 1 71 70 1 36 71 1
		 61 58 1 74 63 1 63 64 1 75 74 1 65 37 1 62 63 1 74 67 1 67 62 1 67 12 1 67 68 1 68 7 1
		 68 69 1 69 13 1 66 62 1 70 51 1 71 42 1 15 76 1 74 68 1 75 69 1 76 77 1 77 69 1 16 77 1
		 79 17 1 53 5 1 18 81 1 81 80 1 83 82 1 83 35 1 84 34 1 85 56 1 97 86 1 86 87 1 87 98 1
		 98 97 1 96 88 1 88 86 1 97 96 1 87 89 1 89 99 1 99 98 1 89 90 1 90 100 1 100 99 1
		 90 20 1 25 100 1 21 88 1 96 24 1 86 91 1 91 92 0 92 87 1 88 93 1 93 91 0 92 94 0
		 94 89 1 94 95 0 95 90 1 95 22 0 23 93 0 96 54 1;
	setAttr ".ed[166:331]" 97 38 1 98 39 1 99 40 1 100 53 1 101 102 1 102 103 1
		 103 104 1 104 101 1 103 105 1 105 106 1 106 104 1 105 107 1 107 108 1 108 106 1 102 109 1
		 109 110 1 110 103 1 110 111 1 111 105 1 111 112 1 112 107 1 109 113 1 113 114 1 114 110 1
		 115 112 1 111 116 1 116 115 1 117 118 1 118 119 1 119 120 1 120 117 1 121 122 1 122 123 1
		 123 124 1 124 121 1 123 125 1 125 126 1 126 124 1 125 127 1 127 128 1 128 126 1 125 129 1
		 129 130 1 130 127 1 112 131 1 131 108 1 121 132 1 132 133 1 133 122 1 109 134 1 134 135 1
		 135 113 1 101 134 1 136 128 1 127 137 1 137 136 1 121 138 1 138 139 1 139 132 1 124 140 1
		 140 138 1 126 115 1 115 140 1 141 142 1 142 143 1 143 144 1 144 141 1 0 101 1 104 1 1
		 106 2 1 108 3 1 131 8 1 137 148 1 148 147 1 132 11 1 4 133 1 139 9 1 134 10 1 19 135 1
		 115 120 1 119 112 1 119 149 1 149 131 1 150 142 1 142 151 1 153 150 1 141 154 1 154 151 1
		 115 154 1 141 120 1 155 156 1 156 143 1 150 155 1 144 117 1 157 156 1 155 149 1 149 157 1
		 149 12 1 149 158 1 158 7 1 158 159 1 159 13 1 118 157 1 151 136 1 154 128 1 15 153 1
		 155 158 1 150 159 1 153 160 1 160 159 1 16 160 1 147 17 1 129 5 1 18 130 1 130 148 1
		 114 116 1 114 140 1 113 138 1 135 139 1 161 162 1 162 163 1 163 164 1 164 161 1 165 166 1
		 166 162 1 161 165 1 163 167 1 167 168 1 168 164 1 167 169 1 169 170 1 170 168 1 169 20 1
		 25 170 1 21 166 1 165 24 1 162 171 1 171 172 0 172 163 1 166 173 1 173 171 0 172 174 0
		 174 167 1 174 175 0 175 169 1 175 22 0 23 173 0 165 133 1 161 122 1 164 123 1 168 125 1
		 170 129 1 52 78 1 70 72 1 72 76 1 51 73 1 14 15 1 14 72 1 72 73 1 73 78 1 6 177 1
		 6 78 1 78 79 1 145 146 1 136 145 1 137 146 1;
	setAttr ".ed[332:407]" 146 147 1 151 152 1 14 152 1 152 153 1 145 152 1 14 176 1
		 6 146 1 6 17 1 73 177 0 145 177 0 145 176 0 73 176 0 37 178 1 66 179 1 178 179 1
		 64 180 1 65 181 1 180 181 1 63 182 0 182 180 0 181 178 1 62 183 0 183 182 0 179 183 1
		 117 184 1 118 185 1 184 185 1 143 186 1 144 187 1 186 187 1 156 188 0 188 186 0 187 184 1
		 157 189 0 189 188 0 185 189 1 73 190 0 177 191 0 190 191 0 145 192 0 192 191 0 176 193 0
		 192 193 0 190 193 0 190 194 0 191 195 0 194 195 0 192 196 0 196 195 0 193 197 0 196 197 0
		 194 197 0 178 198 1 179 199 1 198 199 0 180 200 1 181 201 1 200 201 0 182 202 1 202 200 0
		 201 198 0 183 203 1 203 202 0 199 203 0 184 204 1 185 205 1 204 205 0 186 206 1 187 207 1
		 206 207 0 188 208 1 208 206 0 207 204 0 189 209 1 209 208 0 205 209 0;
	setAttr -s 240 ".n";
	setAttr ".n[0:165]" -type "float3"  1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020;
	setAttr ".n[166:239]" -type "float3"  1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020
		 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020 1e+020;
	setAttr -s 196 ".fc[0:195]" -type "polyFaces" 
		f 4 22 23 24 25
		mu 0 4 0 1 2 3
		f 4 -25 26 27 28
		mu 0 4 3 2 4 5
		f 4 -28 29 30 31
		mu 0 4 5 4 6 7
		f 4 32 33 34 -24
		mu 0 4 1 8 9 2
		f 4 -35 35 36 -27
		mu 0 4 2 9 10 4
		f 4 -37 37 38 -30
		mu 0 4 4 10 11 6
		f 4 39 40 41 -34
		mu 0 4 8 12 13 9
		f 4 42 -38 43 44
		mu 0 4 14 11 10 15
		f 4 45 46 47 48
		mu 0 4 16 17 18 19
		f 4 49 50 51 52
		mu 0 4 20 21 22 23
		f 4 -52 53 54 55
		mu 0 4 23 22 24 25
		f 4 -55 56 57 58
		mu 0 4 25 24 26 27
		f 4 59 60 61 -57
		mu 0 4 24 28 29 26
		f 4 -31 -39 62 63
		mu 0 4 30 31 32 33
		f 4 -50 64 65 66
		mu 0 4 34 35 36 37
		f 4 -40 67 68 69
		mu 0 4 38 39 40 41
		f 4 -33 -23 70 -68
		mu 0 4 39 42 43 40
		f 4 71 -58 72 73
		mu 0 4 44 45 46 47
		f 4 74 75 76 -65
		mu 0 4 35 48 49 36
		f 4 -75 -53 77 78
		mu 0 4 50 20 23 51
		f 4 -78 -56 79 80
		mu 0 4 51 23 25 14
		f 4 81 82 83 84
		mu 0 4 52 53 54 55
		f 4 85 -26 86 -1
		mu 0 4 56 0 3 57
		f 4 -87 -29 87 -2
		mu 0 4 57 3 5 58
		f 4 -88 -32 88 -3
		mu 0 4 58 5 7 59
		f 4 -4 -89 -64 89
		mu 0 4 60 61 30 33
		f 4 92 -5 93 -66
		mu 0 4 36 68 69 37
		f 4 94 -6 -93 -77
		mu 0 4 49 70 68 36
		f 4 95 -7 96 -69
		mu 0 4 40 71 72 41
		f 4 -86 -8 -96 -71
		mu 0 4 43 73 71 40
		f 4 97 -48 98 -43
		mu 0 4 14 19 74 11
		f 4 99 100 -63 -99
		mu 0 4 18 75 33 32
		f 4 104 105 -103 -82
		mu 0 4 80 81 77 53
		f 4 106 -105 107 -98
		mu 0 4 14 82 52 19
		f 5 108 109 -83 -102 110
		mu 0 5 83 84 54 53 76
		f 4 -85 111 -49 -108
		mu 0 4 52 55 85 19
		f 4 112 -109 113 114
		mu 0 4 86 84 83 75
		f 4 -9 -90 -101 115
		mu 0 4 87 60 33 75
		f 4 -10 -116 116 117
		mu 0 4 88 87 75 89
		f 4 -11 -118 118 119
		mu 0 4 90 88 89 91
		f 4 -115 -100 -47 120
		mu 0 4 86 75 18 17
		f 4 122 -72 -122 -106
		mu 0 4 81 45 44 77
		f 4 -80 -59 -123 -107
		mu 0 4 14 25 27 82
		f 3 124 -117 -114
		mu 0 3 83 89 75
		f 4 -125 -111 125 -119
		mu 0 4 89 83 76 91
		f 4 -126 -104 126 127
		mu 0 4 91 76 79 94
		f 4 -12 128 -127 -124
		mu 0 4 93 95 94 79
		f 4 -13 -120 -128 -129
		mu 0 4 95 90 91 94
		f 4 130 -14 131 -61
		mu 0 4 28 99 100 29
		f 4 132 -91 -73 -62
		mu 0 4 29 67 66 26
		f 5 -15 -130 -92 -133 -132
		mu 0 5 100 97 64 67 29
		f 4 -44 -36 -42 133
		mu 0 4 15 10 9 13
		f 4 -45 -134 134 -81
		mu 0 4 14 15 13 51
		f 4 135 -79 -135 -41
		mu 0 4 12 50 51 13
		f 4 -136 -70 136 -76
		mu 0 4 48 38 41 49
		f 4 -16 -95 -137 -97
		mu 0 4 72 70 49 41
		f 4 137 138 139 140
		mu 0 4 101 102 103 104
		f 4 141 142 -138 143
		mu 0 4 105 106 102 101
		f 4 -140 144 145 146
		mu 0 4 104 103 107 108
		f 4 -146 147 148 149
		mu 0 4 108 107 109 110
		f 4 -149 150 -17 151
		mu 0 4 110 109 111 112
		f 4 -18 152 -142 153
		mu 0 4 113 114 106 105
		f 4 154 155 156 -139
		mu 0 4 102 115 116 103
		f 4 157 158 -155 -143
		mu 0 4 106 117 115 102
		f 4 -157 159 160 -145
		mu 0 4 103 116 118 107
		f 4 -161 161 162 -148
		mu 0 4 107 118 119 109
		f 4 -163 163 -19 -151
		mu 0 4 109 119 120 111
		f 4 -20 164 -158 -153
		mu 0 4 114 121 117 106
		f 4 -21 -154 165 -94
		mu 0 4 122 113 105 123
		f 4 -166 -144 166 -67
		mu 0 4 123 105 101 21
		f 4 -167 -141 167 -51
		mu 0 4 21 101 104 22
		f 4 -168 -147 168 -54
		mu 0 4 22 104 108 24
		f 4 -169 -150 169 -60
		mu 0 4 24 108 110 28
		f 4 -170 -152 -22 -131
		mu 0 4 28 110 112 99
		f 4 -174 -173 -172 -171
		mu 0 4 124 127 126 125
		f 4 -177 -176 -175 172
		mu 0 4 127 129 128 126
		f 4 -180 -179 -178 175
		mu 0 4 129 131 130 128
		f 4 171 -183 -182 -181
		mu 0 4 125 126 133 132
		f 4 174 -185 -184 182
		mu 0 4 126 128 134 133
		f 4 177 -187 -186 184
		mu 0 4 128 130 135 134
		f 4 181 -190 -189 -188
		mu 0 4 132 133 137 136
		f 4 -193 -192 185 -191
		mu 0 4 138 139 134 135
		f 4 -197 -196 -195 -194
		mu 0 4 140 143 142 141
		f 4 -201 -200 -199 -198
		mu 0 4 144 147 146 145
		f 4 -204 -203 -202 199
		mu 0 4 147 149 148 146
		f 4 -207 -206 -205 202
		mu 0 4 149 151 150 148
		f 4 204 -210 -209 -208
		mu 0 4 148 150 153 152
		f 4 -212 -211 186 178
		mu 0 4 154 157 156 155
		f 4 -215 -214 -213 197
		mu 0 4 158 161 160 159
		f 4 -218 -217 -216 187
		mu 0 4 162 165 164 163
		f 4 215 -219 170 180
		mu 0 4 163 164 167 166
		f 4 -222 -221 205 -220
		mu 0 4 168 171 170 169
		f 4 212 -225 -224 -223
		mu 0 4 159 160 173 172
		f 4 -227 -226 200 222
		mu 0 4 174 175 147 144
		f 4 -229 -228 203 225
		mu 0 4 175 138 149 147
		f 4 -233 -232 -231 -230
		mu 0 4 176 179 178 177
		f 4 0 -235 173 -234
		mu 0 4 180 181 127 124
		f 4 1 -236 176 234
		mu 0 4 181 182 129 127
		f 4 2 -237 179 235
		mu 0 4 182 183 131 129
		f 4 -238 211 236 3
		mu 0 4 184 157 154 185
		f 4 213 -242 4 -241
		mu 0 4 160 161 193 192
		f 4 224 240 5 -243
		mu 0 4 173 160 192 194
		f 4 216 -245 6 -244
		mu 0 4 164 165 196 195
		f 4 218 243 7 233
		mu 0 4 167 164 195 197
		f 4 190 -247 195 -246
		mu 0 4 138 135 198 143
		f 4 246 210 -249 -248
		mu 0 4 142 156 157 199
		f 4 229 250 -254 -253
		mu 0 4 204 177 201 205
		f 4 245 -256 252 -255
		mu 0 4 138 143 176 206
		f 5 -259 249 230 -258 -257
		mu 0 5 207 200 177 178 208
		f 4 255 196 -260 232
		mu 0 4 176 143 209 179
		f 4 -263 -262 256 -261
		mu 0 4 210 199 207 208
		f 4 -264 248 237 8
		mu 0 4 211 199 157 184
		f 4 -266 -265 263 9
		mu 0 4 212 213 199 211
		f 4 -268 -267 265 10
		mu 0 4 214 215 213 212
		f 4 -269 194 247 262
		mu 0 4 210 141 142 199
		f 4 253 269 219 -271
		mu 0 4 205 201 168 169
		f 4 254 270 206 227
		mu 0 4 138 206 151 149
		f 3 261 264 -273
		mu 0 3 207 199 213
		f 4 266 -274 258 272
		mu 0 4 213 215 200 207
		f 4 -276 -275 251 273
		mu 0 4 215 218 203 200
		f 4 271 274 -277 11
		mu 0 4 217 203 218 219
		f 4 276 275 267 12
		mu 0 4 219 218 215 214
		f 4 208 -280 13 -279
		mu 0 4 152 153 224 223
		f 4 209 220 238 -281
		mu 0 4 153 150 190 191
		f 5 279 280 239 277 14
		mu 0 5 224 153 191 188 221
		f 4 -282 189 183 191
		mu 0 4 139 137 133 134
		f 4 228 -283 281 192
		mu 0 4 138 175 137 139
		f 4 188 282 226 -284
		mu 0 4 136 137 175 174
		f 4 223 -285 217 283
		mu 0 4 172 173 165 162
		f 4 244 284 242 15
		mu 0 4 196 165 173 194
		f 4 -289 -288 -287 -286
		mu 0 4 225 228 227 226
		f 4 -292 285 -291 -290
		mu 0 4 229 225 226 230
		f 4 -295 -294 -293 287
		mu 0 4 228 232 231 227
		f 4 -298 -297 -296 293
		mu 0 4 232 234 233 231
		f 4 -300 16 -299 296
		mu 0 4 234 236 235 233
		f 4 -302 289 -301 17
		mu 0 4 237 229 230 238
		f 4 286 -305 -304 -303
		mu 0 4 226 227 240 239
		f 4 290 302 -307 -306
		mu 0 4 230 226 239 241
		f 4 292 -309 -308 304
		mu 0 4 227 231 242 240
		f 4 295 -311 -310 308
		mu 0 4 231 233 243 242
		f 4 298 18 -312 310
		mu 0 4 233 235 244 243
		f 4 300 305 -313 19
		mu 0 4 238 230 241 245
		f 4 241 -314 301 20
		mu 0 4 246 247 229 237
		f 4 214 -315 291 313
		mu 0 4 247 145 225 229
		f 4 198 -316 288 314
		mu 0 4 145 146 228 225
		f 4 201 -317 294 315
		mu 0 4 146 148 232 228
		f 4 207 -318 297 316
		mu 0 4 148 152 234 232
		f 4 278 21 299 317
		mu 0 4 152 223 236 234
		f 4 -322 -74 318 -326
		mu 0 4 62 44 47 63
		f 4 91 -329 -319 90
		mu 0 4 67 64 65 66
		f 5 101 102 319 320 103
		mu 0 5 76 53 77 78 79
		f 4 -325 -320 121 321
		mu 0 4 62 78 77 44
		f 4 322 123 -321 -324
		mu 0 4 92 93 79 78
		f 4 129 -340 327 328
		mu 0 4 64 97 98 65
		f 4 329 -332 221 330
		mu 0 4 186 187 171 168
		f 4 332 -240 -239 331
		mu 0 4 189 188 191 190
		f 5 -251 -250 -252 -336 -334
		mu 0 5 201 177 200 203 202
		f 4 333 -337 -331 -270
		mu 0 4 201 202 186 168
		f 4 334 335 -272 -323
		mu 0 4 216 202 203 217
		f 4 339 -278 -333 -339
		mu 0 4 222 221 188 189
		f 4 -341 325 -328 326
		mu 0 4 249 62 63 96
		f 4 341 -327 338 -330
		mu 0 4 186 251 220 187
		f 4 -343 336 -335 337
		mu 0 4 250 186 202 216
		f 4 343 -338 323 324
		mu 0 4 62 248 92 78
		f 4 -46 344 346 -346
		mu 0 4 252 253 254 255
		f 4 -84 347 349 -349
		mu 0 4 256 257 258 259
		f 4 -110 350 351 -348
		mu 0 4 260 261 262 263
		f 4 -112 348 352 -345
		mu 0 4 264 265 266 267
		f 4 -113 353 354 -351
		mu 0 4 268 269 270 271
		f 4 -121 345 355 -354
		mu 0 4 272 273 274 275
		f 4 193 357 -359 -357
		mu 0 4 276 277 278 279
		f 4 231 360 -362 -360
		mu 0 4 280 281 282 283
		f 4 257 359 -364 -363
		mu 0 4 284 285 286 287
		f 4 259 356 -365 -361
		mu 0 4 288 289 290 291
		f 4 260 362 -367 -366
		mu 0 4 292 293 294 295
		f 4 268 365 -368 -358
		mu 0 4 296 297 298 299
		f 4 340 369 -371 -369
		mu 0 4 300 301 302 303
		f 4 -342 371 372 -370
		mu 0 4 304 305 306 307
		f 4 342 373 -375 -372
		mu 0 4 308 309 310 311
		f 4 -344 368 375 -374
		mu 0 4 312 313 314 315
		f 4 370 377 -379 -377
		mu 0 4 316 317 318 319
		f 4 -373 379 380 -378
		mu 0 4 320 321 322 323
		f 4 374 381 -383 -380
		mu 0 4 324 325 326 327
		f 4 -376 376 383 -382
		mu 0 4 328 329 330 331
		f 4 -347 384 386 -386
		mu 0 4 332 333 334 335
		f 4 -350 387 389 -389
		mu 0 4 336 337 338 339
		f 4 -352 390 391 -388
		mu 0 4 340 341 342 343
		f 4 -353 388 392 -385
		mu 0 4 344 345 346 347
		f 4 -355 393 394 -391
		mu 0 4 348 349 350 351
		f 4 -356 385 395 -394
		mu 0 4 352 353 354 355
		f 4 358 397 -399 -397
		mu 0 4 356 357 358 359
		f 4 361 400 -402 -400
		mu 0 4 360 361 362 363
		f 4 363 399 -404 -403
		mu 0 4 364 365 366 367
		f 4 364 396 -405 -401
		mu 0 4 368 369 370 371
		f 4 366 402 -407 -406
		mu 0 4 372 373 374 375
		f 4 367 405 -408 -398
		mu 0 4 376 377 378 379;
	setAttr ".cd" -type "dataPolyComponent" Index_Data Edge 0 ;
	setAttr ".cvd" -type "dataPolyComponent" Index_Data Vertex 0 ;
	setAttr ".hfd" -type "dataPolyComponent" Index_Data Face 0 ;
	setAttr ".dr" 1;
createNode transform -n "guidesGroup";
createNode transform -n "eyeLidMainLocators" -p "guidesGroup";
createNode transform -n "eyeLidMainLoc0" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.088338077068328857 0.56888771057128906 0.32347768545150757 ;
createNode locator -n "eyeLidMainLocShape0" -p "eyeLidMainLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc1" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.1008603423833847 0.59515684843063354 0.3289172351360321 ;
createNode locator -n "eyeLidMainLocShape1" -p "eyeLidMainLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc2" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.13372163700318332 0.59726653406164021 0.32594006670019848 ;
createNode locator -n "eyeLidMainLocShape2" -p "eyeLidMainLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc3" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.16362132132053375 0.59897917509078979 0.31828978657722473 ;
createNode locator -n "eyeLidMainLocShape3" -p "eyeLidMainLoc3";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc4" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.19461216032505035 0.58177328109741211 0.30044412612915039 ;
createNode locator -n "eyeLidMainLocShape4" -p "eyeLidMainLoc4";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc5" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.16013789176940918 0.56083798408508301 0.30819064378738403 ;
createNode locator -n "eyeLidMainLocShape5" -p "eyeLidMainLoc5";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc6" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.1321039998074853 0.559962377432074 0.31532750895471895 ;
createNode locator -n "eyeLidMainLocShape6" -p "eyeLidMainLoc6";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc7" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.10059068351984024 0.55878716707229614 0.32003903388977051 ;
createNode locator -n "eyeLidMainLocShape7" -p "eyeLidMainLoc7";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainCurve" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "eyeLidMainCurveShape" -p "eyeLidMainCurve";
	setAttr -k off ".v";
	setAttr -s 9 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 8 0 no 3
		9 0 1 2 3 4 5 6 7 8
		9
		-0.088338077068328857 0.56888771057128906 0.32347768545150757
		-0.1008603423833847 0.59515684843063354 0.3289172351360321
		-0.13372163700318332 0.59726653406164021 0.32594006670019848
		-0.16362132132053375 0.59897917509078979 0.31828978657722473
		-0.19461216032505035 0.58177328109741211 0.30044412612915039
		-0.16013789176940918 0.56083798408508301 0.30819064378738403
		-0.1321039998074853 0.559962377432074 0.31532750895471895
		-0.10059068351984024 0.55878716707229614 0.32003903388977051
		-0.088338077068328857 0.56888771057128906 0.32347768545150757
		;
createNode transform -n "EyeBrowMainLocators" -p "guidesGroup";
createNode transform -n "eyeBrowMainLoc0" -p "EyeBrowMainLocators";
	setAttr ".t" -type "double3" -0.11964528528160934 0.69471410285557145 0.34872346146226857 ;
createNode locator -n "eyeBrowMainLocShape0" -p "eyeBrowMainLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeBrowMainLoc1" -p "EyeBrowMainLocators";
	setAttr ".t" -type "double3" -0.16677600006220683 0.69079386151000466 0.31405728427841828 ;
createNode locator -n "eyeBrowMainLocShape1" -p "eyeBrowMainLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeBrowMainLoc2" -p "EyeBrowMainLocators";
	setAttr ".t" -type "double3" -0.18341155479934435 0.69252091272631622 0.29018675976442992 ;
createNode locator -n "eyeBrowMainLocShape2" -p "eyeBrowMainLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeBrowMainLoc3" -p "EyeBrowMainLocators";
	setAttr ".t" -type "double3" -0.20733055472373962 0.69119304418563843 0.27253681421279907 ;
createNode locator -n "eyeBrowMainLocShape3" -p "eyeBrowMainLoc3";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "EyeBrowMainCurve2" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "EyeBrowMainCurve2Shape" -p "EyeBrowMainCurve2";
	setAttr -k off ".v";
	setAttr -s 4 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 3 0 no 3
		4 0 1 2 3
		4
		-0.11964528528160934 0.69471410285557145 0.34872346146226857
		-0.16677600006220683 0.69079386151000466 0.31405728427841828
		-0.18341155479934435 0.69252091272631622 0.29018675976442992
		-0.20733055472373962 0.69119304418563843 0.27253681421279907
		;
createNode transform -n "lipMainLoc0" -p "guidesGroup";
	setAttr ".t" -type "double3" -0.098746120929718018 0.32888093590736389 0.35446476936340332 ;
createNode locator -n "lipMainLocShape0" -p "lipMainLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "upperLipMainLocators" -p "guidesGroup";
createNode transform -n "upperLipMainLoc0" -p "upperLipMainLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.35392081737518311 0.40634408593177795 ;
	setAttr -l on ".tx";
createNode locator -n "upperLipMainLocShape0" -p "upperLipMainLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "upperLipMainLoc1" -p "upperLipMainLocators";
	setAttr ".t" -type "double3" -0.057255734797981392 0.34119315760221069 0.38194533762548633 ;
createNode locator -n "upperLipMainLocShape1" -p "upperLipMainLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "upperLipMainCurve2" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "upperLipMainCurve2Shape" -p "upperLipMainCurve2";
	setAttr -k off ".v";
	setAttr -s 3 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 2 0 no 3
		3 0 1 2
		3
		-4.3166974595447286e-031 0.35392081737518311 0.40634408593177795
		-0.057255734797981392 0.34119315760221069 0.38194533762548633
		-0.098746120929718018 0.32888093590736389 0.35446476936340332
		;
createNode transform -n "lowerLipMainLocators" -p "guidesGroup";
createNode transform -n "lowerLipMainLoc0" -p "lowerLipMainLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.29215830564498901 0.40330794453620911 ;
	setAttr -l on ".tx";
createNode locator -n "lowerLipMainLocShape0" -p "lowerLipMainLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "lowerLipMainLoc1" -p "lowerLipMainLocators";
	setAttr ".t" -type "double3" -0.057531770895419526 0.31274962206616885 0.3771039679684452 ;
createNode locator -n "lowerLipMainLocShape1" -p "lowerLipMainLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "lowerLipMainCurve2" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "lowerLipMainCurve2Shape" -p "lowerLipMainCurve2";
	setAttr -k off ".v";
	setAttr -s 3 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 2 0 no 3
		3 2 3 4
		3
		-4.3166974595447286e-031 0.29215830564498901 0.40330794453620911
		-0.057531770895419526 0.31274962206616885 0.3771039679684452
		-0.098746120929718018 0.32888093590736389 0.35446476936340332
		;
createNode transform -n "JawMidLocators" -p "guidesGroup";
createNode transform -n "JawMidLoc1" -p "JawMidLocators";
	setAttr ".t" -type "double3" -0.19405116961595231 0.370618 0.278368 ;
createNode locator -n "JawMidLocShape1" -p "JawMidLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "JawMidLoc2" -p "JawMidLocators";
	setAttr ".t" -type "double3" -0.24360564864096021 0.492695 0.064690399999999995 ;
createNode locator -n "JawMidLocShape2" -p "JawMidLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "JawCurveMid" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "JawCurveMidShape" -p "JawCurveMid";
	setAttr -k off ".v";
	setAttr -s 3 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 2 0 no 3
		3 0 1 2
		3
		-0.098746120929718018 0.32888093590736389 0.35446476936340332
		-0.19405116961595231 0.370618 0.278368
		-0.24360564864096021 0.49269499999999999 0.064690399999999995
		;
createNode transform -n "EarCurve" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "EarCurveShape" -p "EarCurve";
	setAttr -k off ".v";
	setAttr -s 6 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 5 0 no 3
		6 0 1 2 3 4 5
		6
		-0.28487307884573476 0.61813026957891282 0.029260069824447843
		-0.31443713254985783 0.64052634421383869 -0.023455756289421814
		-0.31914219592268112 0.55197872666356074 -0.071506367101106028
		-0.29751817214246168 0.48108986741743354 -0.065474884841416392
		-0.2768661545428921 0.43016985784286804 -0.027643528233528059
		-0.24761097836467727 0.4206039098663612 0.01122786657782765
		;
createNode transform -n "earLocators" -p "guidesGroup";
createNode transform -n "EarLoc0" -p "earLocators";
	setAttr ".t" -type "double3" -0.28487307884573476 0.61813026957891282 0.029260069824447843 ;
createNode locator -n "EarLocShape0" -p "EarLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "EarLoc1" -p "earLocators";
	setAttr ".t" -type "double3" -0.31443713254985783 0.64052634421383869 -0.023455756289421814 ;
createNode locator -n "EarLocShape1" -p "EarLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "EarLoc2" -p "earLocators";
	setAttr ".t" -type "double3" -0.31914219592268112 0.55197872666356074 -0.071506367101106028 ;
createNode locator -n "EarLocShape2" -p "EarLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "EarLoc3" -p "earLocators";
	setAttr ".t" -type "double3" -0.29751817214246168 0.48108986741743354 -0.065474884841416392 ;
createNode locator -n "EarLocShape3" -p "EarLoc3";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "EarLoc4" -p "earLocators";
	setAttr ".t" -type "double3" -0.2768661545428921 0.43016985784286804 -0.027643528233528059 ;
createNode locator -n "EarLocShape4" -p "EarLoc4";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "EarLoc5" -p "earLocators";
	setAttr ".t" -type "double3" -0.24761097836467727 0.4206039098663612 0.01122786657782765 ;
createNode locator -n "EarLocShape5" -p "EarLoc5";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideLocators" -p "guidesGroup";
createNode transform -n "HeadProfileSideLoc0" -p "HeadProfileSideLocators";
	setAttr ".t" -type "double3" 0 0.880152 0.323072 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc0Shape" -p "HeadProfileSideLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideLoc1" -p "HeadProfileSideLocators";
	setAttr ".t" -type "double3" 0 0.99209 0.129988 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc1Shape" -p "HeadProfileSideLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideLoc2" -p "HeadProfileSideLocators";
	setAttr ".t" -type "double3" 0 0.959558 -0.142109 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc2Shape" -p "HeadProfileSideLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideLoc3" -p "HeadProfileSideLocators";
	setAttr ".t" -type "double3" 0 0.748149 -0.317261 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc3Shape" -p "HeadProfileSideLoc3";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideLoc4" -p "HeadProfileSideLocators";
	setAttr ".t" -type "double3" 0 0.51268099107915721 -0.316899 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc4Shape" -p "HeadProfileSideLoc4";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideLoc5" -p "HeadProfileSideLocators";
	setAttr ".t" -type "double3" 0 0.276872 -0.252859 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc5Shape" -p "HeadProfileSideLoc5";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideLoc6" -p "HeadProfileSideLocators";
	setAttr ".ove" yes;
	setAttr ".ovc" 24;
	setAttr ".t" -type "double3" 0 -0.0015766379390291657 -0.25956778547015907 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc6Shape" -p "HeadProfileSideLoc6";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideCurve" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "HeadProfileSideCurveShape" -p "HeadProfileSideCurve";
	setAttr -k off ".v";
	setAttr -s 14 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 6 0 no 3
		7 10 11 12 13 14 15 16
		7
		0 0.88015200000000005 0.32307200000000003
		0 0.99209000000000003 0.12998799999999999
		0 0.95955800000000002 -0.14210900000000001
		0 0.74814899999999995 -0.31726100000000002
		0 0.51268099107915721 -0.31689899999999999
		0 0.27687200000000001 -0.252859
		0 -0.0015766379390291657 -0.25956778547015907
		;
createNode transform -n "HeadProfileFrontLocators" -p "guidesGroup";
createNode transform -n "HeadProfileFrontLoc0" -p "HeadProfileFrontLocators";
	setAttr ".ove" yes;
	setAttr ".ovc" 24;
	setAttr ".t" -type "double3" -0.29883210929073445 -1.7868613137794753e-005 -0.066659166097037786 ;
createNode locator -n "HeadProfileFrontLocShape0" -p "HeadProfileFrontLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileFrontLoc1" -p "HeadProfileFrontLocators";
	setAttr ".t" -type "double3" -0.17909383752868618 0.22168673178114931 -0.08946714831203019 ;
createNode locator -n "HeadProfileFrontLocShape1" -p "HeadProfileFrontLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileFrontLoc2" -p "HeadProfileFrontLocators";
	setAttr ".t" -type "double3" -0.22719205125860586 0.416058 -0.089205183702067578 ;
createNode locator -n "HeadProfileFrontLocShape2" -p "HeadProfileFrontLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileFrontLoc3" -p "HeadProfileFrontLocators";
	setAttr ".t" -type "double3" -0.27848612475975332 0.71477004470314176 -0.087789228405209349 ;
createNode locator -n "HeadProfileFrontLocShape3" -p "HeadProfileFrontLoc3";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileFrontLoc4" -p "HeadProfileFrontLocators";
	setAttr ".t" -type "double3" -0.23633245244708026 0.89708714908410925 -0.066549898952336414 ;
createNode locator -n "HeadProfileFrontLocShape4" -p "HeadProfileFrontLoc4";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileFrontLoc5" -p "HeadProfileFrontLocators";
	setAttr ".t" -type "double3" 0.00016708899388481746 0.99844673920058802 0 ;
createNode locator -n "HeadProfileFrontLocShape5" -p "HeadProfileFrontLoc5";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileFrontCurve" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "HeadProfileFrontCurveShape" -p "HeadProfileFrontCurve";
	setAttr -k off ".v";
	setAttr -s 6 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 5 0 no 3
		6 0 1 2 3 4 5
		6
		-0.29883210929073445 -1.7868613137794753e-005 -0.066659166097037786
		-0.17909383752868618 0.22168673178114931 -0.08946714831203019
		-0.22719205125860586 0.41605799999999998 -0.089205183702067578
		-0.27848612475975332 0.71477004470314176 -0.087789228405209349
		-0.23633245244708026 0.89708714908410925 -0.066549898952336414
		0.00016708899388481746 0.99844673920058802 0
		;
createNode transform -n "noseProfileLocators" -p "guidesGroup";
createNode transform -n "noseProfileLoc0" -p "noseProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.59256274264565978 0.40175503269210355 ;
	setAttr -l on ".tx";
createNode locator -n "noseProfileLocShape0" -p "noseProfileLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "noseProfileLoc1" -p "noseProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.51690531051559518 0.41189327429844147 ;
	setAttr -l on ".tx";
createNode locator -n "noseProfileLocShape1" -p "noseProfileLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "noseProfileLoc2" -p "noseProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.45753766538735352 0.45509901697525296 ;
	setAttr -l on ".tx";
createNode locator -n "noseProfileLocShape2" -p "noseProfileLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "noseProfileLoc3" -p "noseProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.42325494073583364 0.45509901697525301 ;
	setAttr -l on ".tx";
createNode locator -n "noseProfileLocShape3" -p "noseProfileLoc3";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "noseProfileLoc4" -p "noseProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.404023 0.400187 ;
	setAttr -l on ".tx";
createNode locator -n "noseProfileLocShape4" -p "noseProfileLoc4";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "noseProfileCurve" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "noseProfileCurveShape" -p "noseProfileCurve";
	setAttr -k off ".v";
	setAttr -s 5 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 4 0 no 3
		5 0 1 2 3 4
		5
		-4.3166974595447286e-031 0.59256274264565978 0.40175503269210355
		-4.3166974595447286e-031 0.51690531051559518 0.41189327429844147
		-4.3166974595447286e-031 0.45753766538735352 0.45509901697525296
		-4.3166974595447286e-031 0.42325494073583364 0.45509901697525301
		-4.3166974595447286e-031 0.40402300000000002 0.40018700000000001
		;
createNode transform -n "chinProfileLocators" -p "guidesGroup";
createNode transform -n "chinProfileLoc0" -p "chinProfileLocators";
	setAttr ".ove" yes;
	setAttr ".ovc" 24;
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.00101735 0.164808 ;
	setAttr -l on ".tx";
createNode locator -n "chinProfileLocShape0" -p "chinProfileLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "chinProfileLoc1" -p "chinProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.14832178560001505 0.16677935929288523 ;
	setAttr -l on ".tx";
createNode locator -n "chinProfileLocShape1" -p "chinProfileLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "chinProfileLoc2" -p "chinProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.18357960410832388 0.36817051780713467 ;
	setAttr -l on ".tx";
createNode locator -n "chinProfileLocShape2" -p "chinProfileLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "chinProfileCurve" -p "guidesGroup";
	setAttr ".it" no;
createNode nurbsCurve -n "chinProfileCurveShape" -p "chinProfileCurve";
	setAttr -k off ".v";
	setAttr -s 3 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 2 0 no 3
		3 0 1 2
		3
		-4.3166974595447286e-031 0.00101735 0.16480800000000001
		-4.3166974595447286e-031 0.14832178560001505 0.16677935929288523
		-4.3166974595447286e-031 0.18357960410832388 0.36817051780713467
		;
createNode transform -n "FaceGroup";
	setAttr -l on ".it" no;
createNode transform -n "FaceFitSkeleton" -p "FaceGroup";
	addAttr -ci true -sn "Geometry" -ln "Geometry" -dt "string";
	addAttr -ci true -sn "AllFaceGeo" -ln "AllFaceGeo" -dt "string";
	addAttr -ci true -sn "Eye_R" -ln "Eye_R" -dt "string";
	addAttr -ci true -sn "Eye_L" -ln "Eye_L" -dt "string";
	addAttr -ci true -sn "UpperTeeth" -ln "UpperTeeth" -dt "string";
	addAttr -ci true -sn "LowerTeeth" -ln "LowerTeeth" -dt "string";
	addAttr -ci true -sn "Tongue" -ln "Tongue" -dt "string";
	addAttr -ci true -sn "HeadJoint" -ln "HeadJoint" -dt "string";
	addAttr -ci true -sn "SkinCluster" -ln "SkinCluster" -dt "string";
	addAttr -ci true -sn "Pupil_R" -ln "Pupil_R" -dt "string";
	addAttr -ci true -sn "Pupil_L" -ln "Pupil_L" -dt "string";
	addAttr -ci true -sn "Iris_R" -ln "Iris_R" -dt "string";
	addAttr -ci true -sn "Iris_L" -ln "Iris_L" -dt "string";
	setAttr ".t" -type "double3" 0 0.19142804567901181 0 ;
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr ".s" -type "double3" 0.5373787352311199 0.5373787352311199 0.5373787352311199 ;
	setAttr -k off ".sx";
	setAttr ".Geometry" -type "string" "headTopology";
	setAttr ".AllFaceGeo" -type "string" "headTopology";
	setAttr ".Eye_R" -type "string" "";
	setAttr ".Eye_L" -type "string" "";
	setAttr ".UpperTeeth" -type "string" "";
	setAttr ".LowerTeeth" -type "string" "";
	setAttr ".Tongue" -type "string" "";
	setAttr ".HeadJoint" -type "string" "Head_M";
	setAttr ".SkinCluster" -type "string" "skinCluster1";
	setAttr ".Pupil_R" -type "string" "eyeball_R.e[942] eyeball_R.e[944] eyeball_R.e[946] eyeball_R.e[948] eyeball_R.e[950] eyeball_R.e[952] eyeball_R.e[954] eyeball_R.e[956] eyeball_R.e[958] eyeball_R.e[960] eyeball_R.e[962] eyeball_R.e[964] eyeball_R.e[966] eyeball_R.e[968] eyeball_R.e[970] eyeball_R.e[972] eyeball_R.e[974] eyeball_R.e[976] eyeball_R.e[978:979]";
	setAttr ".Pupil_L" -type "string" "eyeball_L.e[942] eyeball_L.e[944] eyeball_L.e[946] eyeball_L.e[948] eyeball_L.e[950] eyeball_L.e[952] eyeball_L.e[954] eyeball_L.e[956] eyeball_L.e[958] eyeball_L.e[960] eyeball_L.e[962] eyeball_L.e[964] eyeball_L.e[966] eyeball_L.e[968] eyeball_L.e[970] eyeball_L.e[972] eyeball_L.e[974] eyeball_L.e[976] eyeball_L.e[978:979]";
	setAttr ".Iris_R" -type "string" "eyeball_R.e[862] eyeball_R.e[864] eyeball_R.e[866] eyeball_R.e[868] eyeball_R.e[870] eyeball_R.e[872] eyeball_R.e[874] eyeball_R.e[876] eyeball_R.e[878] eyeball_R.e[880] eyeball_R.e[882] eyeball_R.e[884] eyeball_R.e[886] eyeball_R.e[888] eyeball_R.e[890] eyeball_R.e[892] eyeball_R.e[894] eyeball_R.e[896] eyeball_R.e[898:899]";
	setAttr ".Iris_L" -type "string" "eyeball_L.e[862] eyeball_L.e[864] eyeball_L.e[866] eyeball_L.e[868] eyeball_L.e[870] eyeball_L.e[872] eyeball_L.e[874] eyeball_L.e[876] eyeball_L.e[878] eyeball_L.e[880] eyeball_L.e[882] eyeball_L.e[884] eyeball_L.e[886] eyeball_L.e[888] eyeball_L.e[890] eyeball_L.e[892] eyeball_L.e[894] eyeball_L.e[896] eyeball_L.e[898:899]";
createNode nurbsCurve -n "FaceFitSkeletonShape" -p "FaceFitSkeleton";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 13;
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.39180581244561252 2.3991186704942341e-017 -0.39180581244561191
		-6.321585303914663e-017 3.3928661615554567e-017 -0.55409709377719396
		-0.39180581244561213 2.3991186704942356e-017 -0.39180581244561213
		-0.55409709377719396 9.8316773080939295e-033 -1.605634753618615e-016
		-0.39180581244561224 -2.3991186704942347e-017 0.39180581244561202
		-1.6696026817952597e-016 -3.3928661615554573e-017 0.55409709377719407
		0.39180581244561191 -2.399118670494236e-017 0.39180581244561219
		0.55409709377719396 -1.8223150339523961e-032 2.9760662996402926e-016
		0.39180581244561252 2.3991186704942341e-017 -0.39180581244561191
		-6.321585303914663e-017 3.3928661615554567e-017 -0.55409709377719396
		-0.39180581244561213 2.3991186704942356e-017 -0.39180581244561213
		;
createNode nurbsCurve -n "FaceFitSkeletonHeightShape" -p "FaceFitSkeleton";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 13;
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.39180581244561252 1.5 -0.39180581244561191
		-6.321585303914663e-017 1.5 -0.55409709377719396
		-0.39180581244561213 1.5 -0.39180581244561213
		-0.55409709377719396 1.5 -1.605634753618615e-016
		-0.39180581244561224 1.5 0.39180581244561202
		-1.6696026817952597e-016 1.5 0.55409709377719407
		0.39180581244561191 1.5 0.39180581244561219
		0.55409709377719396 1.5 2.9760662996402926e-016
		0.39180581244561252 1.5 -0.39180581244561191
		-6.321585303914663e-017 1.5 -0.55409709377719396
		-0.39180581244561213 1.5 -0.39180581244561213
		;
createNode transform -n "FaceFitJawPivot" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[35] ";
createNode transform -n "FaceFitJawPivotGeo" -p "FaceFitJawPivot";
createNode transform -n "FaceFitJawPivotCurve" -p "FaceFitJawPivot";
	setAttr ".it" no;
createNode transform -n "FaceFitJawPivotLoc" -p "FaceFitJawPivot";
createNode transform -n "JawPivot" -p "FaceFitJawPivot";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.49162634465313126 0.62555809587620537 0.035620679164037806 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "JawPivotShape" -p "JawPivot";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "JawPivotSphere" -p "FaceFitJawPivot";
createNode nurbsSurface -n "JawPivotSphereShape" -p "JawPivotSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitJawCorner" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[40] ";
createNode transform -n "FaceFitJawCornerGeo" -p "FaceFitJawCorner";
createNode transform -n "FaceFitJawCornerCurve" -p "FaceFitJawCorner";
	setAttr ".it" no;
createNode transform -n "FaceFitJawCornerLoc" -p "FaceFitJawCorner";
createNode transform -n "JawCorner" -p "FaceFitJawCorner";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.37269131915299802 0.17277693652099574 0.13171502016443071 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "JawCornerShape" -p "JawCorner";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "JawCornerSphere" -p "FaceFitJawCorner";
createNode nurbsSurface -n "JawCornerSphereShape" -p "JawCornerSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitJaw" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[17] ";
createNode transform -n "FaceFitJawGeo" -p "FaceFitJaw";
createNode transform -n "FaceFitJawCurve" -p "FaceFitJaw";
	setAttr ".it" no;
createNode transform -n "FaceFitJawLoc" -p "FaceFitJaw";
createNode transform -n "Jaw" -p "FaceFitJaw";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" 0 -0.01807109546564839 0.68202113682980647 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "JawShape" -p "Jaw";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "JawSphere" -p "FaceFitJaw";
createNode nurbsSurface -n "JawSphereShape" -p "JawSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitThroat" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[5] ";
createNode transform -n "FaceFitThroatGeo" -p "FaceFitThroat";
createNode transform -n "FaceFitThroatCurve" -p "FaceFitThroat";
	setAttr ".it" no;
createNode transform -n "FaceFitThroatLoc" -p "FaceFitThroat";
createNode transform -n "Throat" -p "FaceFitThroat";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" 0 -0.080433259020447956 0.31541028106548419 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "ThroatShape" -p "Throat";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "ThroatSphere" -p "FaceFitThroat";
createNode nurbsSurface -n "ThroatSphereShape" -p "ThroatSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitNose" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[13] ";
createNode transform -n "FaceFitNoseGeo" -p "FaceFitNose";
createNode transform -n "FaceFitNoseCurve" -p "FaceFitNose";
	setAttr ".it" no;
createNode transform -n "FaceFitNoseLoc" -p "FaceFitNose";
createNode transform -n "Nose" -p "FaceFitNose";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" 0 0.47350907618530563 0.85531047516321823 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "NoseShape" -p "Nose";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "NoseSphere" -p "FaceFitNose";
createNode nurbsSurface -n "NoseSphereShape" -p "NoseSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitNoseUnder" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[15] ";
createNode transform -n "FaceFitNoseUnderGeo" -p "FaceFitNoseUnder";
createNode transform -n "FaceFitNoseUnderCurve" -p "FaceFitNoseUnder";
	setAttr ".it" no;
createNode transform -n "FaceFitNoseUnderLoc" -p "FaceFitNoseUnder";
createNode transform -n "NoseUnder" -p "FaceFitNoseUnder";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" 0 0.41094385062550204 0.75833673531291668 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "NoseUnderShape" -p "NoseUnder";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "NoseUnderSphere" -p "FaceFitNoseUnder";
createNode nurbsSurface -n "NoseUnderSphereShape" -p "NoseUnderSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitCheek" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[42] ";
createNode transform -n "FaceFitCheekGeo" -p "FaceFitCheek";
createNode transform -n "FaceFitCheekCurve" -p "FaceFitCheek";
	setAttr ".it" no;
createNode transform -n "FaceFitCheekLoc" -p "FaceFitCheek";
createNode transform -n "Cheek" -p "FaceFitCheek";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.34491463857856886 0.28846066476517701 0.45689796870971322 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "CheekShape" -p "Cheek";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "CheekSphere" -p "FaceFitCheek";
createNode nurbsSurface -n "CheekSphereShape" -p "CheekSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitCheekRaiser" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[61] ";
createNode transform -n "FaceFitCheekRaiserGeo" -p "FaceFitCheekRaiser";
createNode transform -n "FaceFitCheekRaiserCurve" -p "FaceFitCheekRaiser";
	setAttr ".it" no;
createNode transform -n "FaceFitCheekRaiserLoc" -p "FaceFitCheekRaiser";
createNode transform -n "CheekRaiser" -p "FaceFitCheekRaiser";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.34709366609150405 0.59629046343875025 0.57993532556143601 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "CheekRaiserShape" -p "CheekRaiser";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "CheekRaiserSphere" -p "FaceFitCheekRaiser";
createNode nurbsSurface -n "CheekRaiserSphereShape" -p "CheekRaiserSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitNoseCorner" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[76] ";
createNode transform -n "FaceFitNoseCornerGeo" -p "FaceFitNoseCorner";
createNode transform -n "FaceFitNoseCornerCurve" -p "FaceFitNoseCorner";
	setAttr ".it" no;
createNode transform -n "FaceFitNoseCornerLoc" -p "FaceFitNoseCorner";
createNode transform -n "NoseCorner" -p "FaceFitNoseCorner";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.10503458464535141 0.42114342526056997 0.71912316892968164 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "NoseCornerShape" -p "NoseCorner";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "NoseCornerSphere" -p "FaceFitNoseCorner";
createNode nurbsSurface -n "NoseCornerSphereShape" -p "NoseCornerSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitLine" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[51] ";
createNode transform -n "FaceFitLineGeo" -p "FaceFitLine";
createNode transform -n "FaceFitLineCurve" -p "FaceFitLine";
	setAttr ".it" no;
createNode transform -n "FaceFitLineLoc" -p "FaceFitLine";
createNode transform -n "Line" -p "FaceFitLine";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.25625260398948807 0.26010446857657615 0.58908068589823248 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "LineShape" -p "Line";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "LineSphere" -p "FaceFitLine";
createNode nurbsSurface -n "LineSphereShape" -p "LineSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitLipOuter" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.e[340] headTopology.e[341] headTopology.e[342] headTopology.e[343] ";
createNode transform -n "FaceFitLipOuterGeo" -p "FaceFitLipOuter";
	setAttr ".it" no;
createNode transform -n "upperLipCylinderOuter" -p "FaceFitLipOuterGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "upperLipCylinderOuterShape" -p "upperLipCylinderOuter";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "lowerLipCylinderOuter" -p "FaceFitLipOuterGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "lowerLipCylinderOuterShape" -p "lowerLipCylinderOuter";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "FaceFitLipOuterCurve" -p "FaceFitLipOuter";
	setAttr ".it" no;
createNode transform -n "upperLipOuterCurve" -p "FaceFitLipOuterCurve";
createNode nurbsCurve -n "upperLipOuterCurveShape" -p "upperLipOuterCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 14;
	setAttr ".cc" -type "nurbsCurve" 
		1 1 0 no 3
		2 0 1
		2
		-6.7640476709999996e-019 0.36057907340000001 0.41193687919999999
		-0.1200366244 0.33014038210000002 0.34887194630000001
		;
createNode transform -n "lowerLipOuterCurve" -p "FaceFitLipOuterCurve";
createNode nurbsCurve -n "lowerLipOuterCurveShape" -p "lowerLipOuterCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 14;
	setAttr ".cc" -type "nurbsCurve" 
		1 1 0 no 3
		2 0 1
		2
		-2.4260007949999999e-018 0.28550004960000003 0.40824612980000002
		-0.1200366244 0.33014038210000002 0.34887194630000001
		;
createNode transform -n "LipProfileOuter" -p "FaceFitLipOuterCurve";
createNode nurbsCurve -n "LipProfileOuterShape" -p "LipProfileOuter";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitLipOuterLoc" -p "FaceFitLipOuter";
createNode transform -n "FaceFitLipMain" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.e[370] headTopology.e[372] headTopology.e[374] headTopology.e[375] ";
createNode transform -n "FaceFitLipMainGeo" -p "FaceFitLipMain";
	setAttr ".it" no;
createNode transform -n "upperLipCylinderMain" -p "FaceFitLipMainGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "upperLipCylinderMainShape" -p "upperLipCylinderMain";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "lowerLipCylinderMain" -p "FaceFitLipMainGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "lowerLipCylinderMainShape" -p "lowerLipCylinderMain";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "FaceFitLipMainCurve" -p "FaceFitLipMain";
	setAttr ".it" no;
createNode transform -n "upperLipMainCurve" -p "FaceFitLipMainCurve";
createNode nurbsCurve -n "upperLipMainCurveShape" -p "upperLipMainCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 13;
	setAttr ".cc" -type "nurbsCurve" 
		1 1 0 no 3
		2 0 1
		2
		-6.4597351619999998e-019 0.35497418050000001 0.41504552960000002
		-0.11463622 0.32908019420000001 0.3497538865
		;
createNode transform -n "lowerLipMainCurve" -p "FaceFitLipMainCurve";
createNode nurbsCurve -n "lowerLipMainCurveShape" -p "lowerLipMainCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 13;
	setAttr ".cc" -type "nurbsCurve" 
		1 1 0 no 3
		2 0 1
		2
		-2.3168557610000001e-018 0.29110494260000003 0.41190582510000001
		-0.11463622 0.32908019420000001 0.3497538865
		;
createNode transform -n "LipProfileMain" -p "FaceFitLipMainCurve";
createNode nurbsCurve -n "LipProfileMainShape" -p "LipProfileMain";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitLipMainLoc" -p "FaceFitLipMain";
createNode transform -n "FaceFitLipInner" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.e[378] headTopology.e[380] headTopology.e[382] headTopology.e[383] ";
createNode transform -n "FaceFitLipInnerGeo" -p "FaceFitLipInner";
	setAttr ".t" -type "double3" 0 -0.35622556891217699 0 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode transform -n "upperLipCylinderInner" -p "FaceFitLipInnerGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "upperLipCylinderInnerShape" -p "upperLipCylinderInner";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "lowerLipCylinderInner" -p "FaceFitLipInnerGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "lowerLipCylinderInnerShape" -p "lowerLipCylinderInner";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "LipInnerAreaMesh" -p "FaceFitLipInnerGeo";
createNode mesh -n "LipInnerAreaMeshShape" -p "LipInnerAreaMesh";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr -s 32 ".uvst[0].uvsp[0:31]" -type "float2" 0 0 1 0 1 1 0 1 0
		 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0
		 1 0 1 1 0 1 0 0 1 0 1 1 0 1;
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr -s 12 ".vt[0:11]"  -0.12003662 0.33014038 0.34887195 0.12003662 0.33014038 0.34887195
		 -6.7640477e-019 0.36057907 0.41193688 -2.4260008e-018 0.28550005 0.40824613 -0.11463622 0.32908019 0.34975389
		 -2.3168558e-018 0.29110494 0.41190583 0.11463622 0.32908019 0.34975389 -6.4597352e-019 0.35497418 0.41504553
		 -0.10703363 0.32842764 0.35349393 -2.066571e-018 0.29455477 0.40256292 0.10703363 0.32842764 0.35349393
		 -5.7619044e-019 0.35152435 0.40536347;
	setAttr -s 20 ".ed[0:19]"  0 3 0 1 3 0 1 2 0 0 2 0 0 4 0 3 5 0 4 5 0
		 1 6 0 6 5 0 2 7 0 6 7 0 4 7 0 4 8 0 5 9 0 8 9 0 6 10 0 10 9 0 7 11 0 10 11 0 8 11 0;
	setAttr -s 8 ".fc[0:7]" -type "polyFaces" 
		f 4 0 5 -7 -5
		mu 0 4 0 1 2 3
		f 4 -2 7 8 -6
		mu 0 4 4 5 6 7
		f 4 2 9 -11 -8
		mu 0 4 8 9 10 11
		f 4 -4 4 11 -10
		mu 0 4 12 13 14 15
		f 4 6 13 -15 -13
		mu 0 4 16 17 18 19
		f 4 -9 15 16 -14
		mu 0 4 20 21 22 23
		f 4 10 17 -19 -16
		mu 0 4 24 25 26 27
		f 4 -12 12 19 -18
		mu 0 4 28 29 30 31;
	setAttr ".cd" -type "dataPolyComponent" Index_Data Edge 0 ;
	setAttr ".cvd" -type "dataPolyComponent" Index_Data Vertex 0 ;
	setAttr ".hfd" -type "dataPolyComponent" Index_Data Face 0 ;
createNode transform -n "FaceFitLipInnerCurve" -p "FaceFitLipInner";
	setAttr ".it" no;
createNode transform -n "upperLipInnerCurve" -p "FaceFitLipInnerCurve";
createNode nurbsCurve -n "upperLipInnerCurveShape" -p "upperLipInnerCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 15;
	setAttr ".cc" -type "nurbsCurve" 
		1 1 0 no 3
		2 0 1
		2
		-5.7619044010000004e-019 0.35152435300000001 0.40536347029999997
		-0.10703363270000001 0.32842764260000001 0.35349392889999998
		;
createNode transform -n "lowerLipInnerCurve" -p "FaceFitLipInnerCurve";
createNode nurbsCurve -n "lowerLipInnerCurveShape" -p "lowerLipInnerCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 15;
	setAttr ".cc" -type "nurbsCurve" 
		1 1 0 no 3
		2 0 1
		2
		-2.0665709669999998e-018 0.29455477000000002 0.40256291630000002
		-0.10703363270000001 0.32842764260000001 0.35349392889999998
		;
createNode transform -n "LipProfileInner" -p "FaceFitLipInnerCurve";
createNode nurbsCurve -n "LipProfileInnerShape" -p "LipProfileInner";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitLipInnerLoc" -p "FaceFitLipInner";
createNode transform -n "FaceFitEyeLidOuter" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.e[45] headTopology.e[83] headTopology.e[109] headTopology.e[111] headTopology.e[112] headTopology.e[120] ";
createNode transform -n "FaceFitEyeLidOuterGeo" -p "FaceFitEyeLidOuter";
	setAttr ".it" no;
createNode transform -n "upperEyeLidCylinderOuter" -p "FaceFitEyeLidOuterGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "upperEyeLidCylinderOuterShape" -p "upperEyeLidCylinderOuter";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "lowerEyeLidCylinderOuter" -p "FaceFitEyeLidOuterGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "lowerEyeLidCylinderOuterShape" -p "lowerEyeLidCylinderOuter";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "FaceFitEyeLidOuterCurve" -p "FaceFitEyeLidOuter";
	setAttr ".it" no;
createNode transform -n "upperEyeLidOuterCurve" -p "FaceFitEyeLidOuterCurve";
createNode nurbsCurve -n "upperEyeLidOuterCurveShape" -p "upperEyeLidOuterCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 14;
	setAttr ".cc" -type "nurbsCurve" 
		1 3 0 no 3
		4 0 1 2 3
		4
		-0.082967221739999999 0.56612211469999996 0.33013713360000002
		-0.098061747850000006 0.60015285019999998 0.33543127779999998
		-0.165674448 0.60748714209999999 0.31889745590000002
		-0.19909352059999999 0.58214378359999996 0.29660406709999998
		;
createNode transform -n "lowerEyeLidOuterCurve" -p "FaceFitEyeLidOuterCurve";
createNode nurbsCurve -n "lowerEyeLidOuterCurveShape" -p "lowerEyeLidOuterCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 14;
	setAttr ".cc" -type "nurbsCurve" 
		1 3 0 no 3
		4 0 1 2 3
		4
		-0.082967221739999999 0.56612211469999996 0.33013713360000002
		-0.098728805779999998 0.55066519979999995 0.32443284989999999
		-0.1632765085 0.55234014990000002 0.3079913259
		-0.19909352059999999 0.58214378359999996 0.29660406709999998
		;
createNode transform -n "EyeLidProfileOuter" -p "FaceFitEyeLidOuterCurve";
createNode nurbsCurve -n "EyeLidProfileOuterShape" -p "EyeLidProfileOuter";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitEyeLidOuterLoc" -p "FaceFitEyeLidOuter";
createNode transform -n "FaceFitEyeLidMain" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.e[346] headTopology.e[349] headTopology.e[351] headTopology.e[352] headTopology.e[354] headTopology.e[355] ";
createNode transform -n "FaceFitEyeLidMainGeo" -p "FaceFitEyeLidMain";
	setAttr ".it" no;
createNode transform -n "upperEyeLidCylinderMain" -p "FaceFitEyeLidMainGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "upperEyeLidCylinderMainShape" -p "upperEyeLidCylinderMain";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "lowerEyeLidCylinderMain" -p "FaceFitEyeLidMainGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "lowerEyeLidCylinderMainShape" -p "lowerEyeLidCylinderMain";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "FaceFitEyeLidMainCurve" -p "FaceFitEyeLidMain";
	setAttr ".it" no;
createNode transform -n "upperEyeLidMainCurve" -p "FaceFitEyeLidMainCurve";
createNode nurbsCurve -n "upperEyeLidMainCurveShape" -p "upperEyeLidMainCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 13;
	setAttr ".cc" -type "nurbsCurve" 
		1 3 0 no 3
		4 0 1 2 3
		4
		-0.088338077070000007 0.56888771059999999 0.32669395210000002
		-0.1008603424 0.59515684840000005 0.33213350180000001
		-0.16362132130000001 0.59897917509999998 0.32150605319999997
		-0.1946121603 0.58177328110000004 0.30366039280000001
		;
createNode transform -n "lowerEyeLidMainCurve" -p "FaceFitEyeLidMainCurve";
createNode nurbsCurve -n "lowerEyeLidMainCurveShape" -p "lowerEyeLidMainCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 13;
	setAttr ".cc" -type "nurbsCurve" 
		1 3 0 no 3
		4 0 1 2 3
		4
		-0.088338077070000007 0.56888771059999999 0.32669395210000002
		-0.1005906835 0.55878716709999998 0.32325530050000001
		-0.16013789179999999 0.56083798409999996 0.31140691040000001
		-0.1946121603 0.58177328110000004 0.30366039280000001
		;
createNode transform -n "EyeLidProfileMain" -p "FaceFitEyeLidMainCurve";
createNode nurbsCurve -n "EyeLidProfileMainShape" -p "EyeLidProfileMain";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitEyeLidMainLoc" -p "FaceFitEyeLidMain";
createNode transform -n "FaceFitEyeLidInner" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.e[386] headTopology.e[389] headTopology.e[391] headTopology.e[392] headTopology.e[394] headTopology.e[395] ";
createNode transform -n "FaceFitEyeLidInnerGeo" -p "FaceFitEyeLidInner";
	setAttr ".t" -type "double3" 0 -0.35622556891217699 0 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode transform -n "upperEyeLidCylinderInner" -p "FaceFitEyeLidInnerGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "upperEyeLidCylinderInnerShape" -p "upperEyeLidCylinderInner";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "lowerEyeLidCylinderInner" -p "FaceFitEyeLidInnerGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "lowerEyeLidCylinderInnerShape" -p "lowerEyeLidCylinderInner";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "EyeLidInnerAreaMesh" -p "FaceFitEyeLidInnerGeo";
createNode mesh -n "EyeLidInnerAreaMeshShape" -p "EyeLidInnerAreaMesh";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr -s 48 ".uvst[0].uvsp[0:47]" -type "float2" 0 0 1 0 1 1 0 1 0
		 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0
		 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1 0 1 1 0 1 0 0 1
		 0 1 1 0 1;
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr -s 18 ".vt[0:17]"  -0.19909352 0.58214378 0.29660407 -0.098061748 0.60015285 0.33543128
		 -0.082967222 0.56612211 0.33013713 -0.098728806 0.5506652 0.32443285 -0.16327651 0.55234015 0.30799133
		 -0.16567445 0.60748714 0.31889746 -0.19461216 0.58177328 0.30366039 -0.16362132 0.59897918 0.32150605
		 -0.10059068 0.55878717 0.3232553 -0.16013789 0.56083798 0.31140691 -0.088338077 0.56888771 0.32669395
		 -0.10086034 0.59515685 0.3321335 -0.19346651 0.58170801 0.29748407 -0.16306534 0.59861118 0.31475553
		 -0.10141834 0.55899978 0.31648394 -0.15969911 0.56116849 0.30485448 -0.088771634 0.56917065 0.32010478
		 -0.10173952 0.59476399 0.32522815;
	setAttr -s 30 ".ed[0:29]"  0 5 1 3 4 1 2 3 1 4 0 1 1 2 1 5 1 1 0 6 1
		 5 7 1 6 7 1 3 8 1 4 9 1 8 9 1 2 10 0 10 8 0 9 6 1 1 11 0 11 10 0 7 11 1 6 12 1 7 13 1
		 12 13 0 8 14 1 9 15 1 14 15 0 10 16 1 16 14 0 15 12 0 11 17 1 17 16 0 13 17 0;
	setAttr -s 12 ".fc[0:11]" -type "polyFaces" 
		f 4 -1 6 8 -8
		mu 0 4 0 1 2 3
		f 4 -2 9 11 -11
		mu 0 4 4 5 6 7
		f 4 -3 12 13 -10
		mu 0 4 8 9 10 11
		f 4 -4 10 14 -7
		mu 0 4 12 13 14 15
		f 4 -5 15 16 -13
		mu 0 4 16 17 18 19
		f 4 -6 7 17 -16
		mu 0 4 20 21 22 23
		f 4 -9 18 20 -20
		mu 0 4 24 25 26 27
		f 4 -12 21 23 -23
		mu 0 4 28 29 30 31
		f 4 -14 24 25 -22
		mu 0 4 32 33 34 35
		f 4 -15 22 26 -19
		mu 0 4 36 37 38 39
		f 4 -17 27 28 -25
		mu 0 4 40 41 42 43
		f 4 -18 19 29 -28
		mu 0 4 44 45 46 47;
	setAttr ".cd" -type "dataPolyComponent" Index_Data Edge 0 ;
	setAttr ".cvd" -type "dataPolyComponent" Index_Data Vertex 0 ;
	setAttr ".hfd" -type "dataPolyComponent" Index_Data Face 0 ;
createNode transform -n "FaceFitEyeLidInnerCurve" -p "FaceFitEyeLidInner";
	setAttr ".it" no;
createNode transform -n "upperEyeLidInnerCurve" -p "FaceFitEyeLidInnerCurve";
createNode nurbsCurve -n "upperEyeLidInnerCurveShape" -p "upperEyeLidInnerCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 15;
	setAttr ".cc" -type "nurbsCurve" 
		1 3 0 no 3
		4 0 1 2 3
		4
		-0.088771633799999999 0.56917065379999998 0.32010477780000002
		-0.1017395183 0.59476399420000003 0.32522815469999999
		-0.16306534410000001 0.59861117600000002 0.31475552919999999
		-0.1934665143 0.58170801400000005 0.2974840701
		;
createNode transform -n "lowerEyeLidInnerCurve" -p "FaceFitEyeLidInnerCurve";
createNode nurbsCurve -n "lowerEyeLidInnerCurveShape" -p "lowerEyeLidInnerCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 15;
	setAttr ".cc" -type "nurbsCurve" 
		1 3 0 no 3
		4 0 1 2 3
		4
		-0.088771633799999999 0.56917065379999998 0.32010477780000002
		-0.1014183387 0.55899977680000001 0.31648394470000002
		-0.15969911219999999 0.56116849179999995 0.30485448240000002
		-0.1934665143 0.58170801400000005 0.2974840701
		;
createNode transform -n "EyeLidProfileInner" -p "FaceFitEyeLidInnerCurve";
createNode nurbsCurve -n "EyeLidProfileInnerShape" -p "EyeLidProfileInner";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitEyeLidInnerLoc" -p "FaceFitEyeLidInner";
createNode transform -n "FaceFitEyeBrowInner" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[50] ";
createNode transform -n "FaceFitEyeBrowInnerGeo" -p "FaceFitEyeBrowInner";
createNode transform -n "FaceFitEyeBrowInnerCurve" -p "FaceFitEyeBrowInner";
	setAttr ".it" no;
createNode transform -n "FaceFitEyeBrowInnerLoc" -p "FaceFitEyeBrowInner";
createNode transform -n "EyeBrowInner" -p "FaceFitEyeBrowInner";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.15924195637888874 0.93979972327396077 0.69574427953477169 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "EyeBrowInnerShape" -p "EyeBrowInner";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "EyeBrowInnerSphere" -p "FaceFitEyeBrowInner";
createNode nurbsSurface -n "EyeBrowInnerSphereShape" -p "EyeBrowInnerSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitEyeBrowOuter" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[33] ";
createNode transform -n "FaceFitEyeBrowOuterGeo" -p "FaceFitEyeBrowOuter";
createNode transform -n "FaceFitEyeBrowOuterCurve" -p "FaceFitEyeBrowOuter";
	setAttr ".it" no;
createNode transform -n "FaceFitEyeBrowOuterLoc" -p "FaceFitEyeBrowOuter";
createNode transform -n "EyeBrowOuter" -p "FaceFitEyeBrowOuter";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.38581830863584382 0.9300051634750377 0.50715965546270525 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "EyeBrowOuterShape" -p "EyeBrowOuter";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "EyeBrowOuterSphere" -p "FaceFitEyeBrowOuter";
createNode nurbsSurface -n "EyeBrowOuterSphereShape" -p "EyeBrowOuterSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitForeHead" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.vtx[2] headTopology.vtx[7] headTopology.vtx[36] ";
createNode transform -n "FaceFitForeHeadGeo" -p "FaceFitForeHead";
	setAttr ".t" -type "double3" 0 -0.35622556891217699 0 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode transform -n "ForeHeadCylinder" -p "FaceFitForeHeadGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "ForeHeadCylinderShape" -p "ForeHeadCylinder";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "ForeHeadAreaMesh" -p "FaceFitForeHeadGeo";
createNode mesh -n "ForeHeadAreaMeshShape" -p "ForeHeadAreaMesh";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr -s 20 ".uvst[0].uvsp[0:19]" -type "float2" 0.625 0.58333302
		 0.54166698 0.66666698 0.625 0.66666698 0.73409498 0.351677 0.875 0.118481 0.589692
		 0.66666698 0.82082498 0.25 0.875 0.25 0.875 0.166667 0.82082498 0.166667 0.75 0.166667
		 0.75 0.25 0.625 0.63151902 0.77708203 0.117253 0.77170599 0.083333001 0.78085399
		 0.083333001 0.82082498 0.10126 0.75 0.11341 0.75 0.083333001 0.76323098 0.083333001;
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr -s 18 ".vt[0:17]"  -7.410281e-018 0.80081809 0.36772943 -8.4191973e-018 0.5191527 0.41064543
		 -5.5721914e-018 0.69587302 0.39002153 -7.3925528e-018 0.59797472 0.40108627 -0.19029589 0.76850891 0.30388093
		 -0.20733055 0.69119304 0.27253681 -0.24539369 0.53258133 0.13113557 -0.19909352 0.58214378 0.29660407
		 -0.1202702 0.80994469 0.34471869 -0.085573241 0.69645643 0.37387818 -0.23049913 0.55204892 0.251856
		 -0.19631773 0.63876754 0.29978815 -0.098061748 0.60015285 0.33543128 -0.082967222 0.56612211 0.33013713
		 -0.16567445 0.60748714 0.31889746 -0.049983926 0.61393923 0.39259547 -0.033676066 0.52397549 0.39561006
		 -0.047899123 0.53483415 0.36830786;
	setAttr -s 27 ".ed[0:26]"  0 2 1 2 3 1 3 1 1 4 8 1 5 4 1 6 5 1 7 14 1
		 14 11 1 11 10 1 10 7 1 5 9 1 9 8 1 8 0 1 9 2 1 6 10 1 11 5 1 11 15 1 15 9 1 17 13 1
		 12 13 1 17 15 1 15 12 1 15 3 1 15 16 1 16 1 1 14 12 1 17 16 1;
	setAttr -s 10 ".fc[0:9]" -type "polyFaces" 
		f 4 6 7 8 9
		mu 0 4 2 3 4 5
		f 4 -4 -5 10 11
		mu 0 4 6 7 8 9
		f 4 -1 -13 -12 13
		mu 0 4 10 11 6 9
		f 4 14 -9 15 -6
		mu 0 4 1 5 12 0
		f 4 16 17 -11 -16
		mu 0 4 4 13 9 8
		f 4 19 -19 20 21
		mu 0 4 16 15 14 13
		f 4 -2 -14 -18 22
		mu 0 4 17 10 9 13
		f 4 -3 -23 23 24
		mu 0 4 18 17 13 19
		f 4 -22 -17 -8 25
		mu 0 4 16 13 4 3
		f 3 26 -24 -21
		mu 0 3 14 19 13;
	setAttr ".cd" -type "dataPolyComponent" Index_Data Edge 0 ;
	setAttr ".cvd" -type "dataPolyComponent" Index_Data Vertex 0 ;
	setAttr ".hfd" -type "dataPolyComponent" Index_Data Face 0 ;
createNode transform -n "FaceFitForeHeadCurve" -p "FaceFitForeHead";
	setAttr ".it" no;
createNode transform -n "ForeHeadCurve" -p "FaceFitForeHeadCurve";
createNode nurbsCurve -n "ForeHeadCurveShape" -p "ForeHeadCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".cc" -type "nurbsCurve" 
		1 14 0 no 3
		15 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15
		15
		-0.1990935206413269 0.58214378356933594 0.29660406708717346
		-0.23049913346767426 0.55204892158508301 0.2518559992313385
		-0.24539369344711304 0.53258132934570313 0.13113556802272797
		-0.20733055472373962 0.69119304418563843 0.27253681421279907
		-0.19029588997364044 0.7685089111328125 0.30388092994689941
		-0.12027020007371902 0.80994468927383423 0.34471869468688965
		-0.12027020007371902 0.91419810056686401 0.26152998208999634
		-5.9264198710586472e-018 0.91634416580200195 0.28326800465583801
		-7.4102809739440474e-018 0.80081808567047119 0.36772942543029785
		-5.5721913542613888e-018 0.69587302207946777 0.39002153277397156
		-7.3925528390558109e-018 0.59797471761703491 0.40108627080917358
		-8.419197302978038e-018 0.51915270090103149 0.41064542531967163
		-0.033676065504550934 0.52397549152374268 0.39561006426811218
		-0.047899123281240463 0.53483414649963379 0.36830785870552063
		-0.082967221736907959 0.56612211465835571 0.33013713359832764
		;
createNode transform -n "ForeHeadProfile" -p "FaceFitForeHeadCurve";
createNode nurbsCurve -n "ForeHeadProfileShape" -p "ForeHeadProfile";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitForeHeadLoc" -p "FaceFitForeHead";
createNode lightLinker -s -n "lightLinker1";
	setAttr -s 11 ".lnk";
	setAttr -s 11 ".slnk";
createNode displayLayerManager -n "layerManager";
	setAttr ".cdl" 8;
	setAttr -s 5 ".dli[1:4]"  7 6 8 1;
	setAttr -s 3 ".dli";
createNode displayLayer -n "defaultLayer";
createNode renderLayerManager -n "renderLayerManager";
createNode renderLayer -n "defaultRenderLayer";
	setAttr ".g" yes;
createNode renderLayerManager -n "renderLayerManager1";
createNode renderLayer -n "defaultRenderLayer1";
	setAttr ".g" yes;
createNode displayLayer -n "ProfileCurves";
	setAttr ".c" 6;
	setAttr ".do" 2;
createNode displayLayer -n "MainCurves";
	setAttr ".c" 13;
	setAttr ".do" 1;
createNode lambert -n "asRed";
	setAttr ".c" -type "float3" 1 0 0 ;
createNode shadingEngine -n "asRedSG";
	setAttr ".ihi" 0;
	setAttr -s 4 ".dsm";
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo1";
createNode lambert -n "asRed2";
	setAttr ".c" -type "float3" 1 0 1 ;
createNode shadingEngine -n "asRed2SG";
	setAttr ".ihi" 0;
	setAttr -s 2 ".dsm";
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo2";
createNode lambert -n "asGreen";
	setAttr ".c" -type "float3" 0 1 0 ;
createNode shadingEngine -n "asGreenSG";
	setAttr ".ihi" 0;
	setAttr -s 4 ".dsm";
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo3";
createNode lambert -n "asGreen2";
	setAttr ".c" -type "float3" 1 1 0 ;
createNode shadingEngine -n "asGreen2SG";
	setAttr ".ihi" 0;
	setAttr -s 2 ".dsm";
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo4";
createNode lambert -n "asBlue";
	setAttr ".c" -type "float3" 0 0 1 ;
createNode shadingEngine -n "asBlueSG";
	setAttr ".ihi" 0;
	setAttr -s 2 ".dsm";
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo5";
createNode lambert -n "asBlue2";
	setAttr ".c" -type "float3" 0 1 1 ;
createNode shadingEngine -n "asBlue2SG";
	setAttr ".ihi" 0;
	setAttr -s 14 ".dsm";
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo6";
createNode script -n "uiConfigurationScriptNode";
	setAttr ".b" -type "string" (
		"// Maya Mel UI Configuration File.\n//\n//  This script is machine generated.  Edit at your own risk.\n//\n//\n\nglobal string $gMainPane;\nif (`paneLayout -exists $gMainPane`) {\n\n\tglobal int $gUseScenePanelConfig;\n\tint    $useSceneConfig = $gUseScenePanelConfig;\n\tint    $menusOkayInPanels = `optionVar -q allowMenusInPanels`;\tint    $nVisPanes = `paneLayout -q -nvp $gMainPane`;\n\tint    $nPanes = 0;\n\tstring $editorName;\n\tstring $panelName;\n\tstring $itemFilterName;\n\tstring $panelConfig;\n\n\t//\n\t//  get current state of the UI\n\t//\n\tsceneUIReplacement -update $gMainPane;\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"Top View\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"Top View\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"top\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"wireframe\" \n"
		+ "                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -rendererName \"base_OpenGL_Renderer\" \n"
		+ "                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 256 256 \n                -bumpResolution 512 512 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 1\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n"
		+ "                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"Top View\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"top\" \n            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"wireframe\" \n            -activeOnly 0\n            -ignorePanZoom 0\n"
		+ "            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n            -xray 0\n            -jointXray 0\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -rendererName \"base_OpenGL_Renderer\" \n            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 256 256 \n            -bumpResolution 512 512 \n            -textureCompression 0\n"
		+ "            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 1\n            -occlusionCulling 0\n            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n"
		+ "            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"Side View\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"Side View\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"side\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"wireframe\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n"
		+ "                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -rendererName \"base_OpenGL_Renderer\" \n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 256 256 \n                -bumpResolution 512 512 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n"
		+ "                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 1\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n"
		+ "                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"Side View\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"side\" \n            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"wireframe\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n"
		+ "            -xray 0\n            -jointXray 0\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -rendererName \"base_OpenGL_Renderer\" \n            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 256 256 \n            -bumpResolution 512 512 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 1\n            -occlusionCulling 0\n"
		+ "            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\n"
		+ "modelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"Front View\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"Front View\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"front\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"wireframe\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n"
		+ "                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -rendererName \"base_OpenGL_Renderer\" \n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 256 256 \n                -bumpResolution 512 512 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 1\n                -occlusionCulling 0\n"
		+ "                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n"
		+ "                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"Front View\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"front\" \n            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"wireframe\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n            -xray 0\n            -jointXray 0\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n"
		+ "            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -rendererName \"base_OpenGL_Renderer\" \n            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 256 256 \n            -bumpResolution 512 512 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 1\n            -occlusionCulling 0\n            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n"
		+ "            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"Persp View\")) `;\n"
		+ "\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"Persp View\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"persp\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"smoothShaded\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 1\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n"
		+ "                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -rendererName \"base_OpenGL_Renderer\" \n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 256 256 \n                -bumpResolution 512 512 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 1\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n"
		+ "                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n"
		+ "\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"Persp View\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"persp\" \n            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"smoothShaded\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 1\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n            -xray 0\n            -jointXray 0\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n"
		+ "            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -rendererName \"base_OpenGL_Renderer\" \n            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 256 256 \n            -bumpResolution 512 512 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 1\n            -occlusionCulling 0\n            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n"
		+ "            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"outlinerPanel\" (localizedPanelLabel(\"Outliner\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `outlinerPanel -unParent -l (localizedPanelLabel(\"Outliner\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            outlinerEditor -e \n"
		+ "                -docTag \"isolOutln_fromSeln\" \n                -showShapes 0\n                -showAttributes 0\n                -showConnected 0\n                -showAnimCurvesOnly 0\n                -showMuteInfo 0\n                -organizeByLayer 1\n                -showAnimLayerWeight 1\n                -autoExpandLayers 1\n                -autoExpand 0\n                -showDagOnly 1\n                -showAssets 1\n                -showContainedOnly 1\n                -showPublishedAsConnected 0\n                -showContainerContents 1\n                -ignoreDagHierarchy 0\n                -expandConnections 0\n                -showUpstreamCurves 1\n                -showUnitlessCurves 1\n                -showCompounds 1\n                -showLeafs 1\n                -showNumericAttrsOnly 0\n                -highlightActive 1\n                -autoSelectNewObjects 0\n                -doNotSelectNewObjects 0\n                -dropIsParent 1\n                -transmitFilters 0\n                -setFilter \"defaultSetFilter\" \n                -showSetMembers 0\n"
		+ "                -allowMultiSelection 1\n                -alwaysToggleSelect 0\n                -directSelect 0\n                -displayMode \"DAG\" \n                -expandObjects 0\n                -setsIgnoreFilters 1\n                -containersIgnoreFilters 0\n                -editAttrName 0\n                -showAttrValues 0\n                -highlightSecondary 0\n                -showUVAttrsOnly 0\n                -showTextureNodesOnly 0\n                -attrAlphaOrder \"default\" \n                -animLayerFilterOptions \"allAffecting\" \n                -sortOrder \"none\" \n                -longNames 0\n                -niceNames 1\n                -showNamespace 1\n                -showPinIcons 0\n                -mapMotionTrails 0\n                $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\toutlinerPanel -edit -l (localizedPanelLabel(\"Outliner\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        outlinerEditor -e \n            -docTag \"isolOutln_fromSeln\" \n            -showShapes 0\n"
		+ "            -showAttributes 0\n            -showConnected 0\n            -showAnimCurvesOnly 0\n            -showMuteInfo 0\n            -organizeByLayer 1\n            -showAnimLayerWeight 1\n            -autoExpandLayers 1\n            -autoExpand 0\n            -showDagOnly 1\n            -showAssets 1\n            -showContainedOnly 1\n            -showPublishedAsConnected 0\n            -showContainerContents 1\n            -ignoreDagHierarchy 0\n            -expandConnections 0\n            -showUpstreamCurves 1\n            -showUnitlessCurves 1\n            -showCompounds 1\n            -showLeafs 1\n            -showNumericAttrsOnly 0\n            -highlightActive 1\n            -autoSelectNewObjects 0\n            -doNotSelectNewObjects 0\n            -dropIsParent 1\n            -transmitFilters 0\n            -setFilter \"defaultSetFilter\" \n            -showSetMembers 0\n            -allowMultiSelection 1\n            -alwaysToggleSelect 0\n            -directSelect 0\n            -displayMode \"DAG\" \n            -expandObjects 0\n"
		+ "            -setsIgnoreFilters 1\n            -containersIgnoreFilters 0\n            -editAttrName 0\n            -showAttrValues 0\n            -highlightSecondary 0\n            -showUVAttrsOnly 0\n            -showTextureNodesOnly 0\n            -attrAlphaOrder \"default\" \n            -animLayerFilterOptions \"allAffecting\" \n            -sortOrder \"none\" \n            -longNames 0\n            -niceNames 1\n            -showNamespace 1\n            -showPinIcons 0\n            -mapMotionTrails 0\n            $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\tif ($useSceneConfig) {\n\t\toutlinerPanel -e -to $panelName;\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"graphEditor\" (localizedPanelLabel(\"Graph Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"graphEditor\" -l (localizedPanelLabel(\"Graph Editor\")) -mbv $menusOkayInPanels `;\n\n\t\t\t$editorName = ($panelName+\"OutlineEd\");\n            outlinerEditor -e \n                -showShapes 1\n"
		+ "                -showAttributes 1\n                -showConnected 1\n                -showAnimCurvesOnly 1\n                -showMuteInfo 0\n                -organizeByLayer 1\n                -showAnimLayerWeight 1\n                -autoExpandLayers 1\n                -autoExpand 1\n                -showDagOnly 0\n                -showAssets 1\n                -showContainedOnly 0\n                -showPublishedAsConnected 0\n                -showContainerContents 0\n                -ignoreDagHierarchy 0\n                -expandConnections 1\n                -showUpstreamCurves 1\n                -showUnitlessCurves 1\n                -showCompounds 0\n                -showLeafs 1\n                -showNumericAttrsOnly 1\n                -highlightActive 0\n                -autoSelectNewObjects 1\n                -doNotSelectNewObjects 0\n                -dropIsParent 1\n                -transmitFilters 1\n                -setFilter \"0\" \n                -showSetMembers 0\n                -allowMultiSelection 1\n                -alwaysToggleSelect 0\n"
		+ "                -directSelect 0\n                -displayMode \"DAG\" \n                -expandObjects 0\n                -setsIgnoreFilters 1\n                -containersIgnoreFilters 0\n                -editAttrName 0\n                -showAttrValues 0\n                -highlightSecondary 0\n                -showUVAttrsOnly 0\n                -showTextureNodesOnly 0\n                -attrAlphaOrder \"default\" \n                -animLayerFilterOptions \"allAffecting\" \n                -sortOrder \"none\" \n                -longNames 0\n                -niceNames 1\n                -showNamespace 1\n                -showPinIcons 1\n                -mapMotionTrails 1\n                $editorName;\n\n\t\t\t$editorName = ($panelName+\"GraphEd\");\n            animCurveEditor -e \n                -displayKeys 1\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 1\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"integer\" \n                -snapValue \"none\" \n"
		+ "                -showResults \"off\" \n                -showBufferCurves \"off\" \n                -smoothness \"fine\" \n                -resultSamples 1\n                -resultScreenSamples 0\n                -resultUpdate \"delayed\" \n                -showUpstreamCurves 1\n                -stackedCurves 0\n                -stackedCurvesMin -1\n                -stackedCurvesMax 1\n                -stackedCurvesSpace 0.2\n                -displayNormalized 0\n                -preSelectionHighlight 0\n                -constrainDrag 0\n                -classicMode 1\n                $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Graph Editor\")) -mbv $menusOkayInPanels  $panelName;\n\n\t\t\t$editorName = ($panelName+\"OutlineEd\");\n            outlinerEditor -e \n                -showShapes 1\n                -showAttributes 1\n                -showConnected 1\n                -showAnimCurvesOnly 1\n                -showMuteInfo 0\n                -organizeByLayer 1\n                -showAnimLayerWeight 1\n"
		+ "                -autoExpandLayers 1\n                -autoExpand 1\n                -showDagOnly 0\n                -showAssets 1\n                -showContainedOnly 0\n                -showPublishedAsConnected 0\n                -showContainerContents 0\n                -ignoreDagHierarchy 0\n                -expandConnections 1\n                -showUpstreamCurves 1\n                -showUnitlessCurves 1\n                -showCompounds 0\n                -showLeafs 1\n                -showNumericAttrsOnly 1\n                -highlightActive 0\n                -autoSelectNewObjects 1\n                -doNotSelectNewObjects 0\n                -dropIsParent 1\n                -transmitFilters 1\n                -setFilter \"0\" \n                -showSetMembers 0\n                -allowMultiSelection 1\n                -alwaysToggleSelect 0\n                -directSelect 0\n                -displayMode \"DAG\" \n                -expandObjects 0\n                -setsIgnoreFilters 1\n                -containersIgnoreFilters 0\n                -editAttrName 0\n"
		+ "                -showAttrValues 0\n                -highlightSecondary 0\n                -showUVAttrsOnly 0\n                -showTextureNodesOnly 0\n                -attrAlphaOrder \"default\" \n                -animLayerFilterOptions \"allAffecting\" \n                -sortOrder \"none\" \n                -longNames 0\n                -niceNames 1\n                -showNamespace 1\n                -showPinIcons 1\n                -mapMotionTrails 1\n                $editorName;\n\n\t\t\t$editorName = ($panelName+\"GraphEd\");\n            animCurveEditor -e \n                -displayKeys 1\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 1\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"integer\" \n                -snapValue \"none\" \n                -showResults \"off\" \n                -showBufferCurves \"off\" \n                -smoothness \"fine\" \n                -resultSamples 1\n                -resultScreenSamples 0\n                -resultUpdate \"delayed\" \n"
		+ "                -showUpstreamCurves 1\n                -stackedCurves 0\n                -stackedCurvesMin -1\n                -stackedCurvesMax 1\n                -stackedCurvesSpace 0.2\n                -displayNormalized 0\n                -preSelectionHighlight 0\n                -constrainDrag 0\n                -classicMode 1\n                $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"dopeSheetPanel\" (localizedPanelLabel(\"Dope Sheet\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"dopeSheetPanel\" -l (localizedPanelLabel(\"Dope Sheet\")) -mbv $menusOkayInPanels `;\n\n\t\t\t$editorName = ($panelName+\"OutlineEd\");\n            outlinerEditor -e \n                -showShapes 1\n                -showAttributes 1\n                -showConnected 1\n                -showAnimCurvesOnly 1\n                -showMuteInfo 0\n                -organizeByLayer 1\n                -showAnimLayerWeight 1\n"
		+ "                -autoExpandLayers 1\n                -autoExpand 0\n                -showDagOnly 0\n                -showAssets 1\n                -showContainedOnly 0\n                -showPublishedAsConnected 0\n                -showContainerContents 0\n                -ignoreDagHierarchy 0\n                -expandConnections 1\n                -showUpstreamCurves 1\n                -showUnitlessCurves 0\n                -showCompounds 1\n                -showLeafs 1\n                -showNumericAttrsOnly 1\n                -highlightActive 0\n                -autoSelectNewObjects 0\n                -doNotSelectNewObjects 1\n                -dropIsParent 1\n                -transmitFilters 0\n                -setFilter \"0\" \n                -showSetMembers 0\n                -allowMultiSelection 1\n                -alwaysToggleSelect 0\n                -directSelect 0\n                -displayMode \"DAG\" \n                -expandObjects 0\n                -setsIgnoreFilters 1\n                -containersIgnoreFilters 0\n                -editAttrName 0\n"
		+ "                -showAttrValues 0\n                -highlightSecondary 0\n                -showUVAttrsOnly 0\n                -showTextureNodesOnly 0\n                -attrAlphaOrder \"default\" \n                -animLayerFilterOptions \"allAffecting\" \n                -sortOrder \"none\" \n                -longNames 0\n                -niceNames 1\n                -showNamespace 1\n                -showPinIcons 0\n                -mapMotionTrails 1\n                $editorName;\n\n\t\t\t$editorName = ($panelName+\"DopeSheetEd\");\n            dopeSheetEditor -e \n                -displayKeys 1\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 0\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"integer\" \n                -snapValue \"none\" \n                -outliner \"dopeSheetPanel1OutlineEd\" \n                -showSummary 1\n                -showScene 0\n                -hierarchyBelow 0\n                -showTicks 1\n                -selectionWindow 0 0 0 0 \n"
		+ "                $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Dope Sheet\")) -mbv $menusOkayInPanels  $panelName;\n\n\t\t\t$editorName = ($panelName+\"OutlineEd\");\n            outlinerEditor -e \n                -showShapes 1\n                -showAttributes 1\n                -showConnected 1\n                -showAnimCurvesOnly 1\n                -showMuteInfo 0\n                -organizeByLayer 1\n                -showAnimLayerWeight 1\n                -autoExpandLayers 1\n                -autoExpand 0\n                -showDagOnly 0\n                -showAssets 1\n                -showContainedOnly 0\n                -showPublishedAsConnected 0\n                -showContainerContents 0\n                -ignoreDagHierarchy 0\n                -expandConnections 1\n                -showUpstreamCurves 1\n                -showUnitlessCurves 0\n                -showCompounds 1\n                -showLeafs 1\n                -showNumericAttrsOnly 1\n                -highlightActive 0\n"
		+ "                -autoSelectNewObjects 0\n                -doNotSelectNewObjects 1\n                -dropIsParent 1\n                -transmitFilters 0\n                -setFilter \"0\" \n                -showSetMembers 0\n                -allowMultiSelection 1\n                -alwaysToggleSelect 0\n                -directSelect 0\n                -displayMode \"DAG\" \n                -expandObjects 0\n                -setsIgnoreFilters 1\n                -containersIgnoreFilters 0\n                -editAttrName 0\n                -showAttrValues 0\n                -highlightSecondary 0\n                -showUVAttrsOnly 0\n                -showTextureNodesOnly 0\n                -attrAlphaOrder \"default\" \n                -animLayerFilterOptions \"allAffecting\" \n                -sortOrder \"none\" \n                -longNames 0\n                -niceNames 1\n                -showNamespace 1\n                -showPinIcons 0\n                -mapMotionTrails 1\n                $editorName;\n\n\t\t\t$editorName = ($panelName+\"DopeSheetEd\");\n            dopeSheetEditor -e \n"
		+ "                -displayKeys 1\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 0\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"integer\" \n                -snapValue \"none\" \n                -outliner \"dopeSheetPanel1OutlineEd\" \n                -showSummary 1\n                -showScene 0\n                -hierarchyBelow 0\n                -showTicks 1\n                -selectionWindow 0 0 0 0 \n                $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"clipEditorPanel\" (localizedPanelLabel(\"Trax Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"clipEditorPanel\" -l (localizedPanelLabel(\"Trax Editor\")) -mbv $menusOkayInPanels `;\n\n\t\t\t$editorName = clipEditorNameFromPanel($panelName);\n            clipEditor -e \n                -displayKeys 0\n                -displayTangents 0\n"
		+ "                -displayActiveKeys 0\n                -displayActiveKeyTangents 0\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"none\" \n                -snapValue \"none\" \n                -manageSequencer 0 \n                $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Trax Editor\")) -mbv $menusOkayInPanels  $panelName;\n\n\t\t\t$editorName = clipEditorNameFromPanel($panelName);\n            clipEditor -e \n                -displayKeys 0\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 0\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"none\" \n                -snapValue \"none\" \n                -manageSequencer 0 \n                $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"sequenceEditorPanel\" (localizedPanelLabel(\"Camera Sequencer\")) `;\n"
		+ "\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"sequenceEditorPanel\" -l (localizedPanelLabel(\"Camera Sequencer\")) -mbv $menusOkayInPanels `;\n\n\t\t\t$editorName = sequenceEditorNameFromPanel($panelName);\n            clipEditor -e \n                -displayKeys 0\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 0\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"none\" \n                -snapValue \"none\" \n                -manageSequencer 1 \n                $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Camera Sequencer\")) -mbv $menusOkayInPanels  $panelName;\n\n\t\t\t$editorName = sequenceEditorNameFromPanel($panelName);\n            clipEditor -e \n                -displayKeys 0\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 0\n                -displayInfinities 0\n"
		+ "                -autoFit 0\n                -snapTime \"none\" \n                -snapValue \"none\" \n                -manageSequencer 1 \n                $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"hyperGraphPanel\" (localizedPanelLabel(\"Hypergraph Hierarchy\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"hyperGraphPanel\" -l (localizedPanelLabel(\"Hypergraph Hierarchy\")) -mbv $menusOkayInPanels `;\n\n\t\t\t$editorName = ($panelName+\"HyperGraphEd\");\n            hyperGraph -e \n                -graphLayoutStyle \"hierarchicalLayout\" \n                -orientation \"horiz\" \n                -mergeConnections 0\n                -zoom 1\n                -animateTransition 0\n                -showRelationships 1\n                -showShapes 0\n                -showDeformers 0\n                -showExpressions 0\n                -showConstraints 0\n                -showUnderworld 0\n                -showInvisible 0\n"
		+ "                -transitionFrames 1\n                -opaqueContainers 0\n                -freeform 0\n                -imagePosition 0 0 \n                -imageScale 1\n                -imageEnabled 0\n                -graphType \"DAG\" \n                -heatMapDisplay 0\n                -updateSelection 1\n                -updateNodeAdded 1\n                -useDrawOverrideColor 0\n                -limitGraphTraversal -1\n                -range 0 0 \n                -iconSize \"smallIcons\" \n                -showCachedConnections 0\n                $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Hypergraph Hierarchy\")) -mbv $menusOkayInPanels  $panelName;\n\n\t\t\t$editorName = ($panelName+\"HyperGraphEd\");\n            hyperGraph -e \n                -graphLayoutStyle \"hierarchicalLayout\" \n                -orientation \"horiz\" \n                -mergeConnections 0\n                -zoom 1\n                -animateTransition 0\n                -showRelationships 1\n                -showShapes 0\n"
		+ "                -showDeformers 0\n                -showExpressions 0\n                -showConstraints 0\n                -showUnderworld 0\n                -showInvisible 0\n                -transitionFrames 1\n                -opaqueContainers 0\n                -freeform 0\n                -imagePosition 0 0 \n                -imageScale 1\n                -imageEnabled 0\n                -graphType \"DAG\" \n                -heatMapDisplay 0\n                -updateSelection 1\n                -updateNodeAdded 1\n                -useDrawOverrideColor 0\n                -limitGraphTraversal -1\n                -range 0 0 \n                -iconSize \"smallIcons\" \n                -showCachedConnections 0\n                $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"hyperShadePanel\" (localizedPanelLabel(\"Hypershade\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"hyperShadePanel\" -l (localizedPanelLabel(\"Hypershade\")) -mbv $menusOkayInPanels `;\n"
		+ "\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Hypershade\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"visorPanel\" (localizedPanelLabel(\"Visor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"visorPanel\" -l (localizedPanelLabel(\"Visor\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Visor\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"createNodePanel\" (localizedPanelLabel(\"Create Node\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"createNodePanel\" -l (localizedPanelLabel(\"Create Node\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n"
		+ "\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Create Node\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"polyTexturePlacementPanel\" (localizedPanelLabel(\"UV Texture Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"polyTexturePlacementPanel\" -l (localizedPanelLabel(\"UV Texture Editor\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"UV Texture Editor\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"renderWindowPanel\" (localizedPanelLabel(\"Render View\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"renderWindowPanel\" -l (localizedPanelLabel(\"Render View\")) -mbv $menusOkayInPanels `;\n"
		+ "\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Render View\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"blendShapePanel\" (localizedPanelLabel(\"Blend Shape\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\tblendShapePanel -unParent -l (localizedPanelLabel(\"Blend Shape\")) -mbv $menusOkayInPanels ;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tblendShapePanel -edit -l (localizedPanelLabel(\"Blend Shape\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"dynRelEdPanel\" (localizedPanelLabel(\"Dynamic Relationships\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"dynRelEdPanel\" -l (localizedPanelLabel(\"Dynamic Relationships\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n"
		+ "\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Dynamic Relationships\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"relationshipPanel\" (localizedPanelLabel(\"Relationship Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"relationshipPanel\" -l (localizedPanelLabel(\"Relationship Editor\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Relationship Editor\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"referenceEditorPanel\" (localizedPanelLabel(\"Reference Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"referenceEditorPanel\" -l (localizedPanelLabel(\"Reference Editor\")) -mbv $menusOkayInPanels `;\n"
		+ "\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Reference Editor\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"componentEditorPanel\" (localizedPanelLabel(\"Component Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"componentEditorPanel\" -l (localizedPanelLabel(\"Component Editor\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Component Editor\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"dynPaintScriptedPanelType\" (localizedPanelLabel(\"Paint Effects\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"dynPaintScriptedPanelType\" -l (localizedPanelLabel(\"Paint Effects\")) -mbv $menusOkayInPanels `;\n"
		+ "\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Paint Effects\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"scriptEditorPanel\" (localizedPanelLabel(\"Script Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"scriptEditorPanel\" -l (localizedPanelLabel(\"Script Editor\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Script Editor\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"Stereo\" (localizedPanelLabel(\"Stereo\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"Stereo\" -l (localizedPanelLabel(\"Stereo\")) -mbv $menusOkayInPanels `;\nstring $editorName = ($panelName+\"Editor\");\n"
		+ "            stereoCameraView -e \n                -camera \"persp\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"wireframe\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n"
		+ "                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 4 4 \n                -bumpResolution 4 4 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 0\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n"
		+ "                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                -displayMode \"centerEye\" \n                -viewColor 0 0 0 1 \n                $editorName;\nstereoCameraView -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Stereo\")) -mbv $menusOkayInPanels  $panelName;\nstring $editorName = ($panelName+\"Editor\");\n"
		+ "            stereoCameraView -e \n                -camera \"persp\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"wireframe\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n"
		+ "                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 4 4 \n                -bumpResolution 4 4 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 0\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n"
		+ "                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                -displayMode \"centerEye\" \n                -viewColor 0 0 0 1 \n                $editorName;\nstereoCameraView -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"\")) `;\n"
		+ "\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"persp\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"smoothShaded\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n"
		+ "                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 4 4 \n                -bumpResolution 4 4 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 0\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n"
		+ "                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels  $panelName;\n"
		+ "\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"persp\" \n            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"smoothShaded\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n            -xray 0\n            -jointXray 0\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n"
		+ "            -maxConstantTransparency 1\n            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 4 4 \n            -bumpResolution 4 4 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 0\n            -occlusionCulling 0\n            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n"
		+ "            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"persp\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"smoothShaded\" \n                -activeOnly 0\n"
		+ "                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 4 4 \n"
		+ "                -bumpResolution 4 4 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 0\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n"
		+ "                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"persp\" \n            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"smoothShaded\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n"
		+ "            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n            -xray 0\n            -jointXray 0\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 4 4 \n            -bumpResolution 4 4 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n"
		+ "            -maximumNumHardwareLights 0\n            -occlusionCulling 0\n            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n"
		+ "            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"persp\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"smoothShaded\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n"
		+ "                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 4 4 \n                -bumpResolution 4 4 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 0\n                -occlusionCulling 0\n                -shadingModel 0\n"
		+ "                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n"
		+ "                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"persp\" \n            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"smoothShaded\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n            -xray 0\n            -jointXray 0\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n"
		+ "            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 4 4 \n            -bumpResolution 4 4 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 0\n            -occlusionCulling 0\n            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n"
		+ "            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels `;\n"
		+ "\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"persp\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"smoothShaded\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n"
		+ "                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 4 4 \n                -bumpResolution 4 4 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 0\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n"
		+ "                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"persp\" \n"
		+ "            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"smoothShaded\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n            -xray 0\n            -jointXray 0\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -objectFilterShowInHUD 1\n            -isFiltered 0\n"
		+ "            -colorResolution 4 4 \n            -bumpResolution 4 4 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 0\n            -occlusionCulling 0\n            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n"
		+ "            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\tif ($useSceneConfig) {\n        string $configName = `getPanel -cwl (localizedPanelLabel(\"Current Layout\"))`;\n        if (\"\" != $configName) {\n\t\t\tpanelConfiguration -edit -label (localizedPanelLabel(\"Current Layout\")) \n\t\t\t\t-defaultImage \"vacantCell.xP:/\"\n\t\t\t\t-image \"\"\n\t\t\t\t-sc false\n\t\t\t\t-configString \"global string $gMainPane; paneLayout -e -cn \\\"single\\\" -ps 1 100 100 $gMainPane;\"\n\t\t\t\t-removeAllPanels\n\t\t\t\t-ap false\n\t\t\t\t\t(localizedPanelLabel(\"Persp View\")) \n\t\t\t\t\t\"modelPanel\"\n"
		+ "\t\t\t\t\t\"$panelName = `modelPanel -unParent -l (localizedPanelLabel(\\\"Persp View\\\")) -mbv $menusOkayInPanels `;\\n$editorName = $panelName;\\nmodelEditor -e \\n    -cam `findStartUpCamera persp` \\n    -useInteractiveMode 0\\n    -displayLights \\\"default\\\" \\n    -displayAppearance \\\"smoothShaded\\\" \\n    -activeOnly 0\\n    -ignorePanZoom 0\\n    -wireframeOnShaded 1\\n    -headsUpDisplay 1\\n    -selectionHiliteDisplay 1\\n    -useDefaultMaterial 0\\n    -bufferMode \\\"double\\\" \\n    -twoSidedLighting 1\\n    -backfaceCulling 0\\n    -xray 0\\n    -jointXray 0\\n    -activeComponentsXray 0\\n    -displayTextures 0\\n    -smoothWireframe 0\\n    -lineWidth 1\\n    -textureAnisotropic 1\\n    -textureHilight 1\\n    -textureSampling 6\\n    -textureDisplay \\\"modulate\\\" \\n    -textureMaxSize 32768\\n    -fogging 0\\n    -fogSource \\\"fragment\\\" \\n    -fogMode \\\"linear\\\" \\n    -fogStart 0\\n    -fogEnd 100\\n    -fogDensity 0.1\\n    -fogColor 0.5 0.5 0.5 1 \\n    -maxConstantTransparency 1\\n    -rendererName \\\"base_OpenGL_Renderer\\\" \\n    -objectFilterShowInHUD 1\\n    -isFiltered 0\\n    -colorResolution 256 256 \\n    -bumpResolution 512 512 \\n    -textureCompression 0\\n    -transparencyAlgorithm \\\"frontAndBackCull\\\" \\n    -transpInShadows 0\\n    -cullingOverride \\\"none\\\" \\n    -lowQualityLighting 0\\n    -maximumNumHardwareLights 1\\n    -occlusionCulling 0\\n    -shadingModel 0\\n    -useBaseRenderer 0\\n    -useReducedRenderer 0\\n    -smallObjectCulling 0\\n    -smallObjectThreshold -1 \\n    -interactiveDisableShadows 0\\n    -interactiveBackFaceCull 0\\n    -sortTransparent 1\\n    -nurbsCurves 1\\n    -nurbsSurfaces 1\\n    -polymeshes 1\\n    -subdivSurfaces 1\\n    -planes 1\\n    -lights 1\\n    -cameras 1\\n    -controlVertices 1\\n    -hulls 1\\n    -grid 1\\n    -joints 1\\n    -ikHandles 1\\n    -deformers 1\\n    -dynamics 1\\n    -fluids 1\\n    -hairSystems 1\\n    -follicles 1\\n    -nCloths 1\\n    -nParticles 1\\n    -nRigids 1\\n    -dynamicConstraints 1\\n    -locators 1\\n    -manipulators 1\\n    -dimensions 1\\n    -handles 1\\n    -pivots 1\\n    -textures 1\\n    -strokes 1\\n    -motionTrails 1\\n    -shadows 0\\n    $editorName;\\nmodelEditor -e -viewSelected 0 $editorName\"\n"
		+ "\t\t\t\t\t\"modelPanel -edit -l (localizedPanelLabel(\\\"Persp View\\\")) -mbv $menusOkayInPanels  $panelName;\\n$editorName = $panelName;\\nmodelEditor -e \\n    -cam `findStartUpCamera persp` \\n    -useInteractiveMode 0\\n    -displayLights \\\"default\\\" \\n    -displayAppearance \\\"smoothShaded\\\" \\n    -activeOnly 0\\n    -ignorePanZoom 0\\n    -wireframeOnShaded 1\\n    -headsUpDisplay 1\\n    -selectionHiliteDisplay 1\\n    -useDefaultMaterial 0\\n    -bufferMode \\\"double\\\" \\n    -twoSidedLighting 1\\n    -backfaceCulling 0\\n    -xray 0\\n    -jointXray 0\\n    -activeComponentsXray 0\\n    -displayTextures 0\\n    -smoothWireframe 0\\n    -lineWidth 1\\n    -textureAnisotropic 1\\n    -textureHilight 1\\n    -textureSampling 6\\n    -textureDisplay \\\"modulate\\\" \\n    -textureMaxSize 32768\\n    -fogging 0\\n    -fogSource \\\"fragment\\\" \\n    -fogMode \\\"linear\\\" \\n    -fogStart 0\\n    -fogEnd 100\\n    -fogDensity 0.1\\n    -fogColor 0.5 0.5 0.5 1 \\n    -maxConstantTransparency 1\\n    -rendererName \\\"base_OpenGL_Renderer\\\" \\n    -objectFilterShowInHUD 1\\n    -isFiltered 0\\n    -colorResolution 256 256 \\n    -bumpResolution 512 512 \\n    -textureCompression 0\\n    -transparencyAlgorithm \\\"frontAndBackCull\\\" \\n    -transpInShadows 0\\n    -cullingOverride \\\"none\\\" \\n    -lowQualityLighting 0\\n    -maximumNumHardwareLights 1\\n    -occlusionCulling 0\\n    -shadingModel 0\\n    -useBaseRenderer 0\\n    -useReducedRenderer 0\\n    -smallObjectCulling 0\\n    -smallObjectThreshold -1 \\n    -interactiveDisableShadows 0\\n    -interactiveBackFaceCull 0\\n    -sortTransparent 1\\n    -nurbsCurves 1\\n    -nurbsSurfaces 1\\n    -polymeshes 1\\n    -subdivSurfaces 1\\n    -planes 1\\n    -lights 1\\n    -cameras 1\\n    -controlVertices 1\\n    -hulls 1\\n    -grid 1\\n    -joints 1\\n    -ikHandles 1\\n    -deformers 1\\n    -dynamics 1\\n    -fluids 1\\n    -hairSystems 1\\n    -follicles 1\\n    -nCloths 1\\n    -nParticles 1\\n    -nRigids 1\\n    -dynamicConstraints 1\\n    -locators 1\\n    -manipulators 1\\n    -dimensions 1\\n    -handles 1\\n    -pivots 1\\n    -textures 1\\n    -strokes 1\\n    -motionTrails 1\\n    -shadows 0\\n    $editorName;\\nmodelEditor -e -viewSelected 0 $editorName\"\n"
		+ "\t\t\t\t$configName;\n\n            setNamedPanelLayout (localizedPanelLabel(\"Current Layout\"));\n        }\n\n        panelHistory -e -clear mainPanelHistory;\n        setFocus `paneLayout -q -p1 $gMainPane`;\n        sceneUIReplacement -deleteRemaining;\n        sceneUIReplacement -clear;\n\t}\n\n\ngrid -spacing 5 -size 12 -divisions 5 -displayAxes yes -displayGridLines yes -displayDivisionLines yes -displayPerspectiveLabels no -displayOrthographicLabels no -displayAxesBold yes -perspectiveLabelPosition axis -orthographicLabelPosition edge;\nviewManip -drawCompass 0 -compassAngle 0 -frontParameters \"\" -homeParameters \"\" -selectionLockParameters \"\";\n}\n");
	setAttr ".st" 3;
createNode script -n "sceneConfigurationScriptNode";
	setAttr ".b" -type "string" "playbackOptions -min 1 -max 24 -ast 1 -aet 48 ";
	setAttr ".st" 6;
createNode lambert -n "asWhite";
	setAttr ".c" -type "float3" 1 1 1 ;
createNode shadingEngine -n "asWhiteSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo7";
createNode lambert -n "asBlack";
	setAttr ".c" -type "float3" 0 0 0 ;
createNode shadingEngine -n "asBlackSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo8";
createNode lambert -n "asBones";
	setAttr ".c" -type "float3" 0.77999997 0.75999999 0.72000003 ;
createNode shadingEngine -n "asBonesSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo9";
createNode extrude -n "extrude18";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode extrude -n "extrude19";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode reverseSurface -n "reverseSurface9";
createNode extrude -n "extrude20";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode extrude -n "extrude21";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode reverseSurface -n "reverseSurface10";
createNode extrude -n "extrude22";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode extrude -n "extrude23";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode reverseSurface -n "reverseSurface11";
createNode extrude -n "extrude24";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode extrude -n "extrude25";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode reverseSurface -n "reverseSurface12";
createNode extrude -n "extrude26";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode extrude -n "extrude27";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode reverseSurface -n "reverseSurface13";
createNode extrude -n "extrude28";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode extrude -n "extrude29";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode reverseSurface -n "reverseSurface14";
createNode extrude -n "extrude30";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode reverseSurface -n "reverseSurface15";
select -ne :time1;
	setAttr -av -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".o" 0;
	setAttr -av ".unw";
	setAttr -k on ".etw";
	setAttr -k on ".tps";
	setAttr -k on ".tms";
lockNode -l 1 ;
select -ne :renderPartition;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 11 ".st";
	setAttr -cb on ".an";
	setAttr -cb on ".pt";
lockNode -l 1 ;
select -ne :initialShadingGroup;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".mwc";
	setAttr -cb on ".an";
	setAttr -cb on ".il";
	setAttr -cb on ".vo";
	setAttr -cb on ".eo";
	setAttr -cb on ".fo";
	setAttr -cb on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :initialParticleSE;
	setAttr -av -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".mwc";
	setAttr -cb on ".an";
	setAttr -cb on ".il";
	setAttr -cb on ".vo";
	setAttr -cb on ".eo";
	setAttr -cb on ".fo";
	setAttr -cb on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :defaultShaderList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 11 ".s";
lockNode -l 1 ;
select -ne :postProcessList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 2 ".p";
lockNode -l 1 ;
select -ne :defaultRenderingList1;
	setAttr -s 2 ".r";
lockNode -l 1 ;
select -ne :renderGlobalsList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
lockNode -l 1 ;
select -ne :defaultRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".macc";
	setAttr -k on ".macd";
	setAttr -k on ".macq";
	setAttr -k on ".mcfr";
	setAttr -cb on ".ifg";
	setAttr -k on ".clip";
	setAttr -k on ".edm";
	setAttr -k on ".edl";
	setAttr -cb on ".ren";
	setAttr -av -k on ".esr";
	setAttr -k on ".ors";
	setAttr -cb on ".sdf";
	setAttr -av -k on ".outf";
	setAttr -cb on ".imfkey";
	setAttr -k on ".gama";
	setAttr -k on ".an";
	setAttr -cb on ".ar";
	setAttr -k on ".fs" 1;
	setAttr -k on ".ef" 10;
	setAttr -av -k on ".bfs";
	setAttr -cb on ".me";
	setAttr -cb on ".se";
	setAttr -k on ".be";
	setAttr -cb on ".ep";
	setAttr -k on ".fec";
	setAttr -av -k on ".ofc";
	setAttr -cb on ".ofe";
	setAttr -cb on ".efe";
	setAttr -cb on ".oft";
	setAttr -cb on ".umfn";
	setAttr -cb on ".ufe";
	setAttr -cb on ".pff";
	setAttr -cb on ".peie";
	setAttr -cb on ".ifp";
	setAttr -k on ".comp";
	setAttr -k on ".cth";
	setAttr -k on ".soll";
	setAttr -k on ".sosl";
	setAttr -k on ".rd";
	setAttr -k on ".lp";
	setAttr -av -k on ".sp";
	setAttr -k on ".shs";
	setAttr -av -k on ".lpr";
	setAttr -cb on ".gv";
	setAttr -cb on ".sv";
	setAttr -k on ".mm";
	setAttr -k on ".npu";
	setAttr -k on ".itf";
	setAttr -k on ".shp";
	setAttr -cb on ".isp";
	setAttr -k on ".uf";
	setAttr -k on ".oi";
	setAttr -k on ".rut";
	setAttr -cb on ".mb";
	setAttr -av -k on ".mbf";
	setAttr -k on ".afp";
	setAttr -k on ".pfb";
	setAttr -k on ".pram";
	setAttr -k on ".poam";
	setAttr -k on ".prlm";
	setAttr -k on ".polm";
	setAttr -cb on ".prm";
	setAttr -cb on ".pom";
	setAttr -cb on ".pfrm";
	setAttr -cb on ".pfom";
	setAttr -av -k on ".bll";
	setAttr -k on ".bls";
	setAttr -av -k on ".smv";
	setAttr -k on ".ubc";
	setAttr -k on ".mbc";
	setAttr -cb on ".mbt";
	setAttr -k on ".udbx";
	setAttr -k on ".smc";
	setAttr -k on ".kmv";
	setAttr -cb on ".isl";
	setAttr -cb on ".ism";
	setAttr -cb on ".imb";
	setAttr -k on ".rlen";
	setAttr -av -k on ".frts";
	setAttr -k on ".tlwd";
	setAttr -k on ".tlht";
	setAttr -k on ".jfc";
	setAttr -cb on ".rsb";
	setAttr -k on ".ope";
	setAttr -k on ".oppf";
	setAttr -cb on ".hbl";
select -ne :defaultResolution;
	setAttr -av -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -av -k on ".w";
	setAttr -av -k on ".h";
	setAttr -av -k on ".pa" 1;
	setAttr -av -k on ".al";
	setAttr -av -k on ".dar";
	setAttr -av -k on ".ldar";
	setAttr -k on ".dpi";
	setAttr -av -k on ".off";
	setAttr -av -k on ".fld";
	setAttr -av -k on ".zsl";
	setAttr -k on ".isu";
	setAttr -k on ".pdu";
select -ne :defaultLightSet;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -k on ".mwc";
	setAttr -k on ".an";
	setAttr -k on ".il";
	setAttr -k on ".vo";
	setAttr -k on ".eo";
	setAttr -k on ".fo";
	setAttr -k on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :defaultObjectSet;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -k on ".mwc";
	setAttr -k on ".an";
	setAttr -k on ".il";
	setAttr -k on ".vo";
	setAttr -k on ".eo";
	setAttr -k on ".fo";
	setAttr -k on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :hardwareRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k off ".ctrs" 256;
	setAttr -av -k off ".btrs" 512;
	setAttr -k off ".fbfm";
	setAttr -k off -cb on ".ehql";
	setAttr -k off -cb on ".eams";
	setAttr -k off -cb on ".eeaa";
	setAttr -k off -cb on ".engm";
	setAttr -k off -cb on ".mes";
	setAttr -k off -cb on ".emb";
	setAttr -av -k off -cb on ".mbbf";
	setAttr -k off -cb on ".mbs";
	setAttr -k off -cb on ".trm";
	setAttr -k off -cb on ".tshc";
	setAttr -k off ".enpt";
	setAttr -k off -cb on ".clmt";
	setAttr -k off -cb on ".tcov";
	setAttr -k off -cb on ".lith";
	setAttr -k off -cb on ".sobc";
	setAttr -k off -cb on ".cuth";
	setAttr -k off -cb on ".hgcd";
	setAttr -k off -cb on ".hgci";
	setAttr -k off -cb on ".mgcs";
	setAttr -k off -cb on ".twa";
	setAttr -k off -cb on ".twz";
	setAttr -k on ".hwcc";
	setAttr -k on ".hwdp";
	setAttr -k on ".hwql";
	setAttr -k on ".hwfr";
	setAttr -k on ".soll";
	setAttr -k on ".sosl";
	setAttr -k on ".bswa";
	setAttr -k on ".shml";
	setAttr -k on ".hwel";
lockNode -l 1 ;
select -ne :hardwareRenderingGlobals;
	setAttr ".otfna" -type "stringArray" 22 "NURBS Curves" "NURBS Surfaces" "Polygons" "Subdiv Surface" "Particles" "Particle Instance" "Fluids" "Strokes" "Image Planes" "UI" "Lights" "Cameras" "Locators" "Joints" "IK Handles" "Deformers" "Motion Trails" "Components" "Hair Systems" "Follicles" "Misc. UI" "Ornaments"  ;
	setAttr ".otfva" -type "Int32Array" 22 0 1 1 1 1 1
		 1 1 1 0 0 0 0 0 0 0 0 0
		 0 0 0 0 ;
	setAttr ".fprt" yes;
select -ne :defaultHardwareRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -av -k on ".rp";
	setAttr -k on ".cai";
	setAttr -k on ".coi";
	setAttr -cb on ".bc";
	setAttr -av -k on ".bcb";
	setAttr -av -k on ".bcg";
	setAttr -av -k on ".bcr";
	setAttr -k on ".ei";
	setAttr -av -k on ".ex";
	setAttr -av -k on ".es";
	setAttr -av -k on ".ef";
	setAttr -av -k on ".bf";
	setAttr -k on ".fii";
	setAttr -av -k on ".sf";
	setAttr -k on ".gr";
	setAttr -k on ".li";
	setAttr -k on ".ls";
	setAttr -av -k on ".mb";
	setAttr -k on ".ti";
	setAttr -k on ".txt";
	setAttr -k on ".mpr";
	setAttr -k on ".wzd";
	setAttr -k on ".fn" -type "string" "im";
	setAttr -k on ".if";
	setAttr -k on ".res" -type "string" "ntsc_4d 646 485 1.333";
	setAttr -k on ".as";
	setAttr -k on ".ds";
	setAttr -k on ".lm";
	setAttr -av -k on ".fir";
	setAttr -k on ".aap";
	setAttr -av -k on ".gh";
	setAttr -cb on ".sd";
lockNode -l 1 ;
connectAttr "MainCurves.di" "eyeLidMainCurve.do";
connectAttr "eyeLidMainLocShape0.wp" "eyeLidMainCurveShape.cp[0]";
connectAttr "eyeLidMainLocShape1.wp" "eyeLidMainCurveShape.cp[1]";
connectAttr "eyeLidMainLocShape2.wp" "eyeLidMainCurveShape.cp[2]";
connectAttr "eyeLidMainLocShape3.wp" "eyeLidMainCurveShape.cp[3]";
connectAttr "eyeLidMainLocShape4.wp" "eyeLidMainCurveShape.cp[4]";
connectAttr "eyeLidMainLocShape5.wp" "eyeLidMainCurveShape.cp[5]";
connectAttr "eyeLidMainLocShape6.wp" "eyeLidMainCurveShape.cp[6]";
connectAttr "eyeLidMainLocShape7.wp" "eyeLidMainCurveShape.cp[7]";
connectAttr "eyeLidMainLocShape0.wp" "eyeLidMainCurveShape.cp[8]";
connectAttr "MainCurves.di" "EyeBrowMainCurve2.do";
connectAttr "eyeBrowMainLocShape0.wp" "EyeBrowMainCurve2Shape.cp[0]";
connectAttr "eyeBrowMainLocShape1.wp" "EyeBrowMainCurve2Shape.cp[1]";
connectAttr "eyeBrowMainLocShape2.wp" "EyeBrowMainCurve2Shape.cp[2]";
connectAttr "eyeBrowMainLocShape3.wp" "EyeBrowMainCurve2Shape.cp[3]";
connectAttr "MainCurves.di" "upperLipMainCurve2.do";
connectAttr "upperLipMainLocShape0.wp" "upperLipMainCurve2Shape.cp[0]";
connectAttr "upperLipMainLocShape1.wp" "upperLipMainCurve2Shape.cp[1]";
connectAttr "lipMainLocShape0.wp" "upperLipMainCurve2Shape.cp[2]";
connectAttr "MainCurves.di" "lowerLipMainCurve2.do";
connectAttr "lowerLipMainLocShape0.wp" "lowerLipMainCurve2Shape.cp[0]";
connectAttr "lowerLipMainLocShape1.wp" "lowerLipMainCurve2Shape.cp[1]";
connectAttr "lipMainLocShape0.wp" "lowerLipMainCurve2Shape.cp[2]";
connectAttr "MainCurves.di" "JawCurveMid.do";
connectAttr "lipMainLocShape0.wp" "JawCurveMidShape.cp[0]";
connectAttr "JawMidLocShape1.wp" "JawCurveMidShape.cp[1]";
connectAttr "JawMidLocShape2.wp" "JawCurveMidShape.cp[2]";
connectAttr "MainCurves.di" "EarCurve.do";
connectAttr "EarLocShape0.wp" "EarCurveShape.cp[0]";
connectAttr "EarLocShape1.wp" "EarCurveShape.cp[1]";
connectAttr "EarLocShape2.wp" "EarCurveShape.cp[2]";
connectAttr "EarLocShape3.wp" "EarCurveShape.cp[3]";
connectAttr "EarLocShape4.wp" "EarCurveShape.cp[4]";
connectAttr "EarLocShape5.wp" "EarCurveShape.cp[5]";
connectAttr "ProfileCurves.di" "HeadProfileSideCurve.do";
connectAttr "HeadProfileSideLoc0Shape.wp" "HeadProfileSideCurveShape.cp[0]";
connectAttr "HeadProfileSideLoc1Shape.wp" "HeadProfileSideCurveShape.cp[1]";
connectAttr "HeadProfileSideLoc2Shape.wp" "HeadProfileSideCurveShape.cp[2]";
connectAttr "HeadProfileSideLoc3Shape.wp" "HeadProfileSideCurveShape.cp[3]";
connectAttr "HeadProfileSideLoc4Shape.wp" "HeadProfileSideCurveShape.cp[4]";
connectAttr "HeadProfileSideLoc5Shape.wp" "HeadProfileSideCurveShape.cp[5]";
connectAttr "HeadProfileSideLoc6Shape.wp" "HeadProfileSideCurveShape.cp[6]";
connectAttr "HeadProfileSideLoc0Shape.wp" "HeadProfileSideCurveShape.cp[10]";
connectAttr "HeadProfileSideLoc1Shape.wp" "HeadProfileSideCurveShape.cp[11]";
connectAttr "HeadProfileSideLoc2Shape.wp" "HeadProfileSideCurveShape.cp[12]";
connectAttr "HeadProfileSideLoc3Shape.wp" "HeadProfileSideCurveShape.cp[13]";
connectAttr "HeadProfileSideLoc4Shape.wp" "HeadProfileSideCurveShape.cp[14]";
connectAttr "HeadProfileSideLoc5Shape.wp" "HeadProfileSideCurveShape.cp[15]";
connectAttr "HeadProfileSideLoc6Shape.wp" "HeadProfileSideCurveShape.cp[16]";
connectAttr "ProfileCurves.di" "HeadProfileFrontCurve.do";
connectAttr "HeadProfileFrontLocShape0.wp" "HeadProfileFrontCurveShape.cp[0]";
connectAttr "HeadProfileFrontLocShape1.wp" "HeadProfileFrontCurveShape.cp[1]";
connectAttr "HeadProfileFrontLocShape2.wp" "HeadProfileFrontCurveShape.cp[2]";
connectAttr "HeadProfileFrontLocShape3.wp" "HeadProfileFrontCurveShape.cp[3]";
connectAttr "HeadProfileFrontLocShape4.wp" "HeadProfileFrontCurveShape.cp[4]";
connectAttr "HeadProfileFrontLocShape5.wp" "HeadProfileFrontCurveShape.cp[5]";
connectAttr "ProfileCurves.di" "noseProfileCurve.do";
connectAttr "noseProfileLocShape0.wp" "noseProfileCurveShape.cp[0]";
connectAttr "noseProfileLocShape1.wp" "noseProfileCurveShape.cp[1]";
connectAttr "noseProfileLocShape2.wp" "noseProfileCurveShape.cp[2]";
connectAttr "noseProfileLocShape3.wp" "noseProfileCurveShape.cp[3]";
connectAttr "noseProfileLocShape4.wp" "noseProfileCurveShape.cp[4]";
connectAttr "ProfileCurves.di" "chinProfileCurve.do";
connectAttr "chinProfileLocShape0.wp" "chinProfileCurveShape.cp[0]";
connectAttr "chinProfileLocShape1.wp" "chinProfileCurveShape.cp[1]";
connectAttr "chinProfileLocShape2.wp" "chinProfileCurveShape.cp[2]";
connectAttr "FaceFitSkeleton.sy" "FaceFitSkeleton.sx";
connectAttr "FaceFitSkeleton.sy" "FaceFitSkeleton.sz";
connectAttr "JawPivot.t" "JawPivotSphere.t";
connectAttr "JawPivot.r" "JawPivotSphere.r";
connectAttr "JawPivot.s" "JawPivotSphere.s";
connectAttr "JawCorner.t" "JawCornerSphere.t";
connectAttr "JawCorner.r" "JawCornerSphere.r";
connectAttr "JawCorner.s" "JawCornerSphere.s";
connectAttr "Jaw.t" "JawSphere.t";
connectAttr "Jaw.r" "JawSphere.r";
connectAttr "Jaw.s" "JawSphere.s";
connectAttr "Throat.t" "ThroatSphere.t";
connectAttr "Throat.r" "ThroatSphere.r";
connectAttr "Throat.s" "ThroatSphere.s";
connectAttr "Nose.t" "NoseSphere.t";
connectAttr "Nose.r" "NoseSphere.r";
connectAttr "Nose.s" "NoseSphere.s";
connectAttr "NoseUnder.t" "NoseUnderSphere.t";
connectAttr "NoseUnder.r" "NoseUnderSphere.r";
connectAttr "NoseUnder.s" "NoseUnderSphere.s";
connectAttr "Cheek.t" "CheekSphere.t";
connectAttr "Cheek.r" "CheekSphere.r";
connectAttr "Cheek.s" "CheekSphere.s";
connectAttr "CheekRaiser.t" "CheekRaiserSphere.t";
connectAttr "CheekRaiser.r" "CheekRaiserSphere.r";
connectAttr "CheekRaiser.s" "CheekRaiserSphere.s";
connectAttr "NoseCorner.t" "NoseCornerSphere.t";
connectAttr "NoseCorner.r" "NoseCornerSphere.r";
connectAttr "NoseCorner.s" "NoseCornerSphere.s";
connectAttr "Line.t" "LineSphere.t";
connectAttr "Line.r" "LineSphere.r";
connectAttr "Line.s" "LineSphere.s";
connectAttr "extrude18.os" "upperLipCylinderOuterShape.cr";
connectAttr "reverseSurface9.os" "lowerLipCylinderOuterShape.cr";
connectAttr "FaceFitLipOuter.radius" "LipProfileOuter.sx";
connectAttr "FaceFitLipOuter.radius" "LipProfileOuter.sy";
connectAttr "FaceFitLipOuter.radius" "LipProfileOuter.sz";
connectAttr "extrude20.os" "upperLipCylinderMainShape.cr";
connectAttr "reverseSurface10.os" "lowerLipCylinderMainShape.cr";
connectAttr "FaceFitLipMain.radius" "LipProfileMain.sx";
connectAttr "FaceFitLipMain.radius" "LipProfileMain.sy";
connectAttr "FaceFitLipMain.radius" "LipProfileMain.sz";
connectAttr "extrude22.os" "upperLipCylinderInnerShape.cr";
connectAttr "reverseSurface11.os" "lowerLipCylinderInnerShape.cr";
connectAttr "FaceFitLipInner.radius" "LipProfileInner.sx";
connectAttr "FaceFitLipInner.radius" "LipProfileInner.sy";
connectAttr "FaceFitLipInner.radius" "LipProfileInner.sz";
connectAttr "extrude24.os" "upperEyeLidCylinderOuterShape.cr";
connectAttr "reverseSurface12.os" "lowerEyeLidCylinderOuterShape.cr";
connectAttr "FaceFitEyeLidOuter.radius" "EyeLidProfileOuter.sx";
connectAttr "FaceFitEyeLidOuter.radius" "EyeLidProfileOuter.sy";
connectAttr "FaceFitEyeLidOuter.radius" "EyeLidProfileOuter.sz";
connectAttr "extrude26.os" "upperEyeLidCylinderMainShape.cr";
connectAttr "reverseSurface13.os" "lowerEyeLidCylinderMainShape.cr";
connectAttr "FaceFitEyeLidMain.radius" "EyeLidProfileMain.sx";
connectAttr "FaceFitEyeLidMain.radius" "EyeLidProfileMain.sy";
connectAttr "FaceFitEyeLidMain.radius" "EyeLidProfileMain.sz";
connectAttr "extrude28.os" "upperEyeLidCylinderInnerShape.cr";
connectAttr "reverseSurface14.os" "lowerEyeLidCylinderInnerShape.cr";
connectAttr "FaceFitEyeLidInner.radius" "EyeLidProfileInner.sx";
connectAttr "FaceFitEyeLidInner.radius" "EyeLidProfileInner.sy";
connectAttr "FaceFitEyeLidInner.radius" "EyeLidProfileInner.sz";
connectAttr "EyeBrowInner.t" "EyeBrowInnerSphere.t";
connectAttr "EyeBrowInner.r" "EyeBrowInnerSphere.r";
connectAttr "EyeBrowInner.s" "EyeBrowInnerSphere.s";
connectAttr "EyeBrowOuter.t" "EyeBrowOuterSphere.t";
connectAttr "EyeBrowOuter.r" "EyeBrowOuterSphere.r";
connectAttr "EyeBrowOuter.s" "EyeBrowOuterSphere.s";
connectAttr "reverseSurface15.os" "ForeHeadCylinderShape.cr";
connectAttr "FaceFitForeHead.radius" "ForeHeadProfile.sx";
connectAttr "FaceFitForeHead.radius" "ForeHeadProfile.sy";
connectAttr "FaceFitForeHead.radius" "ForeHeadProfile.sz";
relationship "link" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asRedSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asRed2SG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asGreenSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asGreen2SG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asBlueSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asBlue2SG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asWhiteSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asBlackSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asBonesSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asRedSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asRed2SG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asGreenSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asGreen2SG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asBlueSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asBlue2SG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asWhiteSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asBlackSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asBonesSG.message" ":defaultLightSet.message";
connectAttr "layerManager.dli[0]" "defaultLayer.id";
connectAttr "renderLayerManager.rlmi[0]" "defaultRenderLayer.rlid";
connectAttr "renderLayerManager1.rlmi[0]" "defaultRenderLayer1.rlid";
connectAttr "layerManager.dli[2]" "ProfileCurves.id";
connectAttr "layerManager.dli[1]" "MainCurves.id";
connectAttr "asRed.oc" "asRedSG.ss";
connectAttr "upperLipCylinderMainShape.iog" "asRedSG.dsm" -na;
connectAttr "LipInnerAreaMeshShape.iog" "asRedSG.dsm" -na;
connectAttr "upperEyeLidCylinderMainShape.iog" "asRedSG.dsm" -na;
connectAttr "EyeLidInnerAreaMeshShape.iog" "asRedSG.dsm" -na;
connectAttr "asRedSG.msg" "materialInfo1.sg";
connectAttr "asRed.msg" "materialInfo1.m";
connectAttr "asRed2.oc" "asRed2SG.ss";
connectAttr "lowerLipCylinderMainShape.iog" "asRed2SG.dsm" -na;
connectAttr "lowerEyeLidCylinderMainShape.iog" "asRed2SG.dsm" -na;
connectAttr "asRed2SG.msg" "materialInfo2.sg";
connectAttr "asRed2.msg" "materialInfo2.m";
connectAttr "asGreen.oc" "asGreenSG.ss";
connectAttr "upperLipCylinderOuterShape.iog" "asGreenSG.dsm" -na;
connectAttr "upperEyeLidCylinderOuterShape.iog" "asGreenSG.dsm" -na;
connectAttr "ForeHeadCylinderShape.iog" "asGreenSG.dsm" -na;
connectAttr "ForeHeadAreaMeshShape.iog" "asGreenSG.dsm" -na;
connectAttr "asGreenSG.msg" "materialInfo3.sg";
connectAttr "asGreen.msg" "materialInfo3.m";
connectAttr "asGreen2.oc" "asGreen2SG.ss";
connectAttr "lowerLipCylinderOuterShape.iog" "asGreen2SG.dsm" -na;
connectAttr "lowerEyeLidCylinderOuterShape.iog" "asGreen2SG.dsm" -na;
connectAttr "asGreen2SG.msg" "materialInfo4.sg";
connectAttr "asGreen2.msg" "materialInfo4.m";
connectAttr "asBlue.oc" "asBlueSG.ss";
connectAttr "upperLipCylinderInnerShape.iog" "asBlueSG.dsm" -na;
connectAttr "upperEyeLidCylinderInnerShape.iog" "asBlueSG.dsm" -na;
connectAttr "asBlueSG.msg" "materialInfo5.sg";
connectAttr "asBlue.msg" "materialInfo5.m";
connectAttr "asBlue2.oc" "asBlue2SG.ss";
connectAttr "JawPivotSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "JawCornerSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "JawSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "ThroatSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "NoseSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "NoseUnderSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "CheekSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "CheekRaiserSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "NoseCornerSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "LineSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "lowerLipCylinderInnerShape.iog" "asBlue2SG.dsm" -na;
connectAttr "lowerEyeLidCylinderInnerShape.iog" "asBlue2SG.dsm" -na;
connectAttr "EyeBrowInnerSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "EyeBrowOuterSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "asBlue2SG.msg" "materialInfo6.sg";
connectAttr "asBlue2.msg" "materialInfo6.m";
connectAttr "asWhite.oc" "asWhiteSG.ss";
connectAttr "asWhiteSG.msg" "materialInfo7.sg";
connectAttr "asWhite.msg" "materialInfo7.m";
connectAttr "asBlack.oc" "asBlackSG.ss";
connectAttr "asBlackSG.msg" "materialInfo8.sg";
connectAttr "asBlack.msg" "materialInfo8.m";
connectAttr "asBones.oc" "asBonesSG.ss";
connectAttr "asBonesSG.msg" "materialInfo9.sg";
connectAttr "asBones.msg" "materialInfo9.m";
connectAttr "LipProfileOuterShape.ws" "extrude18.pr";
connectAttr "upperLipOuterCurveShape.ws" "extrude18.pt";
connectAttr "LipProfileOuterShape.ws" "extrude19.pr";
connectAttr "lowerLipOuterCurveShape.ws" "extrude19.pt";
connectAttr "extrude19.os" "reverseSurface9.is";
connectAttr "LipProfileMainShape.ws" "extrude20.pr";
connectAttr "upperLipMainCurveShape.ws" "extrude20.pt";
connectAttr "LipProfileMainShape.ws" "extrude21.pr";
connectAttr "lowerLipMainCurveShape.ws" "extrude21.pt";
connectAttr "extrude21.os" "reverseSurface10.is";
connectAttr "LipProfileInnerShape.ws" "extrude22.pr";
connectAttr "upperLipInnerCurveShape.ws" "extrude22.pt";
connectAttr "LipProfileInnerShape.ws" "extrude23.pr";
connectAttr "lowerLipInnerCurveShape.ws" "extrude23.pt";
connectAttr "extrude23.os" "reverseSurface11.is";
connectAttr "EyeLidProfileOuterShape.ws" "extrude24.pr";
connectAttr "upperEyeLidOuterCurveShape.ws" "extrude24.pt";
connectAttr "EyeLidProfileOuterShape.ws" "extrude25.pr";
connectAttr "lowerEyeLidOuterCurveShape.ws" "extrude25.pt";
connectAttr "extrude25.os" "reverseSurface12.is";
connectAttr "EyeLidProfileMainShape.ws" "extrude26.pr";
connectAttr "upperEyeLidMainCurveShape.ws" "extrude26.pt";
connectAttr "EyeLidProfileMainShape.ws" "extrude27.pr";
connectAttr "lowerEyeLidMainCurveShape.ws" "extrude27.pt";
connectAttr "extrude27.os" "reverseSurface13.is";
connectAttr "EyeLidProfileInnerShape.ws" "extrude28.pr";
connectAttr "upperEyeLidInnerCurveShape.ws" "extrude28.pt";
connectAttr "EyeLidProfileInnerShape.ws" "extrude29.pr";
connectAttr "lowerEyeLidInnerCurveShape.ws" "extrude29.pt";
connectAttr "extrude29.os" "reverseSurface14.is";
connectAttr "ForeHeadProfileShape.ws" "extrude30.pr";
connectAttr "ForeHeadCurveShape.ws" "extrude30.pt";
connectAttr "extrude30.os" "reverseSurface15.is";
connectAttr "asRedSG.pa" ":renderPartition.st" -na;
connectAttr "asRed2SG.pa" ":renderPartition.st" -na;
connectAttr "asGreenSG.pa" ":renderPartition.st" -na;
connectAttr "asGreen2SG.pa" ":renderPartition.st" -na;
connectAttr "asBlueSG.pa" ":renderPartition.st" -na;
connectAttr "asBlue2SG.pa" ":renderPartition.st" -na;
connectAttr "asWhiteSG.pa" ":renderPartition.st" -na;
connectAttr "asBlackSG.pa" ":renderPartition.st" -na;
connectAttr "asBonesSG.pa" ":renderPartition.st" -na;
connectAttr "headTopologyShape.iog" ":initialShadingGroup.dsm" -na;
connectAttr "asRed.msg" ":defaultShaderList1.s" -na;
connectAttr "asRed2.msg" ":defaultShaderList1.s" -na;
connectAttr "asGreen.msg" ":defaultShaderList1.s" -na;
connectAttr "asGreen2.msg" ":defaultShaderList1.s" -na;
connectAttr "asBlue.msg" ":defaultShaderList1.s" -na;
connectAttr "asBlue2.msg" ":defaultShaderList1.s" -na;
connectAttr "asWhite.msg" ":defaultShaderList1.s" -na;
connectAttr "asBlack.msg" ":defaultShaderList1.s" -na;
connectAttr "asBones.msg" ":defaultShaderList1.s" -na;
connectAttr "defaultRenderLayer.msg" ":defaultRenderingList1.r" -na;
connectAttr "defaultRenderLayer1.msg" ":defaultRenderingList1.r" -na;
// End of loHead.ma
