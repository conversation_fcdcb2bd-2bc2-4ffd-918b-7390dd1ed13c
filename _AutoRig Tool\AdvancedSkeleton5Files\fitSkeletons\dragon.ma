//Maya ASCII 2012 scene
//Name: dragon.ma
//Last modified: <PERSON><PERSON>, Jul 31, 2018 09:20:03 PM
//Codeset: 1252
requires maya "2008";
fileInfo "application" "maya";
fileInfo "product" "Maya 2012";
fileInfo "version" "2012 x64";
fileInfo "cutIdentifier" "201201172029-821146";
fileInfo "osv" "Microsoft Business Edition, 64-bit  (Build 9200)\n";
createNode transform -s -n "persp";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -117.51180438930714 148.34212005335294 142.76052747852503 ;
	setAttr ".r" -type "double3" -37.538352764222942 -36.599999999998552 0 ;
	setAttr ".rp" -type "double3" 1.5543122344752192e-015 1.7763568394002505e-015 3.5527136788005009e-015 ;
	setAttr ".rpt" -type "double3" -3.2065984757165251e-013 2.0263643277600396e-013 
		-1.3353096423216151e-012 ;
createNode camera -s -n "perspShape" -p "persp";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".fl" 55;
	setAttr ".ncp" 1;
	setAttr ".fcp" 10000000;
	setAttr ".coi" 224.99178232980154;
	setAttr ".imn" -type "string" "persp";
	setAttr ".den" -type "string" "persp_depth";
	setAttr ".man" -type "string" "persp_mask";
	setAttr ".tp" -type "double3" -3.4937315134491769 -1.7714658418820193 3.1141689183426706 ;
	setAttr ".hc" -type "string" "viewSet -p %camera";
createNode transform -s -n "top";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -2.9183565038109962 5888.6539394447664 0.22212883343222278 ;
	setAttr ".r" -type "double3" -89.999999999999972 -2.0559311560195166e-017 2.0559311560195157e-017 ;
	setAttr ".rpt" -type "double3" 0 2.0194839173657902e-028 -1.0097419586828951e-027 ;
createNode camera -s -n "topShape" -p "top";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".coi" 5878.6539394447664;
	setAttr ".ow" 8.225304558381664;
	setAttr ".imn" -type "string" "top";
	setAttr ".den" -type "string" "top_depth";
	setAttr ".man" -type "string" "top_mask";
	setAttr ".tp" -type "double3" -0.17747617697572837 10 2.0159145859535657 ;
	setAttr ".hc" -type "string" "viewSet -t %camera";
	setAttr ".o" yes;
createNode transform -s -n "front";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -1.7420210867011079 5.2952963433099729 5087.1716136116083 ;
createNode camera -s -n "frontShape" -p "front";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".coi" 5091.7192493097509;
	setAttr ".ow" 26.823724603738572;
	setAttr ".imn" -type "string" "front";
	setAttr ".den" -type "string" "front_depth";
	setAttr ".man" -type "string" "front_mask";
	setAttr ".tp" -type "double3" -1.7420210867011079 5.2952963433099729 -4.5476356981425852 ;
	setAttr ".hc" -type "string" "viewSet -f %camera";
	setAttr ".o" yes;
createNode transform -s -n "side";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 8051.0444894309467 0.96958653879008638 -1.2973755749751261 ;
	setAttr ".r" -type "double3" -3.4557703524278578e-019 89.999999999999986 0 ;
	setAttr ".rpt" -type "double3" 6.3108872417680944e-030 0 0 ;
createNode camera -s -n "sideShape" -p "side";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".coi" 8053.1547599087025;
	setAttr ".ow" 6.0623494309549706;
	setAttr ".imn" -type "string" "side";
	setAttr ".den" -type "string" "side_depth";
	setAttr ".man" -type "string" "side_mask";
	setAttr ".tp" -type "double3" -2.1102704777558756 0.035122969321497562 -0.45371414268045901 ;
	setAttr ".hc" -type "string" "viewSet -s %camera";
	setAttr ".o" yes;
createNode transform -n "FitSkeleton";
	addAttr -ci true -k true -sn "visGeo" -ln "visGeo" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visGeoType" -ln "visGeoType" -min 0 -max 3 -en "cylinders:boxes:spheres:bones" 
		-at "enum";
	addAttr -ci true -sn "visCylinders" -ln "visCylinders" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "visBoxes" -ln "visBoxes" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "visSpheres" -ln "visSpheres" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "visBones" -ln "visBones" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "lockCenterJoints" -ln "lockCenterJoints" -dv 1 -min 
		0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visGap" -ln "visGap" -dv 0.75 -min 0 -max 1 -at "double";
	addAttr -ci true -k true -sn "visPoleVector" -ln "visPoleVector" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visJointOrient" -ln "visJointOrient" -min 0 -max 1 
		-at "bool";
	addAttr -ci true -k true -sn "visJointAxis" -ln "visJointAxis" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "preRebuildScript" -ln "preRebuildScript" -dt "string";
	addAttr -ci true -sn "postRebuildScript" -ln "postRebuildScript" -dt "string";
	addAttr -ci true -sn "run" -ln "run" -dt "string";
	addAttr -ci true -m -im false -sn "drivingSystem" -ln "drivingSystem" -at "message";
	addAttr -ci true -m -sn "drivingSystem_toesCurl_R" -ln "drivingSystem_toesCurl_R" 
		-dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -m -sn "drivingSystem_toesCurl_L" -ln "drivingSystem_toesCurl_L" 
		-dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -m -sn "drivingSystem_fingers_R" -ln "drivingSystem_fingers_R" 
		-dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -m -sn "drivingSystem_fingers_L" -ln "drivingSystem_fingers_L" 
		-dv 1 -min 0 -max 1 -at "bool";
	setAttr -l on ".v";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -k on ".visGeoType" 1;
	setAttr ".visBoxes" yes;
	setAttr -k on ".lockCenterJoints" no;
	setAttr -k on ".visGap" 1;
	setAttr ".run" -type "string" ";setAttr FKIKSplineTail_M.FKIKBlend 10;setAttr FKIKSplineNeck_M.FKIKBlend 10;setAttr FKIKSpine_M.FKIKBlend 10;";
	setAttr -s 60 ".drivingSystem";
	setAttr -s 11 ".drivingSystem_toesCurl_R";
	setAttr -s 11 ".drivingSystem_toesCurl_R";
	setAttr -s 11 ".drivingSystem_toesCurl_L";
	setAttr -s 11 ".drivingSystem_toesCurl_L";
	setAttr -s 19 ".drivingSystem_fingers_R";
	setAttr -s 19 ".drivingSystem_fingers_R";
	setAttr -s 19 ".drivingSystem_fingers_L";
	setAttr -s 19 ".drivingSystem_fingers_L";
createNode nurbsCurve -n "FitSkeletonShape" -p "FitSkeleton";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 29;
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		4.8354296362566176 2.9608467132719543e-016 -4.8354296362566087
		-7.8017170638366795e-016 4.1872695780170065e-016 -6.8383301714949107
		-4.8354296362566105 2.9608467132719543e-016 -4.8354296362566105
		-6.8383301714949107 1.8394931520376138e-031 -3.1095074659834977e-015
		-4.8354296362566105 -2.9608467132719538e-016 4.8354296362566087
		-2.0605223383323332e-015 -4.187269578017007e-016 6.8383301714949107
		4.8354296362566087 -2.9608467132719543e-016 4.8354296362566087
		6.8383301714949107 -1.622863473062071e-031 2.5449500110789744e-015
		4.8354296362566176 2.9608467132719543e-016 -4.8354296362566087
		-7.8017170638366795e-016 4.1872695780170065e-016 -6.8383301714949107
		-4.8354296362566105 2.9608467132719543e-016 -4.8354296362566105
		;
createNode joint -n "Root" -p "FitSkeleton";
	addAttr -ci true -sn "run" -ln "run" -dt "string";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.25744699893917217 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".ove" yes;
	addAttr -ci true -k true -sn "centerBtwFeet" -ln "centerBtwFeet" -dv 1 -min 0 -max 
		1 -at "bool";
	addAttr -ci true -k true -sn "numMainExtras" -ln "numMainExtras" -min 0 -at "long";
	setAttr ".t" -type "double3" 0 10 -0.81730826779243471 ;
	setAttr ".ro" 3;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 90.000000000027185 -90 89.999999999972829 ;
	setAttr ".bps" -type "matrix" 4.3298697960381105e-015 0.011729042580818545 0.9999312124142028 0
		 -1.8737615625763479e-014 -0.9999312124142028 0.011729042580818545 0 1 -1.8787055244828821e-014 -4.2188474935755949e-015 0
		 0 418.78607209451673 -32.692330711697387 1;
	setAttr ".dl" yes;
	setAttr ".typ" 1;
	setAttr -k on ".run";
	setAttr -k on ".fat" 2.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";

createNode joint -n "Spine1" -p "Root";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 88.312621463069604 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 2.5941380949752997 1.3145040611561853e-013 -2.1861623576228998e-016 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.5187257321399325e-015 -1.9083328088781094e-014 
		4.4979835663949435e-015 ;
	setAttr -k on ".fat" 2.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";

createNode joint -n "Tail0" -p "Root";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 58.5054038812928 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "flipOrient" -ln "flipOrient" -dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" -0.97178662747305666 -1.2434497875801753e-014 1.3600158152953666e-014 ;
	setAttr ".r" -type "double3" 0 2.7988881196878945e-013 0 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0 179.99999999999969 0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "0Tail";
	setAttr -k on ".fat" 2.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Tail1" -p "Tail0";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.45688826117041126 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" 5.0000000000000018 7.1054273576010019e-014 -6.0230056655112545e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.5187257321399088e-015 5.6679433323611379e-013 -4.4979835663949679e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "1";
	setAttr -k on ".fat" 2.3;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Tail2" -p "Tail1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 63.075500209582785 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 4.9999999999999964 -9.5923269327613525e-014 -8.2970842235258023e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.5187257321399088e-015 5.6679433323611379e-013 -4.4979835663949679e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "2";
	setAttr -k on ".fat" 2.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Tail3" -p "Tail2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 59.219589052295234 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 4.9999999999999858 -1.9539925233402755e-014 -3.8934241377639281e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.5187257321399088e-015 5.6679433323611379e-013 -4.4979835663949679e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "3";
	setAttr -k on ".fat" 1.9;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Tail4" -p "Tail3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 55.363677895007683 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 5.0000000000000178 2.6645352591003757e-014 -2.7533029155313801e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.5187257321399088e-015 5.6679433323611379e-013 -4.4979835663949679e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "4";
	setAttr -k on ".fat" 1.7;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Tail5" -p "Tail4";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 51.507766737720125 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 5.0000000000000142 -1.0125233984581428e-013 -3.5090300660701049e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.5187257321399088e-015 5.6679433323611379e-013 -4.4979835663949679e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "5";
	setAttr -k on ".fat" 1.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Tail6" -p "Tail5";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 47.651855580432574 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 4.9999999999999822 -3.5527136788005009e-015 -2.4315020206458812e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.5187257321399088e-015 5.6679433323611379e-013 -4.4979835663949679e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "6";
	setAttr -k on ".fat" 1.3;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Tail7" -p "Tail6";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.14999999999999902 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" 5.0000000000000036 -3.1974423109204508e-014 -3.1299663339340869e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.5187257321399088e-015 5.6679433323611379e-013 -4.4979835663949679e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "7";
	setAttr -k on ".fat" 1.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Tail8" -p "Tail7";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 39.940033265857465 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 4.9999999999999858 -3.5527136788005009e-015 -3.5024477258172674e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.5187257321399088e-015 5.6679433323611379e-013 -4.4979835663949679e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "8";
	setAttr -k on ".fat" 0.9;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Tail9" -p "Tail8";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 15.954429765007944 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 5.0000000000000213 -5.3290705182007514e-015 -2.0073477395433059e-014 ;
	setAttr ".r" -type "double3" 1.9697940191701653e-012 3.4452547257358531e-012 -5.7249984266284087e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.5187257321399088e-015 5.6679433323611379e-013 -4.4979835663949679e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "9";
	setAttr -k on ".fat" 0.7;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Hip" -p "Root";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.32656086305614013 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" 0.59527556016123295 0.38059085028360862 -2.1657607641923375 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 179.99999999998531 -1.0117125542961077e-015 74.327337690922064 ;
	setAttr ".dl" yes;
	setAttr ".typ" 2;
	setAttr ".otp" -type "string" "LegAim";
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Knee" -p "Hip";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.33131609538367984 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" 4.1876265810690416 -1.6431300764452317e-014 -2.5979218776228663e-013 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.890512535342733e-015 7.5663836554359512e-015 -83.7085789536699 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Hip1";
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Ankle" -p "Knee";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 26.62668884390515 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 4.1740922648205219 9.1482377229112899e-014 7.3274719625260332e-014 ;
	setAttr ".r" -type "double3" 5.3147189970699484e-013 4.7057515628501526e-013 5.406942958488e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.587772549881556e-014 -7.2341562896794155e-015 96.955442700761921 ;
	setAttr ".dl" yes;
	setAttr ".typ" 4;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Toes1" -p "Ankle";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.037357417561876632 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -sn "reverseKnee" -ln "reverseKnee" -min 0 -max 1 -at "bool";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" 2.703546618629511 2.4980018054066022e-014 -2.3092638912203256e-014 ;
	setAttr ".ro" 3;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 5.1383672368434421e-015 1.8764690286443402e-015 11.747916653998576 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "QToes";
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
	setAttr ".reverseKnee" yes;
createNode joint -n "Toes2" -p "Toes1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.32268411320534407 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" 1.7480816577135259 1.3045120539345589e-015 4.1411318818518339e-012 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.8184672176900338e-014 -9.0658037277065282e-015 37.352884253725435 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "QToes";
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY" 0.29999999999999993;
	setAttr -k on ".fatZ";
createNode joint -n "ToesEnd" -p "Toes2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.32268411320534407 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" 1.7348750191913802 3.1363800445660672e-015 -3.1086244689504383e-015 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.8184672176898892e-014 -9.065803727703633e-015 4.7708320221952767e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "ToesEnd";
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY" 0.2;
	setAttr -k on ".fatZ";
createNode joint -n "ToeBig" -p "Toes2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 26.62668884390515 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -0.24658546886964161 -0.43029157948951952 -0.58017988781868057 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 7.0527307917744049e-015 -7.7007366262655936e-015 7.9513867036587919e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "BigToe";
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "ToePinky" -p "Toes2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 26.62668884390515 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.048437501011334372 -0.27561701845497977 0.93629922366114604 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 7.0527307917744049e-015 -7.7007366262655936e-015 7.9513867036587919e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "PinkyToe";
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyToe1" -p "Toes2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 18.477922439575181 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -0.076472476703496217 0.40945574969422782 0.63074936753502042 ;
	setAttr ".r" -type "double3" 6.9574633657014382e-013 1.8799438735377162e-013 -2.1861343818371774e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 8.0487484708274291e-015 -17.000000000000071 -4.9999999999999964 ;
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyToe2" -p "PinkyToe1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 21.502848944229346 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.60000000000000053 -2.4424906541753444e-015 -1.3322676295501878e-015 ;
	setAttr ".r" -type "double3" 2.1283328440350781e-013 -5.9370354053985665e-013 3.1805546814634063e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -4.7708320221952783e-015 -1.5902773407317588e-015 
		-39.443899810292102 ;
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyToe3" -p "PinkyToe2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 41.376441086294925 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.59999999999999964 2.2204460492503131e-016 7.1054273576010019e-015 ;
	setAttr ".r" -type "double3" -7.7362768452413721e-012 6.7287678176583841e-013 9.5637555435773266e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -4.7708320221952783e-015 -1.5902773407317588e-015 
		1.272221872585407e-014 ;
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleToe1" -p "Toes2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 21.1591987609863 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.0046029478791511647 0.45800561871024237 0.050016744457395124 ;
	setAttr ".r" -type "double3" -3.6106715272498452e-015 8.2697974132527839e-014 8.5079837729149082e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 7.6970564422017529e-015 -7.0567470079341601e-015 -5.0000000000000027 ;
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleToe2" -p "MiddleToe1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 20.671285425576801 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.61535818062319214 9.9920072216264089e-016 -8.8817841970012523e-016 ;
	setAttr ".r" -type "double3" -1.5049800509178268e-013 4.134898706626398e-013 1.2722218725853521e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.8793319585307295e-014 1.3718732271870788e-014 -40.000000000000007 ;
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleToe3" -p "MiddleToe2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 48.695037457774887 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.61535818062319303 4.4408920985006262e-016 -3.9968028886505635e-015 ;
	setAttr ".r" -type "double3" 6.4295813628810016e-012 -1.2306964543779181e-013 3.9907380898544273e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.8793319585302956e-014 1.3718732271869345e-014 6.3611093629270351e-015 ;
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "IndexToe1" -p "Toes2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 20.819784896987279 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -0.058440104962815598 0.38911696906001136 -0.43789301580477358 ;
	setAttr ".r" -type "double3" -2.6686841624154824e-013 5.0157720047931377e-013 -2.6053215496208175e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 8.0487484708281832e-015 17.000000000000053 -4.9999999999999956 ;
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "IndexToe2" -p "IndexToe1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 20.753709308479028 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.61535818062319303 -2.2204460492503131e-016 -4.8849813083506888e-015 ;
	setAttr ".r" -type "double3" -2.9645909526053286e-014 8.2697974132527991e-014 -1.9083328088781123e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.7034714792439904e-014 -4.7708320221952752e-015 
		-39.443899810291732 ;
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "IndexToe3" -p "IndexToe2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 41.417854101580218 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.61535818062319203 4.163336342344337e-017 -1.1102230246251565e-015 ;
	setAttr ".r" -type "double3" -3.9188409368982346e-012 -6.325902734690364e-013 -1.0523745717577104e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.5902773407317587e-014 1.431249606658583e-014 3.1805546814635183e-015 ;
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "ThumbToe1" -p "Toes1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 20.963439770631211 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 1.0492177145984247 -0.14796554255550584 -0.5098051036429383 ;
	setAttr ".r" -type "double3" -3.9756933518294677e-015 1.558471793917123e-013 -5.4069429584879776e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -38.129501950004645 48.912097863817024 2.8683428009200358 ;
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "ThumbToe2" -p "ThumbToe1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 22.073601599437552 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.40995334854871235 -5.5511151231257827e-017 -2.0261570199409107e-015 ;
	setAttr ".r" -type "double3" -1.4286699663582723e-013 3.9252384724223454e-013 -4.8937900522527885e-028 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.2722218725854073e-014 -6.3611093629270375e-015 -40.000000000000028 ;
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "ThumbToe3" -p "ThumbToe2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 45.90539826333481 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.56719822060913205 0 -3.8025138593411612e-015 ;
	setAttr ".r" -type "double3" 8.963324902116618e-014 -5.7709673810148616e-014 4.3359905618389309e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 9.5416640443905535e-015 -9.5416640443905566e-015 -7.1562480332929167e-015 ;
	setAttr -k on ".fat" 0.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";

createNode joint -n "Spine2" -p "Spine1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 71.447749883563063 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 2.5941380949752983 1.4210854715202004e-014 -1.553964262160969e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.5187257321399325e-015 -1.9083328088781094e-014 
		4.4979835663949435e-015 ;
	setAttr ".bps" -type "matrix" 4.5321069354918177e-015 0.022534217351690037 0.99974607228453116 0
		 -1.8689730295399286e-014 -0.99974607228453116 0.022534217351690033 0 1 -1.8787055244828821e-014 -4.2188474935755949e-015 0
		 6.2338142319657656e-013 420.6668156804883 127.64592377092548 1;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Mid";
	setAttr -k on ".fat" 2.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Chest" -p "Spine2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 74.34588842162097 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 1.7710673010770028 -2.3092638912203256e-014 -2.364975820778601e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.5187257321399325e-015 -1.9083328088781094e-014 
		4.4979835663949435e-015 ;
	setAttr ".bps" -type "matrix" 8.7463157861362347e-015 0.25396474555454462 0.96721347592732387 0
		 -1.7127404122011112e-014 -0.96721347592732387 0.25396474555454462 0 1 -1.8787055244828821e-014 -4.2188474935755949e-015 0
		 1.0636730527503151e-012 423.3263607038366 245.63847037947599 1;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Chest";
	setAttr -k on ".fat" 2.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Neck1" -p "Chest";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 64.045434240404688 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 3.0000064449438835 -7.638334409421077e-014 -9.0508869145362464e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.5187257321399325e-015 -1.9083328088781094e-014 
		4.4979835663949435e-015 ;
	setAttr ".bps" -type "matrix" 4.1097976632104425e-015 4.163336342344337e-016 1.0000000000000002 0
		 -1.878711193762812e-014 -1.0000000000000002 4.163336342344337e-016 0 1 -1.8787055244828821e-014 -4.2188474935755949e-015 0
		 1.9357845125436296e-012 459.16168084843326 382.11569867484934 1;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "0Neck";
	setAttr -k on ".fat" 2.5;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Neck2" -p "Neck1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 53.377629815821081 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 2.9999999999999964 4.0856207306205761e-014 -8.0187317362449003e-019 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.5187257321399325e-015 -1.9083328088781094e-014 
		4.4979835663949435e-015 ;
	setAttr ".bps" -type "matrix" 4.1097976632104425e-015 4.163336342344337e-016 1.0000000000000002 0
		 -1.878711193762812e-014 -1.0000000000000002 4.163336342344337e-016 0 1 -1.8787055244828821e-014 -4.2188474935755949e-015 0
		 2.4227117790751862e-012 459.16168084843321 500.60045383684189 1;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "1";
	setAttr -k on ".fat" 2.3;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Neck3" -p "Neck2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 71.354199801369305 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 2.9999999999999982 -2.4868995751603507e-014 -7.4060944747090937e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.5187257321399325e-015 -1.9083328088781094e-014 
		4.4979835663949435e-015 ;
	setAttr ".bps" -type "matrix" 4.1097976632104425e-015 4.163336342344337e-016 1.0000000000000002 0
		 -1.878711193762812e-014 -1.0000000000000002 4.163336342344337e-016 0 1 -1.8787055244828821e-014 -4.2188474935755949e-015 0
		 2.6049673758393485e-012 459.16168084843264 617.02672184222206 1;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "2";
	setAttr -k on ".fat" 2.1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Neck4" -p "Neck3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 51.766871613649073 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 3.0000000000000018 -1.4210854715202004e-014 6.1648198301068282e-015 ;
	setAttr ".r" -type "double3" 1.5234826636435174e-016 3.4748496913468544e-014 -1.7194873746662138e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.0400584054798935e-014 -1.5099459089450568e-016 0.5024019756351199 ;
	setAttr ".bps" -type "matrix" 3.9449056979696776e-015 -0.0087684562777781173 0.99996155634829531 0
		 -1.8822426273560627e-014 -0.99996155634829531 -0.0087684562777781173 0 1 -1.8787055244828821e-014 -4.2188474935755949e-015 0
		 3.2908716639380117e-012 459.16168084843463 723.91775622553052 1;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "3";
	setAttr -k on ".fat" 1.9;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Head" -p "Neck4";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.18344414940536341 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -sn "liw" -ln "lockInfluenceWeights" -min 0 -max 1 -at "bool";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" 3.0000000000000142 1.4210854715202004e-014 -1.8194270437789603e-015 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.2682778353613858e-014 -8.2239189898271132e-015 
		-89.999999999999986 ;
	setAttr ".bps" -type "matrix" 1.8957691399207054e-014 0.99892122154921803 0.046436980291773511 0
		 3.2329473552673078e-015 -0.046436980291773511 0.99892122154921803 0 1 -1.8787055244828821e-014 -4.2188474935755949e-015 0
		 3.634464026475551e-012 458.23604841890977 829.47759657973563 1;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "4";
	setAttr -k on ".fat" 1.7;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "HeadEnd" -p "Head";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.18344414940536341 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -sn "liw" -ln "lockInfluenceWeights" -min 0 -max 1 -at "bool";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" 1.4999999999999787 -3.1974423109204508e-014 -1.2459067458179024e-015 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.2682778353613861e-014 -8.2239189898271163e-015 
		6.4605016967227709e-015 ;
	setAttr ".bps" -type "matrix" 1.8957691399207054e-014 0.99892122154921803 0.046436980291773511 0
		 3.2329473552673078e-015 -0.046436980291773511 0.99892122154921803 0 1 -1.8787055244828821e-014 -4.2188474935755949e-015 0
		 3.634464026475551e-012 458.23604841890977 829.47759657973563 1;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "4N";
	setAttr -k on ".fat" 1.7;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Jaw" -p "Head";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 151.00120651586815 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -0.32623530957259561 0.19018236843251302 -9.0511486274067187e-014 ;
	setAttr ".r" -type "double3" -8.0051507304291271e-015 -5.4595769299906315e-015 3.8166656177562201e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.73420085939367e-015 9.5266302645706892e-015 111.41139773327272 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "JawEnd" -p "Jaw";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 3.7750301628966305 7.1054273576010019e-015 3.5971353845814517e-016 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.0075761435680725e-014 7.8174520584635937e-015 -3.1805546814635183e-015 ;
	setAttr -k on ".fat";
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Scapula" -p "Chest";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.16434865912560742 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" 1.7783445963982025 0 -1.4268522817499423 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 90.000000000000114 89.999999999999986 0 ;
	setAttr ".otp" -type "string" "PropA1";
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "Shoulder" -p "Scapula";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.64310293807879848 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "twistJoints" -ln "twistJoints" -dv 2 -min 0 -max 10 
		-at "long";
	addAttr -ci true -k true -sn "bendyJoints" -ln "bendyJoints" -min 0 -max 1 -at "bool";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" 1.5707269426583861 -4.7961634663806763e-014 0 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.3888984247698334e-014 -4.2349142405070775e-015 
		-24.743416098827137 ;
	setAttr ".pa" -type "double3" -4.1293130717023521e-007 0 0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 10;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
	setAttr -k on ".bendyJoints" yes;
createNode joint -n "Elbow" -p "Shoulder";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.97953586154532324 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "twistJoints" -ln "twistJoints" -dv 2 -min 0 -max 10 
		-at "long";
	addAttr -ci true -k true -sn "bendyJoints" -ln "bendyJoints" -min 0 -max 1 -at "bool";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" 9.4081368233570704 3.3750779948604759e-014 -4.7961634663806763e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.4965169271885424e-014 2.146209572883481e-015 50.000000000000064 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "22";
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
	setAttr -k on ".bendyJoints" yes;
createNode joint -n "Wrist" -p "Elbow";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.58385734483332996 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".ove" yes;
	setAttr ".t" -type "double3" 11.046552172890651 -1.2634338020234281e-013 -2.7249313916399842e-012 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.4965169271885386e-014 2.1462095728834932e-015 -3.1805546814635183e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 12;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "ThumbFinger1" -p "Wrist";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 19.160850777325777 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "curveGuide" -ln "curveGuide" -min 0 -max 0 -en "wingCurve" 
		-at "enum";
	addAttr -ci true -k true -sn "curveGuideMode" -ln "curveGuideMode" -dv 1 -min 0 
		-max 1 -en "point:aim" -at "enum";
	setAttr ".t" -type "double3" 2.1371243636699475 -5.9952043329758453e-015 -7.1054273576010019e-015 ;
	setAttr ".r" -type "double3" -6.4337145309616903e-011 1.0769598576067187e-013 2.098795491762901e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.4441574629359328e-014 0.45468642172972423 -2.1275808471326663 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "ThumbFinger2" -p "ThumbFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 24.684967987339128 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 7.6626922795103312 2.8310687127941492e-014 -1.5987211554602254e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 5.4914264422143547e-015 -9.9392333795734924e-017 9.5416640443905535e-015 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "ThumbFinger3" -p "ThumbFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 16.88607942247382 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 7.6615767180585124 -2.3203661214665772e-014 8.8817841970012523e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 5.4914264422143547e-015 -9.9392333795734924e-017 9.5416640443905535e-015 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "ThumbFinger4" -p "ThumbFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.2883022927432002 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 7.6599899042723507 1.2323475573339238e-014 3.5527136788005009e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 5.4914264422143547e-015 -9.9392333795734924e-017 9.5416640443905535e-015 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "IndexFinger1" -p "Wrist";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 291.17016824294092 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "curveGuide" -ln "curveGuide" -min 0 -max 0 -en "wingCurve" 
		-at "enum";
	addAttr -ci true -k true -sn "curveGuideMode" -ln "curveGuideMode" -dv 1 -min 0 
		-max 1 -en "point:aim" -at "enum";
	setAttr ".t" -type "double3" 1.951329752690949 -0.62239436204851795 0.008340929391305707 ;
	setAttr ".r" -type "double3" 4.1349695667370612e-013 -6.8409879745345784e-014 3.3254937463467745e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -0.15551479784368277 0.42726495065907583 -22.128160701108843 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "0D";
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "IndexFinger2" -p "IndexFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 41.960222422412542 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 12.330537902129432 1.2434497875801753e-014 2.3092638912203256e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.6985018625542027e-014 8.6595570819534077e-015 7.9513867036587959e-016 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "IndexFinger3" -p "IndexFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 28.402915712522763 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 12.330459137129182 2.8421709430404007e-014 -4.2632564145606011e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.6985018625542027e-014 8.6595570819534077e-015 7.9513867036587959e-016 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "IndexFinger4" -p "IndexFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.2883022927432002 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 12.330053588777119 -7.1054273576010019e-015 -1.2434497875801753e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.6985018625542027e-014 8.6595570819534077e-015 7.9513867036587959e-016 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleFinger1" -p "Wrist";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 296.63753441677648 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "curveGuide" -ln "curveGuide" -min 0 -max 0 -en "wingCurve" 
		-at "enum";
	addAttr -ci true -k true -sn "curveGuideMode" -ln "curveGuideMode" -dv 1 -min 0 
		-max 1 -en "point:aim" -at "enum";
	setAttr ".t" -type "double3" 1.6257751735733557 -1.0257737936463263 0.042384821103325976 ;
	setAttr ".r" -type "double3" 1.4994327476424567e-012 4.3881715370816405e-014 4.2124975283543123e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -0.34831302779018014 0.29226499798955763 -52.128469221821732 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "0C";
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleFinger2" -p "MiddleFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 36.893801247645392 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 11.939107934612831 1.0658141036401503e-014 -8.8817841970012523e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.5731543999566697e-014 1.4784609652115569e-015 -3.1805546814635176e-015 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleFinger3" -p "MiddleFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 25.025301596011332 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 11.93829645924718 -6.3948846218409017e-014 3.730349362740526e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.5731543999566697e-014 1.4784609652115569e-015 -3.1805546814635176e-015 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleFinger4" -p "MiddleFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.2883022927432002 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 11.938938638898428 7.1054273576010019e-015 7.1054273576010019e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.5731543999566697e-014 1.4784609652115569e-015 -3.1805546814635176e-015 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "RingFinger1" -p "Wrist";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 269.52186820642351 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "curveGuide" -ln "curveGuide" -min 0 -max 0 -en "wingCurve" 
		-at "enum";
	addAttr -ci true -k true -sn "curveGuideMode" -ln "curveGuideMode" -dv 1 -min 0 
		-max 1 -en "point:aim" -at "enum";
	setAttr ".t" -type "double3" 1.143522160680007 -1.1012345051033561 -0.0030065844952815723 ;
	setAttr ".r" -type "double3" 7.2288044369637987e-013 -7.7675108861368027e-014 1.************384e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -0.44777899674570137 0.078954664810636968 -82.127889372703336 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "0B";
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "RingFinger2" -p "RingFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 36.487127745106143 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 11.298788188871924 3.5527136788005009e-014 2.1316282072803006e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.5902773407317578e-014 -1.987846675914698e-015 6.3611093629270335e-015 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "RingFinger3" -p "RingFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 24.754185927651832 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 11.293541906601817 -4.6185277824406512e-014 3.3750779948604759e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.5902773407317578e-014 -1.987846675914698e-015 6.3611093629270335e-015 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "RingFinger4" -p "RingFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.2883022927432002 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 11.296960773561789 -1.4210854715202004e-014 -1.2434497875801753e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.5902773407317578e-014 -1.987846675914698e-015 6.3611093629270335e-015 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyFinger1" -p "Wrist";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 346.37790904114678 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "curveGuide" -ln "curveGuide" -min 0 -max 0 -en "wingCurve" 
		-at "enum";
	addAttr -ci true -k true -sn "curveGuideMode" -ln "curveGuideMode" -dv 1 -min 0 
		-max 1 -en "point:aim" -at "enum";
	setAttr ".t" -type "double3" 0.4168729404303555 -1.316373380172825 0.019486984275069119 ;
	setAttr ".r" -type "double3" 1.1869184021052173e-012 -8.5775584065719587e-014 3.6236561883100191e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -0.42726652446320623 -0.15551047380022473 -112.12700100714336 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyFinger2" -p "PinkyFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 346.37790904114678 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 7.0634468317076404 1.0658141036401503e-014 1.5987211554602254e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.9927541723958629e-013 -3.9259971849315277e-015 
		-4.7708320221952736e-015 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyFinger3" -p "PinkyFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 231.25193936076454 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 7.0601975301913296 -3.1974423109204508e-014 1.0658141036401503e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.9927541723958629e-013 -3.9259971849315277e-015 
		-4.7708320221952736e-015 ;
	setAttr -k on ".fat" 1;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyFinger4" -p "PinkyFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 7.0589303310559748 2.1316282072803006e-014 3.5527136788005009e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.9927541723958629e-013 -3.9259971849315277e-015 
		-4.7708320221952736e-015 ;
	setAttr -k on ".fat";
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode transform -n "wingCurve" -p "FitSkeleton";
createNode nurbsCurve -n "wingCurveShape" -p "wingCurve";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 2 0 no 3
		7 0 0 0 1 2 2 2
		5
		-24.012654447411517 10 -19.780481497154256
		-44.346352805787177 10 -29.157672487667295
		-65.864538447175022 10 -15.042532365105561
		-72.379218503741953 10 11.805846049837063
		-66.061952994343699 10 28.28996073842314
		;
createNode lightLinker -s -n "lightLinker1";
	setAttr -s 2 ".lnk";
	setAttr -s 2 ".slnk";
createNode displayLayerManager -n "layerManager";
	setAttr ".cdl" 8;
	setAttr -s 8 ".dli[1:7]"  7 8 2 4 3 5 6;

createNode multiplyDivide -n "RootFat";
createNode multiplyDivide -n "Spine1Fat";
createNode multiplyDivide -n "Spine2Fat";
createNode multiplyDivide -n "ChestFat";
createNode multiplyDivide -n "ScapulaFat";
createNode multiplyDivide -n "ShoulderFat";
createNode multiplyDivide -n "ElbowFat";
createNode multiplyDivide -n "WristFat";
createNode multiplyDivide -n "PinkyFinger1Fat";
createNode multiplyDivide -n "PinkyFinger2Fat";
createNode multiplyDivide -n "PinkyFinger3Fat";
createNode multiplyDivide -n "PinkyFinger4Fat";
createNode multiplyDivide -n "RingFinger1Fat";
createNode multiplyDivide -n "RingFinger2Fat";
createNode multiplyDivide -n "RingFinger3Fat";
createNode multiplyDivide -n "RingFinger4Fat";
createNode multiplyDivide -n "MiddleFinger1Fat";
createNode multiplyDivide -n "MiddleFinger2Fat";
createNode multiplyDivide -n "MiddleFinger3Fat";
createNode multiplyDivide -n "MiddleFinger4Fat";
createNode multiplyDivide -n "IndexFinger1Fat";
createNode multiplyDivide -n "IndexFinger2Fat";
createNode multiplyDivide -n "IndexFinger3Fat";
createNode multiplyDivide -n "IndexFinger4Fat";
createNode multiplyDivide -n "ThumbFinger1Fat";
createNode multiplyDivide -n "ThumbFinger2Fat";
createNode multiplyDivide -n "ThumbFinger3Fat";
createNode multiplyDivide -n "ThumbFinger4Fat";
createNode multiplyDivide -n "Neck1Fat";
createNode multiplyDivide -n "Neck2Fat";
createNode multiplyDivide -n "Neck3Fat";
createNode multiplyDivide -n "Neck4Fat";
createNode multiplyDivide -n "HeadFat";
createNode multiplyDivide -n "JawFat";
createNode multiplyDivide -n "JawEndFat";
createNode multiplyDivide -n "HeadEndFat";
createNode multiplyDivide -n "HipFat";
createNode multiplyDivide -n "KneeFat";
createNode multiplyDivide -n "AnkleFat";
createNode multiplyDivide -n "Toes1Fat";
createNode multiplyDivide -n "ThumbToe1Fat";
createNode multiplyDivide -n "ThumbToe2Fat";
createNode multiplyDivide -n "ThumbToe3Fat";
createNode multiplyDivide -n "Toes2Fat";
createNode multiplyDivide -n "IndexToe1Fat";
createNode multiplyDivide -n "IndexToe2Fat";
createNode multiplyDivide -n "IndexToe3Fat";
createNode multiplyDivide -n "MiddleToe1Fat";
createNode multiplyDivide -n "MiddleToe2Fat";
createNode multiplyDivide -n "MiddleToe3Fat";
createNode multiplyDivide -n "PinkyToe1Fat";
createNode multiplyDivide -n "PinkyToe2Fat";
createNode multiplyDivide -n "PinkyToe3Fat";
createNode multiplyDivide -n "ToePinkyFat";
createNode multiplyDivide -n "ToeBigFat";
createNode multiplyDivide -n "ToesEndFat";
createNode multiplyDivide -n "Tail_0Fat";
createNode multiplyDivide -n "Tail_1Fat";
createNode multiplyDivide -n "Tail_2Fat";
createNode multiplyDivide -n "Tail_3Fat";
createNode multiplyDivide -n "Tail_4Fat";
createNode multiplyDivide -n "Tail_5Fat";
createNode multiplyDivide -n "Tail_6Fat";
createNode multiplyDivide -n "Tail_7Fat";
createNode multiplyDivide -n "Tail_8Fat";
createNode multiplyDivide -n "Tail_9Fat";
createNode animCurveUA -n "SDK1FKPinkyToe1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexToe1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbToe1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 33;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexToe1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbToe1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 33;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexToe2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKIndexToe1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexToe2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKIndexToe1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyToe1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyToe1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbToe2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKThumbToe1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbToe2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKThumbToe1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 14.408433451045594;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -19.007603302490494;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -35.829570466084284;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -53.040392624406117;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 23.319495369999999;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -18.277878130000001;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -23.873753239999999;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -35.960653729999997;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKMiddleFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKMiddleFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKRingFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKRingFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKThumbFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKThumbFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -10 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;

select -ne :time1;
	setAttr -av -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".o" 71;
	setAttr -av ".unw" 71;
	setAttr -av ".tms";
select -ne :renderPartition;
	setAttr -cb on ".cch";
	setAttr -cb on ".ihi";
	setAttr -cb on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 2 ".st";
	setAttr -cb on ".an";
	setAttr -cb on ".pt";
select -ne :initialShadingGroup;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".mwc";
	setAttr -cb on ".an";
	setAttr -cb on ".il";
	setAttr -cb on ".vo";
	setAttr -cb on ".eo";
	setAttr -cb on ".fo";
	setAttr -cb on ".epo";
	setAttr -k on ".ro" yes;
select -ne :initialParticleSE;
	setAttr -av -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -cb on ".mwc";
	setAttr -cb on ".an";
	setAttr -cb on ".il";
	setAttr -cb on ".vo";
	setAttr -cb on ".eo";
	setAttr -cb on ".fo";
	setAttr -cb on ".epo";
	setAttr -k on ".ro" yes;
select -ne :defaultShaderList1;
	setAttr -cb on ".cch";
	setAttr -cb on ".ihi";
	setAttr -cb on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 2 ".s";
select -ne :postProcessList1;
	setAttr -cb on ".cch";
	setAttr -cb on ".ihi";
	setAttr -cb on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 2 ".p";
select -ne :defaultRenderingList1;
	setAttr -s 2 ".r";
select -ne :renderGlobalsList1;
	setAttr -cb on ".cch";
	setAttr -cb on ".ihi";
	setAttr -cb on ".nds";
	setAttr -cb on ".bnm";
select -ne :defaultRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -cb on ".macc";
	setAttr -cb on ".macd";
	setAttr -cb on ".macq";
	setAttr -k on ".mcfr";
	setAttr -cb on ".ifg";
	setAttr -k on ".clip";
	setAttr -k on ".edm";
	setAttr -k on ".edl";
	setAttr -av -k on ".esr";
	setAttr -k on ".ors";
	setAttr -cb on ".sdf";
	setAttr -av -k on ".outf" 3;
	setAttr -cb on ".imfkey" -type "string" "tif";
	setAttr -k on ".gama";
	setAttr -cb on ".an" yes;
	setAttr -cb on ".ar";
	setAttr -k on ".fs";
	setAttr -k on ".ef" 7;
	setAttr -av -k on ".bfs";
	setAttr -cb on ".me";
	setAttr -cb on ".se";
	setAttr -k on ".be";
	setAttr -cb on ".ep";
	setAttr -k on ".fec";
	setAttr -k on ".ofc";
	setAttr -cb on ".ofe";
	setAttr -cb on ".efe";
	setAttr -cb on ".oft";
	setAttr -cb on ".umfn";
	setAttr -cb on ".ufe";
	setAttr -cb on ".pff" yes;
	setAttr -cb on ".peie" 2;
	setAttr -cb on ".ifp";
	setAttr -k on ".comp";
	setAttr -k on ".cth";
	setAttr -k on ".soll";
	setAttr -k on ".rd";
	setAttr -k on ".lp";
	setAttr -av -k on ".sp";
	setAttr -k on ".shs";
	setAttr -k on ".lpr";
	setAttr -cb on ".gv";
	setAttr -cb on ".sv";
	setAttr -k on ".mm";
	setAttr -k on ".npu";
	setAttr -k on ".itf";
	setAttr -k on ".shp";
	setAttr -cb on ".isp";
	setAttr -k on ".uf";
	setAttr -k on ".oi";
	setAttr -k on ".rut";
	setAttr -k on ".mb";
	setAttr -av -k on ".mbf";
	setAttr -k on ".afp";
	setAttr -k on ".pfb";
	setAttr -k on ".pram";
	setAttr -k on ".poam";
	setAttr -k on ".prlm";
	setAttr -k on ".polm";
	setAttr -cb on ".prm";
	setAttr -cb on ".pom";
	setAttr -cb on ".pfrm";
	setAttr -cb on ".pfom";
	setAttr -av -k on ".bll";
	setAttr -k on ".bls";
	setAttr -k on ".smv";
	setAttr -k on ".ubc";
	setAttr -k on ".mbc";
	setAttr -cb on ".mbt";
	setAttr -k on ".udbx";
	setAttr -k on ".smc";
	setAttr -k on ".kmv";
	setAttr -cb on ".isl";
	setAttr -cb on ".ism";
	setAttr -cb on ".imb";
	setAttr -k on ".rlen";
	setAttr -av -k on ".frts";
	setAttr -k on ".tlwd";
	setAttr -k on ".tlht";
	setAttr -k on ".jfc";
	setAttr -cb on ".rsb";
	setAttr -cb on ".ope";
	setAttr -cb on ".oppf";
	setAttr -cb on ".hbl";
select -ne :defaultResolution;
	setAttr -av -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -av -k on ".w" 1280;
	setAttr -av -k on ".h" 720;
	setAttr -av -k on ".pa" 1;
	setAttr -av -k on ".al";
	setAttr -av -k on ".dar" 1.7769999504089355;
	setAttr -av -k on ".ldar";
	setAttr -k on ".dpi";
	setAttr -av -k on ".off";
	setAttr -av -k on ".fld";
	setAttr -av -k on ".zsl";
	setAttr -k on ".isu";
	setAttr -k on ".pdu";
select -ne :defaultLightSet;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -k on ".mwc";
	setAttr -k on ".an";
	setAttr -k on ".il";
	setAttr -k on ".vo";
	setAttr -k on ".eo";
	setAttr -k on ".fo";
	setAttr -k on ".epo";
	setAttr -k on ".ro" yes;
select -ne :defaultObjectSet;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -k on ".mwc";
	setAttr -k on ".an";
	setAttr -k on ".il";
	setAttr -k on ".vo";
	setAttr -k on ".eo";
	setAttr -k on ".fo";
	setAttr -k on ".epo";
	setAttr ".ro" yes;
select -ne :hardwareRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k off ".ctrs" 256;
	setAttr -av -k off ".btrs" 512;
	setAttr -k off ".fbfm";
	setAttr -k off -cb on ".ehql";
	setAttr -k off -cb on ".eams";
	setAttr -k off -cb on ".eeaa";
	setAttr -k off -cb on ".engm";
	setAttr -k off -cb on ".mes";
	setAttr -k off -cb on ".emb";
	setAttr -av -k off -cb on ".mbbf";
	setAttr -k off -cb on ".mbs";
	setAttr -k off -cb on ".trm";
	setAttr -k off -cb on ".tshc";
	setAttr -k off ".enpt";
	setAttr -k off -cb on ".clmt";
	setAttr -k off -cb on ".tcov";
	setAttr -k off -cb on ".lith";
	setAttr -k off -cb on ".sobc";
	setAttr -k off -cb on ".cuth";
	setAttr -k off -cb on ".hgcd";
	setAttr -k off -cb on ".hgci";
	setAttr -k off -cb on ".mgcs";
	setAttr -k off -cb on ".twa";
	setAttr -k off -cb on ".twz";
	setAttr -k on ".hwcc";
	setAttr -k on ".hwdp";
	setAttr -k on ".hwql";
	setAttr -k on ".hwfr";
	setAttr -k on ".soll";
	setAttr -k on ".sosl";
	setAttr -k on ".bswa";
	setAttr -k on ".shml";
	setAttr -k on ".hwel";
select -ne :hardwareRenderingGlobals;
	setAttr ".fprt" yes;
select -ne :defaultHardwareRenderGlobals;
	setAttr ".fn" -type "string" "im";
	setAttr ".res" -type "string" "ntsc_4d 646 485 1.333";
connectAttr "SDK1FKIndexToe1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbToe1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKIndexToe1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexToe2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyToe1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKThumbToe1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbToe2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexToe1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbToe1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKIndexToe1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexToe2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyToe1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKThumbToe1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbToe2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKMiddleFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKRingFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKThumbFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKMiddleFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKRingFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKThumbFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "RootFat.oy" "Root.fatYabs";
connectAttr "RootFat.oz" "Root.fatZabs";
connectAttr "Root.s" "Tail0.is";
connectAttr "Tail_0Fat.oy" "Tail0.fatYabs";
connectAttr "Tail_0Fat.oz" "Tail0.fatZabs";
connectAttr "Tail0.s" "Tail1.is";
connectAttr "Tail_1Fat.oy" "Tail1.fatYabs";
connectAttr "Tail_1Fat.oz" "Tail1.fatZabs";
connectAttr "Tail1.s" "Tail2.is";
connectAttr "Tail_2Fat.oy" "Tail2.fatYabs";
connectAttr "Tail_2Fat.oz" "Tail2.fatZabs";
connectAttr "Tail2.s" "Tail3.is";
connectAttr "Tail_3Fat.oy" "Tail3.fatYabs";
connectAttr "Tail_3Fat.oz" "Tail3.fatZabs";
connectAttr "Tail3.s" "Tail4.is";
connectAttr "Tail_4Fat.oy" "Tail4.fatYabs";
connectAttr "Tail_4Fat.oz" "Tail4.fatZabs";
connectAttr "Tail4.s" "Tail5.is";
connectAttr "Tail_5Fat.oy" "Tail5.fatYabs";
connectAttr "Tail_5Fat.oz" "Tail5.fatZabs";
connectAttr "Tail5.s" "Tail6.is";
connectAttr "Tail_6Fat.oy" "Tail6.fatYabs";
connectAttr "Tail_6Fat.oz" "Tail6.fatZabs";
connectAttr "Tail6.s" "Tail7.is";
connectAttr "Tail_7Fat.oy" "Tail7.fatYabs";
connectAttr "Tail_7Fat.oz" "Tail7.fatZabs";
connectAttr "Tail7.s" "Tail8.is";
connectAttr "Tail_8Fat.oy" "Tail8.fatYabs";
connectAttr "Tail_8Fat.oz" "Tail8.fatZabs";
connectAttr "Tail8.s" "Tail9.is";
connectAttr "Tail_9Fat.oy" "Tail9.fatYabs";
connectAttr "Tail_9Fat.oz" "Tail9.fatZabs";
connectAttr "Root.s" "Hip.is";
connectAttr "HipFat.oy" "Hip.fatYabs";
connectAttr "HipFat.oz" "Hip.fatZabs";
connectAttr "Hip.s" "Knee.is";
connectAttr "KneeFat.oy" "Knee.fatYabs";
connectAttr "KneeFat.oz" "Knee.fatZabs";
connectAttr "Knee.s" "Ankle.is";
connectAttr "AnkleFat.oy" "Ankle.fatYabs";
connectAttr "AnkleFat.oz" "Ankle.fatZabs";
connectAttr "Ankle.s" "Toes1.is";
connectAttr "Toes1Fat.oy" "Toes1.fatYabs";
connectAttr "Toes1Fat.oz" "Toes1.fatZabs";
connectAttr "Toes1.s" "Toes2.is";
connectAttr "Toes2Fat.oy" "Toes2.fatYabs";
connectAttr "Toes2Fat.oz" "Toes2.fatZabs";
connectAttr "Toes2.s" "ToesEnd.is";
connectAttr "ToesEndFat.oy" "ToesEnd.fatYabs";
connectAttr "ToesEndFat.oz" "ToesEnd.fatZabs";
connectAttr "Toes2.s" "ToeBig.is";
connectAttr "ToeBigFat.oy" "ToeBig.fatYabs";
connectAttr "ToeBigFat.oz" "ToeBig.fatZabs";
connectAttr "Toes2.s" "ToePinky.is";
connectAttr "ToePinkyFat.oy" "ToePinky.fatYabs";
connectAttr "ToePinkyFat.oz" "ToePinky.fatZabs";
connectAttr "Toes2.s" "PinkyToe1.is";
connectAttr "PinkyToe1Fat.oy" "PinkyToe1.fatYabs";
connectAttr "PinkyToe1Fat.oz" "PinkyToe1.fatZabs";
connectAttr "PinkyToe1.s" "PinkyToe2.is";
connectAttr "PinkyToe2Fat.oy" "PinkyToe2.fatYabs";
connectAttr "PinkyToe2Fat.oz" "PinkyToe2.fatZabs";
connectAttr "PinkyToe2.s" "PinkyToe3.is";
connectAttr "PinkyToe3Fat.oy" "PinkyToe3.fatYabs";
connectAttr "PinkyToe3Fat.oz" "PinkyToe3.fatZabs";
connectAttr "Toes2.s" "MiddleToe1.is";
connectAttr "MiddleToe1Fat.oy" "MiddleToe1.fatYabs";
connectAttr "MiddleToe1Fat.oz" "MiddleToe1.fatZabs";
connectAttr "MiddleToe1.s" "MiddleToe2.is";
connectAttr "MiddleToe2Fat.oy" "MiddleToe2.fatYabs";
connectAttr "MiddleToe2Fat.oz" "MiddleToe2.fatZabs";
connectAttr "MiddleToe2.s" "MiddleToe3.is";
connectAttr "MiddleToe3Fat.oy" "MiddleToe3.fatYabs";
connectAttr "MiddleToe3Fat.oz" "MiddleToe3.fatZabs";
connectAttr "Toes2.s" "IndexToe1.is";
connectAttr "IndexToe1Fat.oy" "IndexToe1.fatYabs";
connectAttr "IndexToe1Fat.oz" "IndexToe1.fatZabs";
connectAttr "IndexToe1.s" "IndexToe2.is";
connectAttr "IndexToe2Fat.oy" "IndexToe2.fatYabs";
connectAttr "IndexToe2Fat.oz" "IndexToe2.fatZabs";
connectAttr "IndexToe2.s" "IndexToe3.is";
connectAttr "IndexToe3Fat.oy" "IndexToe3.fatYabs";
connectAttr "IndexToe3Fat.oz" "IndexToe3.fatZabs";
connectAttr "Toes1.s" "ThumbToe1.is";
connectAttr "ThumbToe1Fat.oy" "ThumbToe1.fatYabs";
connectAttr "ThumbToe1Fat.oz" "ThumbToe1.fatZabs";
connectAttr "ThumbToe1.s" "ThumbToe2.is";
connectAttr "ThumbToe2Fat.oy" "ThumbToe2.fatYabs";
connectAttr "ThumbToe2Fat.oz" "ThumbToe2.fatZabs";
connectAttr "ThumbToe2.s" "ThumbToe3.is";
connectAttr "ThumbToe3Fat.oy" "ThumbToe3.fatYabs";
connectAttr "ThumbToe3Fat.oz" "ThumbToe3.fatZabs";
connectAttr "Root.s" "Spine1.is";
connectAttr "Spine1Fat.oy" "Spine1.fatYabs";
connectAttr "Spine1Fat.oz" "Spine1.fatZabs";
connectAttr "Spine1.s" "Spine2.is";
connectAttr "Spine2Fat.oy" "Spine2.fatYabs";
connectAttr "Spine2Fat.oz" "Spine2.fatZabs";
connectAttr "Spine2.s" "Chest.is";
connectAttr "ChestFat.oy" "Chest.fatYabs";
connectAttr "ChestFat.oz" "Chest.fatZabs";
connectAttr "Chest.s" "Neck1.is";
connectAttr "Neck1Fat.oy" "Neck1.fatYabs";
connectAttr "Neck1Fat.oz" "Neck1.fatZabs";
connectAttr "Neck1.s" "Neck2.is";
connectAttr "Neck2Fat.oy" "Neck2.fatYabs";
connectAttr "Neck2Fat.oz" "Neck2.fatZabs";
connectAttr "Neck2.s" "Neck3.is";
connectAttr "Neck3Fat.oy" "Neck3.fatYabs";
connectAttr "Neck3Fat.oz" "Neck3.fatZabs";
connectAttr "Neck3.s" "Neck4.is";
connectAttr "Neck4Fat.oy" "Neck4.fatYabs";
connectAttr "Neck4Fat.oz" "Neck4.fatZabs";
connectAttr "Neck4.s" "Head.is";
connectAttr "HeadFat.oy" "Head.fatYabs";
connectAttr "HeadFat.oz" "Head.fatZabs";
connectAttr "Head.s" "HeadEnd.is";
connectAttr "HeadEndFat.oy" "HeadEnd.fatYabs";
connectAttr "HeadEndFat.oz" "HeadEnd.fatZabs";
connectAttr "Head.s" "Jaw.is";
connectAttr "JawFat.oy" "Jaw.fatYabs";
connectAttr "JawFat.oz" "Jaw.fatZabs";
connectAttr "Jaw.s" "JawEnd.is";
connectAttr "JawEndFat.oy" "JawEnd.fatYabs";
connectAttr "JawEndFat.oz" "JawEnd.fatZabs";
connectAttr "Chest.s" "Scapula.is";
connectAttr "ScapulaFat.oy" "Scapula.fatYabs";
connectAttr "ScapulaFat.oz" "Scapula.fatZabs";
connectAttr "Scapula.s" "Shoulder.is";
connectAttr "ShoulderFat.oy" "Shoulder.fatYabs";
connectAttr "ShoulderFat.oz" "Shoulder.fatZabs";
connectAttr "Shoulder.s" "Elbow.is";
connectAttr "ElbowFat.oy" "Elbow.fatYabs";
connectAttr "ElbowFat.oz" "Elbow.fatZabs";
connectAttr "Elbow.s" "Wrist.is";
connectAttr "WristFat.oy" "Wrist.fatYabs";
connectAttr "WristFat.oz" "Wrist.fatZabs";
connectAttr "Wrist.s" "ThumbFinger1.is";
connectAttr "ThumbFinger1Fat.oy" "ThumbFinger1.fatYabs";
connectAttr "ThumbFinger1Fat.oz" "ThumbFinger1.fatZabs";
connectAttr "ThumbFinger1.s" "ThumbFinger2.is";
connectAttr "ThumbFinger2Fat.oy" "ThumbFinger2.fatYabs";
connectAttr "ThumbFinger2Fat.oz" "ThumbFinger2.fatZabs";
connectAttr "ThumbFinger2.s" "ThumbFinger3.is";
connectAttr "ThumbFinger3Fat.oy" "ThumbFinger3.fatYabs";
connectAttr "ThumbFinger3Fat.oz" "ThumbFinger3.fatZabs";
connectAttr "ThumbFinger3.s" "ThumbFinger4.is";
connectAttr "ThumbFinger4Fat.oy" "ThumbFinger4.fatYabs";
connectAttr "ThumbFinger4Fat.oz" "ThumbFinger4.fatZabs";
connectAttr "Wrist.s" "IndexFinger1.is";
connectAttr "IndexFinger1Fat.oy" "IndexFinger1.fatYabs";
connectAttr "IndexFinger1Fat.oz" "IndexFinger1.fatZabs";
connectAttr "IndexFinger1.s" "IndexFinger2.is";
connectAttr "IndexFinger2Fat.oy" "IndexFinger2.fatYabs";
connectAttr "IndexFinger2Fat.oz" "IndexFinger2.fatZabs";
connectAttr "IndexFinger2.s" "IndexFinger3.is";
connectAttr "IndexFinger3Fat.oy" "IndexFinger3.fatYabs";
connectAttr "IndexFinger3Fat.oz" "IndexFinger3.fatZabs";
connectAttr "IndexFinger3.s" "IndexFinger4.is";
connectAttr "IndexFinger4Fat.oy" "IndexFinger4.fatYabs";
connectAttr "IndexFinger4Fat.oz" "IndexFinger4.fatZabs";
connectAttr "Wrist.s" "MiddleFinger1.is";
connectAttr "MiddleFinger1Fat.oy" "MiddleFinger1.fatYabs";
connectAttr "MiddleFinger1Fat.oz" "MiddleFinger1.fatZabs";
connectAttr "MiddleFinger1.s" "MiddleFinger2.is";
connectAttr "MiddleFinger2Fat.oy" "MiddleFinger2.fatYabs";
connectAttr "MiddleFinger2Fat.oz" "MiddleFinger2.fatZabs";
connectAttr "MiddleFinger2.s" "MiddleFinger3.is";
connectAttr "MiddleFinger3Fat.oy" "MiddleFinger3.fatYabs";
connectAttr "MiddleFinger3Fat.oz" "MiddleFinger3.fatZabs";
connectAttr "MiddleFinger3.s" "MiddleFinger4.is";
connectAttr "MiddleFinger4Fat.oy" "MiddleFinger4.fatYabs";
connectAttr "MiddleFinger4Fat.oz" "MiddleFinger4.fatZabs";
connectAttr "Wrist.s" "RingFinger1.is";
connectAttr "RingFinger1Fat.oy" "RingFinger1.fatYabs";
connectAttr "RingFinger1Fat.oz" "RingFinger1.fatZabs";
connectAttr "RingFinger1.s" "RingFinger2.is";
connectAttr "RingFinger2Fat.oy" "RingFinger2.fatYabs";
connectAttr "RingFinger2Fat.oz" "RingFinger2.fatZabs";
connectAttr "RingFinger2.s" "RingFinger3.is";
connectAttr "RingFinger3Fat.oy" "RingFinger3.fatYabs";
connectAttr "RingFinger3Fat.oz" "RingFinger3.fatZabs";
connectAttr "RingFinger3.s" "RingFinger4.is";
connectAttr "RingFinger4Fat.oy" "RingFinger4.fatYabs";
connectAttr "RingFinger4Fat.oz" "RingFinger4.fatZabs";
connectAttr "Wrist.s" "PinkyFinger1.is";
connectAttr "PinkyFinger1Fat.oy" "PinkyFinger1.fatYabs";
connectAttr "PinkyFinger1Fat.oz" "PinkyFinger1.fatZabs";
connectAttr "PinkyFinger1.s" "PinkyFinger2.is";
connectAttr "PinkyFinger2Fat.oy" "PinkyFinger2.fatYabs";
connectAttr "PinkyFinger2Fat.oz" "PinkyFinger2.fatZabs";
connectAttr "PinkyFinger2.s" "PinkyFinger3.is";
connectAttr "PinkyFinger3Fat.oy" "PinkyFinger3.fatYabs";
connectAttr "PinkyFinger3Fat.oz" "PinkyFinger3.fatZabs";
connectAttr "PinkyFinger3.s" "PinkyFinger4.is";
connectAttr "PinkyFinger4Fat.oy" "PinkyFinger4.fatYabs";
connectAttr "PinkyFinger4Fat.oz" "PinkyFinger4.fatZabs";
relationship "link" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
connectAttr "layerManager.dli[0]" "defaultLayer.id";
connectAttr "Root.fat" "RootFat.i1y";
connectAttr "Root.fat" "RootFat.i1z";
connectAttr "Root.fatY" "RootFat.i2y";
connectAttr "Root.fatZ" "RootFat.i2z";
connectAttr "Spine1.fat" "Spine1Fat.i1y";
connectAttr "Spine1.fat" "Spine1Fat.i1z";
connectAttr "Spine1.fatY" "Spine1Fat.i2y";
connectAttr "Spine1.fatZ" "Spine1Fat.i2z";
connectAttr "Spine2.fat" "Spine2Fat.i1y";
connectAttr "Spine2.fat" "Spine2Fat.i1z";
connectAttr "Spine2.fatY" "Spine2Fat.i2y";
connectAttr "Spine2.fatZ" "Spine2Fat.i2z";
connectAttr "Chest.fat" "ChestFat.i1y";
connectAttr "Chest.fat" "ChestFat.i1z";
connectAttr "Chest.fatY" "ChestFat.i2y";
connectAttr "Chest.fatZ" "ChestFat.i2z";
connectAttr "Scapula.fat" "ScapulaFat.i1y";
connectAttr "Scapula.fat" "ScapulaFat.i1z";
connectAttr "Scapula.fatY" "ScapulaFat.i2y";
connectAttr "Scapula.fatZ" "ScapulaFat.i2z";
connectAttr "Shoulder.fat" "ShoulderFat.i1y";
connectAttr "Shoulder.fat" "ShoulderFat.i1z";
connectAttr "Shoulder.fatY" "ShoulderFat.i2y";
connectAttr "Shoulder.fatZ" "ShoulderFat.i2z";
connectAttr "Elbow.fat" "ElbowFat.i1y";
connectAttr "Elbow.fat" "ElbowFat.i1z";
connectAttr "Elbow.fatY" "ElbowFat.i2y";
connectAttr "Elbow.fatZ" "ElbowFat.i2z";
connectAttr "Wrist.fat" "WristFat.i1y";
connectAttr "Wrist.fat" "WristFat.i1z";
connectAttr "Wrist.fatY" "WristFat.i2y";
connectAttr "Wrist.fatZ" "WristFat.i2z";
connectAttr "PinkyFinger1.fat" "PinkyFinger1Fat.i1y";
connectAttr "PinkyFinger1.fat" "PinkyFinger1Fat.i1z";
connectAttr "PinkyFinger1.fatY" "PinkyFinger1Fat.i2y";
connectAttr "PinkyFinger1.fatZ" "PinkyFinger1Fat.i2z";
connectAttr "PinkyFinger2.fat" "PinkyFinger2Fat.i1y";
connectAttr "PinkyFinger2.fat" "PinkyFinger2Fat.i1z";
connectAttr "PinkyFinger2.fatY" "PinkyFinger2Fat.i2y";
connectAttr "PinkyFinger2.fatZ" "PinkyFinger2Fat.i2z";
connectAttr "PinkyFinger3.fat" "PinkyFinger3Fat.i1y";
connectAttr "PinkyFinger3.fat" "PinkyFinger3Fat.i1z";
connectAttr "PinkyFinger3.fatY" "PinkyFinger3Fat.i2y";
connectAttr "PinkyFinger3.fatZ" "PinkyFinger3Fat.i2z";
connectAttr "PinkyFinger4.fat" "PinkyFinger4Fat.i1y";
connectAttr "PinkyFinger4.fat" "PinkyFinger4Fat.i1z";
connectAttr "PinkyFinger4.fatY" "PinkyFinger4Fat.i2y";
connectAttr "PinkyFinger4.fatZ" "PinkyFinger4Fat.i2z";
connectAttr "RingFinger1.fat" "RingFinger1Fat.i1y";
connectAttr "RingFinger1.fat" "RingFinger1Fat.i1z";
connectAttr "RingFinger1.fatY" "RingFinger1Fat.i2y";
connectAttr "RingFinger1.fatZ" "RingFinger1Fat.i2z";
connectAttr "RingFinger2.fat" "RingFinger2Fat.i1y";
connectAttr "RingFinger2.fat" "RingFinger2Fat.i1z";
connectAttr "RingFinger2.fatY" "RingFinger2Fat.i2y";
connectAttr "RingFinger2.fatZ" "RingFinger2Fat.i2z";
connectAttr "RingFinger3.fat" "RingFinger3Fat.i1y";
connectAttr "RingFinger3.fat" "RingFinger3Fat.i1z";
connectAttr "RingFinger3.fatY" "RingFinger3Fat.i2y";
connectAttr "RingFinger3.fatZ" "RingFinger3Fat.i2z";
connectAttr "RingFinger4.fat" "RingFinger4Fat.i1y";
connectAttr "RingFinger4.fat" "RingFinger4Fat.i1z";
connectAttr "RingFinger4.fatY" "RingFinger4Fat.i2y";
connectAttr "RingFinger4.fatZ" "RingFinger4Fat.i2z";
connectAttr "MiddleFinger1.fat" "MiddleFinger1Fat.i1y";
connectAttr "MiddleFinger1.fat" "MiddleFinger1Fat.i1z";
connectAttr "MiddleFinger1.fatY" "MiddleFinger1Fat.i2y";
connectAttr "MiddleFinger1.fatZ" "MiddleFinger1Fat.i2z";
connectAttr "MiddleFinger2.fat" "MiddleFinger2Fat.i1y";
connectAttr "MiddleFinger2.fat" "MiddleFinger2Fat.i1z";
connectAttr "MiddleFinger2.fatY" "MiddleFinger2Fat.i2y";
connectAttr "MiddleFinger2.fatZ" "MiddleFinger2Fat.i2z";
connectAttr "MiddleFinger3.fat" "MiddleFinger3Fat.i1y";
connectAttr "MiddleFinger3.fat" "MiddleFinger3Fat.i1z";
connectAttr "MiddleFinger3.fatY" "MiddleFinger3Fat.i2y";
connectAttr "MiddleFinger3.fatZ" "MiddleFinger3Fat.i2z";
connectAttr "MiddleFinger4.fat" "MiddleFinger4Fat.i1y";
connectAttr "MiddleFinger4.fat" "MiddleFinger4Fat.i1z";
connectAttr "MiddleFinger4.fatY" "MiddleFinger4Fat.i2y";
connectAttr "MiddleFinger4.fatZ" "MiddleFinger4Fat.i2z";
connectAttr "IndexFinger1.fat" "IndexFinger1Fat.i1y";
connectAttr "IndexFinger1.fat" "IndexFinger1Fat.i1z";
connectAttr "IndexFinger1.fatY" "IndexFinger1Fat.i2y";
connectAttr "IndexFinger1.fatZ" "IndexFinger1Fat.i2z";
connectAttr "IndexFinger2.fat" "IndexFinger2Fat.i1y";
connectAttr "IndexFinger2.fat" "IndexFinger2Fat.i1z";
connectAttr "IndexFinger2.fatY" "IndexFinger2Fat.i2y";
connectAttr "IndexFinger2.fatZ" "IndexFinger2Fat.i2z";
connectAttr "IndexFinger3.fat" "IndexFinger3Fat.i1y";
connectAttr "IndexFinger3.fat" "IndexFinger3Fat.i1z";
connectAttr "IndexFinger3.fatY" "IndexFinger3Fat.i2y";
connectAttr "IndexFinger3.fatZ" "IndexFinger3Fat.i2z";
connectAttr "IndexFinger4.fat" "IndexFinger4Fat.i1y";
connectAttr "IndexFinger4.fat" "IndexFinger4Fat.i1z";
connectAttr "IndexFinger4.fatY" "IndexFinger4Fat.i2y";
connectAttr "IndexFinger4.fatZ" "IndexFinger4Fat.i2z";
connectAttr "ThumbFinger1.fat" "ThumbFinger1Fat.i1y";
connectAttr "ThumbFinger1.fat" "ThumbFinger1Fat.i1z";
connectAttr "ThumbFinger1.fatY" "ThumbFinger1Fat.i2y";
connectAttr "ThumbFinger1.fatZ" "ThumbFinger1Fat.i2z";
connectAttr "ThumbFinger2.fat" "ThumbFinger2Fat.i1y";
connectAttr "ThumbFinger2.fat" "ThumbFinger2Fat.i1z";
connectAttr "ThumbFinger2.fatY" "ThumbFinger2Fat.i2y";
connectAttr "ThumbFinger2.fatZ" "ThumbFinger2Fat.i2z";
connectAttr "ThumbFinger3.fat" "ThumbFinger3Fat.i1y";
connectAttr "ThumbFinger3.fat" "ThumbFinger3Fat.i1z";
connectAttr "ThumbFinger3.fatY" "ThumbFinger3Fat.i2y";
connectAttr "ThumbFinger3.fatZ" "ThumbFinger3Fat.i2z";
connectAttr "ThumbFinger4.fat" "ThumbFinger4Fat.i1y";
connectAttr "ThumbFinger4.fat" "ThumbFinger4Fat.i1z";
connectAttr "ThumbFinger4.fatY" "ThumbFinger4Fat.i2y";
connectAttr "ThumbFinger4.fatZ" "ThumbFinger4Fat.i2z";
connectAttr "Neck1.fat" "Neck1Fat.i1y";
connectAttr "Neck1.fat" "Neck1Fat.i1z";
connectAttr "Neck1.fatY" "Neck1Fat.i2y";
connectAttr "Neck1.fatZ" "Neck1Fat.i2z";
connectAttr "Neck2.fat" "Neck2Fat.i1y";
connectAttr "Neck2.fat" "Neck2Fat.i1z";
connectAttr "Neck2.fatY" "Neck2Fat.i2y";
connectAttr "Neck2.fatZ" "Neck2Fat.i2z";
connectAttr "Neck3.fat" "Neck3Fat.i1y";
connectAttr "Neck3.fat" "Neck3Fat.i1z";
connectAttr "Neck3.fatY" "Neck3Fat.i2y";
connectAttr "Neck3.fatZ" "Neck3Fat.i2z";
connectAttr "Neck4.fat" "Neck4Fat.i1y";
connectAttr "Neck4.fat" "Neck4Fat.i1z";
connectAttr "Neck4.fatY" "Neck4Fat.i2y";
connectAttr "Neck4.fatZ" "Neck4Fat.i2z";
connectAttr "Head.fat" "HeadFat.i1y";
connectAttr "Head.fat" "HeadFat.i1z";
connectAttr "Head.fatY" "HeadFat.i2y";
connectAttr "Head.fatZ" "HeadFat.i2z";
connectAttr "Jaw.fat" "JawFat.i1y";
connectAttr "Jaw.fat" "JawFat.i1z";
connectAttr "Jaw.fatY" "JawFat.i2y";
connectAttr "Jaw.fatZ" "JawFat.i2z";
connectAttr "JawEnd.fat" "JawEndFat.i1y";
connectAttr "JawEnd.fat" "JawEndFat.i1z";
connectAttr "JawEnd.fatY" "JawEndFat.i2y";
connectAttr "JawEnd.fatZ" "JawEndFat.i2z";
connectAttr "HeadEnd.fat" "HeadEndFat.i1y";
connectAttr "HeadEnd.fat" "HeadEndFat.i1z";
connectAttr "HeadEnd.fatY" "HeadEndFat.i2y";
connectAttr "HeadEnd.fatZ" "HeadEndFat.i2z";
connectAttr "Hip.fat" "HipFat.i1y";
connectAttr "Hip.fat" "HipFat.i1z";
connectAttr "Hip.fatY" "HipFat.i2y";
connectAttr "Hip.fatZ" "HipFat.i2z";
connectAttr "Knee.fat" "KneeFat.i1y";
connectAttr "Knee.fat" "KneeFat.i1z";
connectAttr "Knee.fatY" "KneeFat.i2y";
connectAttr "Knee.fatZ" "KneeFat.i2z";
connectAttr "Ankle.fat" "AnkleFat.i1y";
connectAttr "Ankle.fat" "AnkleFat.i1z";
connectAttr "Ankle.fatY" "AnkleFat.i2y";
connectAttr "Ankle.fatZ" "AnkleFat.i2z";
connectAttr "Toes1.fat" "Toes1Fat.i1y";
connectAttr "Toes1.fat" "Toes1Fat.i1z";
connectAttr "Toes1.fatY" "Toes1Fat.i2y";
connectAttr "Toes1.fatZ" "Toes1Fat.i2z";
connectAttr "ThumbToe1.fat" "ThumbToe1Fat.i1y";
connectAttr "ThumbToe1.fat" "ThumbToe1Fat.i1z";
connectAttr "ThumbToe1.fatY" "ThumbToe1Fat.i2y";
connectAttr "ThumbToe1.fatZ" "ThumbToe1Fat.i2z";
connectAttr "ThumbToe2.fat" "ThumbToe2Fat.i1y";
connectAttr "ThumbToe2.fat" "ThumbToe2Fat.i1z";
connectAttr "ThumbToe2.fatY" "ThumbToe2Fat.i2y";
connectAttr "ThumbToe2.fatZ" "ThumbToe2Fat.i2z";
connectAttr "ThumbToe3.fat" "ThumbToe3Fat.i1y";
connectAttr "ThumbToe3.fat" "ThumbToe3Fat.i1z";
connectAttr "ThumbToe3.fatY" "ThumbToe3Fat.i2y";
connectAttr "ThumbToe3.fatZ" "ThumbToe3Fat.i2z";
connectAttr "Toes2.fat" "Toes2Fat.i1y";
connectAttr "Toes2.fat" "Toes2Fat.i1z";
connectAttr "Toes2.fatY" "Toes2Fat.i2y";
connectAttr "Toes2.fatZ" "Toes2Fat.i2z";
connectAttr "IndexToe1.fat" "IndexToe1Fat.i1y";
connectAttr "IndexToe1.fat" "IndexToe1Fat.i1z";
connectAttr "IndexToe1.fatY" "IndexToe1Fat.i2y";
connectAttr "IndexToe1.fatZ" "IndexToe1Fat.i2z";
connectAttr "IndexToe2.fat" "IndexToe2Fat.i1y";
connectAttr "IndexToe2.fat" "IndexToe2Fat.i1z";
connectAttr "IndexToe2.fatY" "IndexToe2Fat.i2y";
connectAttr "IndexToe2.fatZ" "IndexToe2Fat.i2z";
connectAttr "IndexToe3.fat" "IndexToe3Fat.i1y";
connectAttr "IndexToe3.fat" "IndexToe3Fat.i1z";
connectAttr "IndexToe3.fatY" "IndexToe3Fat.i2y";
connectAttr "IndexToe3.fatZ" "IndexToe3Fat.i2z";
connectAttr "MiddleToe1.fat" "MiddleToe1Fat.i1y";
connectAttr "MiddleToe1.fat" "MiddleToe1Fat.i1z";
connectAttr "MiddleToe1.fatY" "MiddleToe1Fat.i2y";
connectAttr "MiddleToe1.fatZ" "MiddleToe1Fat.i2z";
connectAttr "MiddleToe2.fat" "MiddleToe2Fat.i1y";
connectAttr "MiddleToe2.fat" "MiddleToe2Fat.i1z";
connectAttr "MiddleToe2.fatY" "MiddleToe2Fat.i2y";
connectAttr "MiddleToe2.fatZ" "MiddleToe2Fat.i2z";
connectAttr "MiddleToe3.fat" "MiddleToe3Fat.i1y";
connectAttr "MiddleToe3.fat" "MiddleToe3Fat.i1z";
connectAttr "MiddleToe3.fatY" "MiddleToe3Fat.i2y";
connectAttr "MiddleToe3.fatZ" "MiddleToe3Fat.i2z";
connectAttr "PinkyToe1.fat" "PinkyToe1Fat.i1y";
connectAttr "PinkyToe1.fat" "PinkyToe1Fat.i1z";
connectAttr "PinkyToe1.fatY" "PinkyToe1Fat.i2y";
connectAttr "PinkyToe1.fatZ" "PinkyToe1Fat.i2z";
connectAttr "PinkyToe2.fat" "PinkyToe2Fat.i1y";
connectAttr "PinkyToe2.fat" "PinkyToe2Fat.i1z";
connectAttr "PinkyToe2.fatY" "PinkyToe2Fat.i2y";
connectAttr "PinkyToe2.fatZ" "PinkyToe2Fat.i2z";
connectAttr "PinkyToe3.fat" "PinkyToe3Fat.i1y";
connectAttr "PinkyToe3.fat" "PinkyToe3Fat.i1z";
connectAttr "PinkyToe3.fatY" "PinkyToe3Fat.i2y";
connectAttr "PinkyToe3.fatZ" "PinkyToe3Fat.i2z";
connectAttr "ToePinky.fat" "ToePinkyFat.i1y";
connectAttr "ToePinky.fat" "ToePinkyFat.i1z";
connectAttr "ToePinky.fatY" "ToePinkyFat.i2y";
connectAttr "ToePinky.fatZ" "ToePinkyFat.i2z";
connectAttr "ToeBig.fat" "ToeBigFat.i1y";
connectAttr "ToeBig.fat" "ToeBigFat.i1z";
connectAttr "ToeBig.fatY" "ToeBigFat.i2y";
connectAttr "ToeBig.fatZ" "ToeBigFat.i2z";
connectAttr "ToesEnd.fat" "ToesEndFat.i1y";
connectAttr "ToesEnd.fat" "ToesEndFat.i1z";
connectAttr "ToesEnd.fatY" "ToesEndFat.i2y";
connectAttr "ToesEnd.fatZ" "ToesEndFat.i2z";
connectAttr "Tail0.fat" "Tail_0Fat.i1y";
connectAttr "Tail0.fat" "Tail_0Fat.i1z";
connectAttr "Tail0.fatY" "Tail_0Fat.i2y";
connectAttr "Tail0.fatZ" "Tail_0Fat.i2z";
connectAttr "Tail1.fat" "Tail_1Fat.i1y";
connectAttr "Tail1.fat" "Tail_1Fat.i1z";
connectAttr "Tail1.fatY" "Tail_1Fat.i2y";
connectAttr "Tail1.fatZ" "Tail_1Fat.i2z";
connectAttr "Tail2.fat" "Tail_2Fat.i1y";
connectAttr "Tail2.fat" "Tail_2Fat.i1z";
connectAttr "Tail2.fatY" "Tail_2Fat.i2y";
connectAttr "Tail2.fatZ" "Tail_2Fat.i2z";
connectAttr "Tail3.fat" "Tail_3Fat.i1y";
connectAttr "Tail3.fat" "Tail_3Fat.i1z";
connectAttr "Tail3.fatY" "Tail_3Fat.i2y";
connectAttr "Tail3.fatZ" "Tail_3Fat.i2z";
connectAttr "Tail4.fat" "Tail_4Fat.i1y";
connectAttr "Tail4.fat" "Tail_4Fat.i1z";
connectAttr "Tail4.fatY" "Tail_4Fat.i2y";
connectAttr "Tail4.fatZ" "Tail_4Fat.i2z";
connectAttr "Tail5.fat" "Tail_5Fat.i1y";
connectAttr "Tail5.fat" "Tail_5Fat.i1z";
connectAttr "Tail5.fatY" "Tail_5Fat.i2y";
connectAttr "Tail5.fatZ" "Tail_5Fat.i2z";
connectAttr "Tail6.fat" "Tail_6Fat.i1y";
connectAttr "Tail6.fat" "Tail_6Fat.i1z";
connectAttr "Tail6.fatY" "Tail_6Fat.i2y";
connectAttr "Tail6.fatZ" "Tail_6Fat.i2z";
connectAttr "Tail7.fat" "Tail_7Fat.i1y";
connectAttr "Tail7.fat" "Tail_7Fat.i1z";
connectAttr "Tail7.fatY" "Tail_7Fat.i2y";
connectAttr "Tail7.fatZ" "Tail_7Fat.i2z";
connectAttr "Tail8.fat" "Tail_8Fat.i1y";
connectAttr "Tail8.fat" "Tail_8Fat.i1z";
connectAttr "Tail8.fatY" "Tail_8Fat.i2y";
connectAttr "Tail8.fatZ" "Tail_8Fat.i2z";
connectAttr "Tail9.fat" "Tail_9Fat.i1y";
connectAttr "Tail9.fat" "Tail_9Fat.i1z";
connectAttr "Tail9.fatY" "Tail_9Fat.i2y";
connectAttr "Tail9.fatZ" "Tail_9Fat.i2z";
connectAttr "FitSkeleton.drivingSystem_toesCurl_R[1]" "SDK1FKPinkyToe1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_R[0]" "SDK1FKIndexToe1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_R[2]" "SDK1FKThumbToe1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_L[1]" "SDK1FKPinkyToe1_L_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_L[0]" "SDK1FKIndexToe1_L_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_L[2]" "SDK1FKThumbToe1_L_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_R[4]" "SDK1FKIndexToe2_R_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_R[3]" "SDK2FKIndexToe1_R_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_L[4]" "SDK1FKIndexToe2_L_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_L[3]" "SDK2FKIndexToe1_L_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_R[6]" "SDK1FKMiddleToe2_R_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_R[5]" "SDK1FKMiddleToe1_R_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_L[6]" "SDK1FKMiddleToe2_L_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_L[5]" "SDK1FKMiddleToe1_L_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_R[8]" "SDK1FKPinkyToe2_R_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_R[7]" "SDK2FKPinkyToe1_R_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_L[8]" "SDK1FKPinkyToe2_L_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_L[7]" "SDK2FKPinkyToe1_L_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_R[10]" "SDK1FKThumbToe2_R_rotateZ.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_R[9]" "SDK2FKThumbToe1_R_rotateZ.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_L[10]" "SDK1FKThumbToe2_L_rotateZ.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_toesCurl_L[9]" "SDK2FKThumbToe1_L_rotateZ.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[1]" "SDK1FKThumbFinger1_R_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[0]" "SDK1FKMiddleFinger1_R_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[2]" "SDK1FKRingFinger1_R_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[3]" "SDK1FKPinkyFinger1_R_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[1]" "SDK1FKThumbFinger1_L_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[0]" "SDK1FKMiddleFinger1_L_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[2]" "SDK1FKRingFinger1_L_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[3]" "SDK1FKPinkyFinger1_L_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[5]" "SDK1FKIndexFinger3_R_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[4]" "SDK1FKIndexFinger2_R_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[6]" "SDK1FKIndexFinger1_R_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[5]" "SDK1FKIndexFinger3_L_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[4]" "SDK1FKIndexFinger2_L_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[6]" "SDK1FKIndexFinger1_L_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[8]" "SDK1FKMiddleFinger3_R_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[7]" "SDK1FKMiddleFinger2_R_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[9]" "SDK2FKMiddleFinger1_R_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[8]" "SDK1FKMiddleFinger3_L_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[7]" "SDK1FKMiddleFinger2_L_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[9]" "SDK2FKMiddleFinger1_L_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[11]" "SDK1FKRingFinger3_R_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[10]" "SDK1FKRingFinger2_R_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[12]" "SDK2FKRingFinger1_R_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[11]" "SDK1FKRingFinger3_L_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[10]" "SDK1FKRingFinger2_L_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[12]" "SDK2FKRingFinger1_L_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[14]" "SDK1FKPinkyFinger3_R_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[13]" "SDK1FKPinkyFinger2_R_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[15]" "SDK2FKPinkyFinger1_R_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[14]" "SDK1FKPinkyFinger3_L_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[13]" "SDK1FKPinkyFinger2_L_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[15]" "SDK2FKPinkyFinger1_L_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[17]" "SDK1FKThumbFinger3_R_rotateY.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[16]" "SDK1FKThumbFinger2_R_rotateY.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_R[18]" "SDK2FKThumbFinger1_R_rotateY.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[17]" "SDK1FKThumbFinger3_L_rotateY.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[16]" "SDK1FKThumbFinger2_L_rotateY.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_fingers_L[18]" "SDK2FKThumbFinger1_L_rotateY.thumbCurl"
		;
connectAttr "defaultRenderLayer.msg" ":defaultRenderingList1.r" -na;
// End of dragon.ma
