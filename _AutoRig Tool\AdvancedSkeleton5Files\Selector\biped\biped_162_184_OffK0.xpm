/* XPM */
static char *biped_162_184_OffK0[] = {
/* columns rows colors chars-per-pixel */
"18 31 132 2",
"   c #8B8B8C",
".  c #8D8D8D",
"X  c #909090",
"o  c gray58",
"O  c #959696",
"+  c #989898",
"@  c #989998",
"#  c #989999",
"$  c #999898",
"%  c #999899",
"&  c gray60",
"*  c #9A9A9A",
"=  c #9E9D9E",
"-  c #A3A3A2",
";  c gray64",
":  c #A4A3A4",
">  c #A4A4A4",
",  c #A7A7A8",
"<  c #ACACAD",
"1  c #B4B3B4",
"2  c #B6B6B6",
"3  c #BCBCBD",
"4  c gray75",
"5  c gray77",
"6  c gray79",
"7  c #CBCBCB",
"8  c #D2D3D3",
"9  c #D8D8D9",
"0  c #DAD9DA",
"q  c #DDDDDE",
"w  c #DEDEDF",
"e  c #DFDEDF",
"r  c gray88",
"t  c #E0E0E1",
"y  c #E1E0E1",
"u  c #E1E1E1",
"i  c #E1E2E1",
"p  c #E1E3E2",
"a  c #E2E1E1",
"s  c #E2E2E2",
"d  c #E2E4E2",
"f  c #E5E5E6",
"g  c #E4E6E5",
"h  c #E5E6E6",
"j  c #E6E5E6",
"k  c #E6E6E6",
"l  c #E7E7E7",
"z  c #E9E8E8",
"x  c #E9E9E8",
"c  c #EAEAEA",
"v  c gray92",
"b  c #EBEDEB",
"n  c #EBECEC",
"m  c #ECEBEB",
"M  c #ECECEC",
"N  c #ECEDEC",
"B  c #ECEDED",
"V  c #EDECED",
"C  c #ECEDEE",
"Z  c #ECEEEC",
"A  c #EDEEEF",
"S  c #EEEDED",
"D  c #EEEFED",
"F  c #EEEFEF",
"G  c #EFEEEE",
"H  c #EFEEEF",
"J  c #EEEEF0",
"K  c #EEEFF0",
"L  c #EFF0F0",
"P  c #F0EFEE",
"I  c #F0F1F0",
"U  c #F1F1F1",
"Y  c #F2F2F0",
"T  c gray95",
"R  c #F3F3F3",
"E  c #F2F4F3",
"W  c #F5F7F7",
"Q  c #F8F8F9",
"!  c #FAF9F9",
"~  c #FAFAFB",
"^  c #FBFAFA",
"/  c #FCFAFA",
"(  c #FCFCFD",
")  c #FCFDFD",
"_  c #FDFCFC",
"`  c #FDFCFD",
"'  c #FDFDFC",
"]  c #FDFDFD",
"[  c #FCFCFE",
"{  c #FCFDFE",
"}  c #FCFDFF",
"|  c #FDFCFE",
" . c #FDFDFE",
".. c #FDFDFF",
"X. c #FCFEFC",
"o. c #FCFEFD",
"O. c #FCFFFC",
"+. c #FCFFFD",
"@. c #FDFEFC",
"#. c #FDFEFD",
"$. c #FDFFFC",
"%. c #FDFFFD",
"&. c #FCFEFE",
"*. c #FCFFFE",
"=. c #FDFEFE",
"-. c #FDFEFF",
";. c #FDFFFE",
":. c #FDFFFF",
">. c #FEFDFC",
",. c #FEFDFD",
"<. c #FFFCFD",
"1. c #FFFDFD",
"2. c #FEFCFE",
"3. c #FEFCFF",
"4. c #FEFDFE",
"5. c #FEFDFF",
"6. c #FFFDFE",
"7. c #FFFDFF",
"8. c #FEFEFC",
"9. c #FEFEFD",
"0. c #FEFFFD",
"q. c #FFFEFD",
"w. c #FFFFFC",
"e. c #FFFFFD",
"r. c #FEFEFE",
"t. c #FEFEFF",
"y. c #FEFFFE",
"u. c #FEFFFF",
"i. c #FFFEFE",
"p. c #FFFEFF",
"a. c #FFFFFE",
"s. c gray100",
/* pixels */
"4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 ",
"4 Z 1.1.1.1.( ( 1.( 1.1.1.1.( ( p 4 ",
"4 b 1.1.1.1.( 1.1.( 1.1.( 1.1.~ w 4 ",
"4 b 1.( ( 1.1.( ( ( ( 1.1.1.1.Q 0 4 ",
"4 b 1.( ( 1.1.( ( 1.1.( 1.1.( W 8 4 ",
"4 b ( ( ( 1.1.( ( 1.( ( 1.( 1.E 6 4 ",
"4 b 1.( ( 1.1.1.1.1.( 1.1.1.1.L 5 4 ",
"4 J 1.( 1.( 1.1.1.1.( 1.( ( 1.L 4 4 ",
"4 L 1.1.( 1.1.1.( ( ( 1.( ( 1.b 1 4 ",
"4 L ( 1.1.1.1.1.1.1.1.1.( 1.1.x < 4 ",
"4 L ( ( ( ( 1.( 1.( 1.1.( ( 1.x , 4 ",
"4 L 1.( ( 1.( ( 1.( ( 1.1.1.( g ; 4 ",
"4 L 1.1.( ( ( 1.1.1.1.( ( 1.1.h - 4 ",
"4 L 1.1.( 1.1.1.1.( 1.( ( 1.( x - 4 ",
"4 E ( ( ( ( ( 1.1.( 1.1.( 1.( g - 4 ",
"4 L 1.1.( ( ( 1.1.( 1.1.1.1.( g = 4 ",
"4 L ( ( ( ( ( 1.( 1.( ( 1.1.1.p # 4 ",
"4 K ( ( ( 1.1.( ( 1.1.( 1.1.1.t # 4 ",
"4 L ( ( ( 1.( ( 1.( 1.1.( ( ( p # 4 ",
"4 J ( 1.1.( 1.1.1.1.( ( ( ( ( p # 4 ",
"4 F 1.1.1.( 1.1.1.1.1.( ( 1.1.p # 4 ",
"4 F 1.( ( ( 1.1.1.( ( 1.1.( ( p # 4 ",
"4 Z 1.1.1.1.1.1.1.( 1.( 1.( ( t # 4 ",
"4 Z 1.1.( ( 1.1.1.( 1.( 1.( ( t # 4 ",
"4 Z 1.1.( ( 1.1.( 1.1.1.1.( ( t # 4 ",
"4 Z 1.1.( 1.( ( 1.( 1.1.1.1.( t O 4 ",
"4 n 1.( ( 1.( ( ( 1.( 1.1.( ( 9 . 4 ",
"4 n 1.1.1.( 1.( 1.( ( 1.1.1.L 6 . 4 ",
"4 g ( ( 1.( 1.1.( 1.( 1.1.( x 2 . 4 ",
"4 t ~ 1.1.( ( ( ( ( 1.1.( ( w > O 4 ",
"4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 "
};
