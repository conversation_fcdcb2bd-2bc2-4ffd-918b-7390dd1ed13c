/* XPM */
static char *biped_195_168_OffK1[] = {
/* columns rows colors chars-per-pixel */
"21 22 36 1",
"  c #FF4444",
". c #FF4545",
"X c #FF4A4A",
"o c #FF4C4C",
"O c #FF4D4D",
"+ c #FF4E4E",
"@ c #FF4F4F",
"# c #FF5151",
"$ c #FF5454",
"% c #FF5656",
"& c #EC7F7F",
"* c #ED7E7E",
"= c #EE7D7D",
"- c #FF6363",
"; c #FF6767",
": c #FF6868",
"> c #FF6C6C",
", c #FF6D6D",
"< c #FF6E6E",
"1 c #F17B7B",
"2 c #F37979",
"3 c #F47979",
"4 c #F67878",
"5 c #F77878",
"6 c #FA7575",
"7 c #FF7070",
"8 c #FE7272",
"9 c #FF7272",
"0 c gray75",
"q c #E38484",
"w c #E68383",
"e c #E78282",
"r c #E48484",
"t c #E88181",
"y c #E98181",
"u c #EA8080",
/* pixels */
"000000000000000000000",
"0qqqqqqqre,,yqqqqqqq0",
"0qqqqqqqr*++2qqqqqqq0",
"0qqqqqqqr*  4qqqqqqq0",
"0qqqqqqqr*  4qqqqqqq0",
"0qqqqqqqr*  4qqqqqqq0",
"0qqqqqqqr*  4qqqqqqq0",
"0qqqqqqqr*  4qqqqqqq0",
"0qqeqqqer*  4rqqrrqr0",
"0e*******4  8*******0",
"0,%######+  +######%0",
"0;+                X0",
"02888,888,  :88,88,80",
"0eeyyyyyy1  6yyyyyyy0",
"0qqerrrre*  4rrrrrqq0",
"0qqqqqqqq*  4qqrqqqq0",
"0qqqqqqqr*  4qqqqqqq0",
"0qqqqqqqr*  4qqqqqqq0",
"0qqqqqqqr*  4qqqqqqq0",
"0qqqqqqqq*XX4qqqqqqq0",
"0qqqqqqqqy--*qqqqqqq0",
"000000000000000000000"
};
