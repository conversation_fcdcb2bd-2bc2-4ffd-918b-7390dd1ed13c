/* XPM */
static char *biped_252_131_OffK1[] = {
/* columns rows colors chars-per-pixel */
"38 46 59 1",
"  c #CF7C7C",
". c #D07C7C",
"X c #D17D7D",
"o c #D27D7D",
"O c #D47E7E",
"+ c #D57E7E",
"@ c #D67E7E",
"# c #D67F7E",
"$ c #D67F7F",
"% c #D87F7F",
"& c gray75",
"* c #D98080",
"= c #DF8283",
"- c #DF8382",
"; c #E08383",
": c #E18383",
"> c #E28383",
", c #E28384",
"< c #E28484",
"1 c #E38484",
"2 c #E78685",
"3 c #EC8887",
"4 c #ED8888",
"5 c #EE8888",
"6 c #EF8989",
"7 c #FC8E8E",
"8 c #FD8E8E",
"9 c #FF9292",
"0 c #FF9393",
"q c #FF9494",
"w c #FF9696",
"e c #FF9898",
"r c #FF9998",
"t c #FF9A99",
"y c #FF9B9B",
"u c #FF9C9B",
"i c #FF9C9C",
"p c #FF9D9D",
"a c #FF9E9E",
"s c #FF9F9E",
"d c #FF9F9F",
"f c #FFA2A2",
"g c #FFA2A3",
"h c #FFA3A3",
"j c #FFA5A5",
"k c #FFA5A6",
"l c #FFA6A5",
"z c #FFA6A6",
"x c #FFA7A6",
"c c #FFA7A7",
"v c #FFA7A8",
"b c #FFA8A7",
"n c #FFA8A8",
"m c #FFA8A9",
"M c #FFA9A8",
"N c #FFA9A9",
"B c #FFA9AA",
"V c #FFAAA9",
"C c #FFAAAA",
/* pixels */
"&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCv&",
"&zCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCv&",
"&cCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&zCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&zCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCz&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCz&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCz&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCf&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCvs&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCvq&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCz7&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCj5&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCz:&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCfO&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCs &",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCzq &",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCzy<*&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCs6O>&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCzqo=>&",
"&CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCz0o:>&",
"&yzCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCqo2<&",
"&20szCCCCCCCCCCCCCCCCCCCCCCCCCCCCsO<2&",
"&*O6rzcCCCCCCCCCCCCCCCCCCCCCCCCCCs%>>&",
"&11O-qzCCCCCCCCCCCCCCCCCCCCCCCCCvso2<&",
"&111*1yCCCCCCCCCCCCCCCCCCCCCCCCCvy <2&",
"&111<oqzCCCCCCCCCCCCCCCCCCCCCCCCzy >>&",
"&112<O7fCCCCCCCCCCCCCCCCCCCCCCCCCqo<>&",
"&1111*3sCCCCCCCCCCCCCCCCCCCCCCCCzy ><&",
"&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&"
};
