/* XPM */
static char *AS_version[] = {
/* columns rows colors chars-per-pixel */
"32 32 52 1",
"  c black",
". c gray21",
"X c #373737",
"o c #3A3A3A",
"O c #5A5A5A",
"+ c #5F5F5F",
"@ c gray42",
"# c #747474",
"$ c gray46",
"% c #777777",
"& c gray47",
"* c #868686",
"= c #9F9F9F",
"- c #A0A0A0",
"; c #AAAAAA",
": c #AEAEAE",
"> c gray",
", c gray76",
"< c #C3C3C3",
"1 c gray79",
"2 c #CBCBCB",
"3 c #CECECE",
"4 c #D0D0D0",
"5 c #D5D5D5",
"6 c #D8D8D8",
"7 c #DADADA",
"8 c gray86",
"9 c gainsboro",
"0 c #DFDFDF",
"q c gray88",
"w c #E1E1E1",
"e c #E2E2E2",
"r c #E6E6E6",
"t c #E7E7E7",
"y c #E9E9E9",
"u c #EAEAEA",
"i c gray92",
"p c gray93",
"a c #EFEFEF",
"s c #F1F1F1",
"d c #F3F3F3",
"f c gray96",
"g c #F6F6F6",
"h c gray97",
"j c #F8F8F8",
"k c #F9F9F9",
"l c #FBFBFB",
"z c gray99",
"x c #FDFDFD",
"c c #FEFEFE",
"v c gray100",
"b c None",
/* pixels */
"bbbbbbbbbbbbbbbbbbbbvvvvvbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbvvvvlbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbv;   bbbbbbb",
"bbbbbbvvvlbbbbvvgbbbv=bbbbbbbbbb",
"bbbbbbvvvvgbbvvvv7bpvlvdbbbbbbbb",
"bbbbbbbb 5vb7v> ddbevvvvpbbbbbbb",
"bbbbbbbgvvl@<vvi<bbrv*.iv@bbbbbb",
"bbbbbbvvvvv#bpvvvrbbbbb4v-bbbbbb",
"bbbbblv- <v#bb:<glbgvbbiv=bbbbbb",
"bbbbbllobdv&5v5b9l@ev1blv+bbbbbb",
"bbbbbpvvvvv#bvvvvi bvvvv9 bbbbbb",
"bbbbbbpvdev#b1lveObb3gve.bbbbbbb",
"bbbbbbbb  bbbbbb bbbbbb bbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb"
};
