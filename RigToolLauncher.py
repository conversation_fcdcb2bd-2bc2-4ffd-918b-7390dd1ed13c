"""
Launcher script for the Comprehensive Rig Tool
This script provides easy access to the rigging system
"""

import maya.cmds as mc
import sys
import os

def launch_rig_tool():
    """Launch the comprehensive rig tool"""
    try:
        # Import the main rigging module
        import ComprehensiveRigTool
        
        # Reload the module to get latest changes
        if 'ComprehensiveRigTool' in sys.modules:
            reload(ComprehensiveRigTool)
        
        # Show the UI
        ComprehensiveRigTool.show_comprehensive_rig_ui()
        
    except ImportError as e:
        mc.confirmDialog(
            title="Import Error",
            message=f"Could not import ComprehensiveRigTool: {str(e)}\n\nMake sure the script is in your Maya script path.",
            button="OK"
        )
    except Exception as e:
        mc.confirmDialog(
            title="Error",
            message=f"Error launching rig tool: {str(e)}",
            button="OK"
        )

def create_test_skeleton():
    """Create a simple test skeleton for testing the rig tool"""
    try:
        # Clear selection
        mc.select(clear=True)
        
        # Create a simple biped skeleton for testing
        # Spine
        spine_joints = []
        spine_joints.append(mc.joint(p=(0, 0, 0), n="spine_01_jnt"))
        spine_joints.append(mc.joint(p=(0, 5, 0), n="spine_02_jnt"))
        spine_joints.append(mc.joint(p=(0, 10, 0), n="spine_03_jnt"))
        spine_joints.append(mc.joint(p=(0, 15, 0), n="chest_jnt"))
        
        # Left arm
        mc.select(spine_joints[-1])
        left_clavicle = mc.joint(p=(-2, 15, 0), n="left_clavicle_jnt")
        left_shoulder = mc.joint(p=(-5, 15, 0), n="left_shoulder_jnt")
        left_elbow = mc.joint(p=(-10, 15, 0), n="left_elbow_jnt")
        left_wrist = mc.joint(p=(-15, 15, 0), n="left_wrist_jnt")
        
        # Right arm
        mc.select(spine_joints[-1])
        right_clavicle = mc.joint(p=(2, 15, 0), n="right_clavicle_jnt")
        right_shoulder = mc.joint(p=(5, 15, 0), n="right_shoulder_jnt")
        right_elbow = mc.joint(p=(10, 15, 0), n="right_elbow_jnt")
        right_wrist = mc.joint(p=(15, 15, 0), n="right_wrist_jnt")
        
        # Hips
        mc.select(spine_joints[0])
        hips = mc.joint(p=(0, -2, 0), n="hips_jnt")
        
        # Left leg
        mc.select(hips)
        left_hip = mc.joint(p=(-2, -2, 0), n="left_hip_jnt")
        left_knee = mc.joint(p=(-2, -10, 0), n="left_knee_jnt")
        left_ankle = mc.joint(p=(-2, -18, 0), n="left_ankle_jnt")
        
        # Right leg
        mc.select(hips)
        right_hip = mc.joint(p=(2, -2, 0), n="right_hip_jnt")
        right_knee = mc.joint(p=(2, -10, 0), n="right_knee_jnt")
        right_ankle = mc.joint(p=(2, -18, 0), n="right_ankle_jnt")
        
        # Clear selection
        mc.select(clear=True)
        
        # Print instructions
        print("Test skeleton created!")
        print("\nTo test the rig tool:")
        print("1. Select joints for each limb (shoulder->elbow->wrist for arms)")
        print("2. Use the 'Set' buttons in the rig tool UI")
        print("3. Click 'BUILD COMPLETE RIG'")
        print("\nExample selections:")
        print("- Left Arm: left_shoulder_jnt, left_elbow_jnt, left_wrist_jnt")
        print("- Right Arm: right_shoulder_jnt, right_elbow_jnt, right_wrist_jnt")
        print("- Left Leg: left_hip_jnt, left_knee_jnt, left_ankle_jnt")
        print("- Right Leg: right_hip_jnt, right_knee_jnt, right_ankle_jnt")
        
        mc.confirmDialog(
            title="Test Skeleton Created",
            message="Test skeleton created successfully!\n\nCheck the script editor for selection instructions.",
            button="OK"
        )
        
    except Exception as e:
        mc.confirmDialog(
            title="Error",
            message=f"Error creating test skeleton: {str(e)}",
            button="OK"
        )

def show_help():
    """Show help information"""
    help_text = """
COMPREHENSIVE RIG TOOL - HELP

This tool creates a complete rigging system similar to 4SchoolV1 with:
- FK/IK switching for arms and legs
- Spine controls
- Hip, chest, and clavicle controls

WORKFLOW:
1. Create or load your skeleton
2. Launch the rig tool
3. Select joints for each body part
4. Click 'Set' to store selections
5. Validate selections
6. Build the complete rig

JOINT SELECTION TIPS:
- Select joints in order from root to tip
- Arms/Legs need at least 2 joints for FK/IK
- Use consistent naming (e.g., left_shoulder_jnt)

CONTROLS CREATED:
- FK controllers (circles) for all joints
- IK controllers (arrows) for limb ends
- Pole vector controls (cubes) for limbs
- Switch controls (squares) for FK/IK blending

SWITCHING FK/IK:
- Use the ikfkBlend attribute on switch controls
- 0 = Full FK, 1 = Full IK
- Animate between values for smooth transitions
"""
    
    mc.confirmDialog(
        title="Comprehensive Rig Tool - Help",
        message=help_text,
        button="OK"
    )

# Create a simple UI for launching tools
def create_launcher_ui():
    """Create a simple launcher UI"""
    window_id = "RigToolLauncher"
    
    if mc.window(window_id, q=True, exists=True):
        mc.deleteUI(window_id)
    
    window = mc.window(window_id, title="Rig Tool Launcher", w=300, h=200)
    
    mc.columnLayout(adj=True, rs=10)
    
    mc.text(label="Comprehensive Rig Tool Launcher", font="boldLabelFont")
    mc.separator(h=10)
    
    mc.button(label="Launch Rig Tool", command=lambda x: launch_rig_tool(), 
              h=40, bgc=[0.2, 0.7, 0.2])
    
    mc.separator(h=5)
    
    mc.button(label="Create Test Skeleton", command=lambda x: create_test_skeleton(), 
              h=30, bgc=[0.5, 0.5, 0.7])
    
    mc.button(label="Show Help", command=lambda x: show_help(), 
              h=30, bgc=[0.7, 0.7, 0.3])
    
    mc.showWindow(window)

# Execute if run directly
if __name__ == "__main__":
    create_launcher_ui()
