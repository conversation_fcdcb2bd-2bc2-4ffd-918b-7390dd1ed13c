import maya.cmds as mc

follicles= mc.ls(sl=True)

lineCount = len(follicles)
segmentCount = lineCount - 1
stride = 1/segmentCount 

for i in range(0, lineCount):
    follicle = follicles[i]
    follicleName = "follicle_upper_lip_" + str(i).zfill(2)
    mc.rename(follicle, follicleName)
    follicleShape = mc.listRelatives(follicleName, s=True)[0]
    mc.setAttr(follicleShape+".parameterU", i * stride)
    
    mc.select(cl=True)
    jntName = follicleName.replace("follicle", "jnt")
    mc.joint(n=jntName)
    mc.parentConstraint(follicleName, jntName, mo=False)