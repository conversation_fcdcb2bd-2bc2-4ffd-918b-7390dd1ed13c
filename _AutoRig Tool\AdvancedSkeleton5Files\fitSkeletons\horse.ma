//Maya ASCII 2012 scene
//Name: horse.ma
//Last modified: Mon, Aug 06, 2018 06:55:44 PM
//Codeset: 1252
requires maya "2008";
fileInfo "application" "maya";
fileInfo "product" "Maya 2012";
fileInfo "version" "2012 x64";
fileInfo "cutIdentifier" "201201172029-821146";
fileInfo "osv" "Microsoft Business Edition, 64-bit  (Build 9200)\n";
createNode transform -s -n "persp";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -37.121217851259154 11.516722716927557 10.156928921851135 ;
	setAttr ".r" -type "double3" -5.7383527318065894 -86.999999999988063 -2.2789456640622298e-014 ;
	setAttr ".rp" -type "double3" -4.4408920985006262e-016 6.9388939039072284e-018 0 ;
	setAttr ".rpt" -type "double3" 6.8522917298714157e-016 7.582925976939003e-016 3.6273500378082328e-016 ;
createNode camera -s -n "perspShape" -p "persp";
	setAttr -k off ".v" no;
	setAttr ".fl" 34.999999999999979;
	setAttr ".ncp" 1;
	setAttr ".coi" 39.931863036118834;
	setAttr ".imn" -type "string" "persp";
	setAttr ".den" -type "string" "persp_depth";
	setAttr ".man" -type "string" "persp_mask";
	setAttr ".tp" -type "double3" -1.4320743664597586 0.38266674347289609 3.03107256176588 ;
	setAttr ".hc" -type "string" "viewSet -p %camera";
createNode transform -s -n "top";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 0 101.72777466931498 5.0591237182467452 ;
	setAttr ".r" -type "double3" -89.999999999999986 0 0 ;
	setAttr ".rp" -type "double3" 0 0 -1.4210854715202004e-014 ;
	setAttr ".rpt" -type "double3" 0 -1.4210854715202007e-014 1.4210854715202013e-014 ;
createNode camera -s -n "topShape" -p "top";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 92.956918141992944;
	setAttr ".ow" 64.431756082637719;
	setAttr ".imn" -type "string" "top";
	setAttr ".den" -type "string" "top_depth";
	setAttr ".man" -type "string" "top_mask";
	setAttr ".tp" -type "double3" -0.92121148222907756 8.6997414858987554 4.667703911888192 ;
	setAttr ".hc" -type "string" "viewSet -t %camera";
	setAttr ".o" yes;
createNode transform -s -n "front";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 0 8.6729601320683809 100.59786988434907 ;
createNode camera -s -n "frontShape" -p "front";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 104.02558206488972;
	setAttr ".ow" 68.333041412906667;
	setAttr ".imn" -type "string" "front";
	setAttr ".den" -type "string" "front_depth";
	setAttr ".man" -type "string" "front_mask";
	setAttr ".tp" -type "double3" -0.70157198929103071 -0.02211088597723998 -3.8272793103230782 ;
	setAttr ".hc" -type "string" "viewSet -f %camera";
	setAttr ".o" yes;
createNode transform -s -n "side";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 104.01064278233328 5.549285495890298 -3.7700078571006577 ;
	setAttr ".r" -type "double3" 0 89.999999999999972 0 ;
	setAttr ".rpt" -type "double3" -3.1554436208840472e-030 0 9.4663308626521417e-030 ;
createNode camera -s -n "sideShape" -p "side";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 105.0955703032582;
	setAttr ".ow" 24.435287984719743;
	setAttr ".imn" -type "string" "side";
	setAttr ".den" -type "string" "side_depth";
	setAttr ".man" -type "string" "side_mask";
	setAttr ".tp" -type "double3" -1.0849275209249214 3.2647990795037511 3.1367915905578698 ;
	setAttr ".hc" -type "string" "viewSet -s %camera";
	setAttr ".o" yes;
createNode transform -n "FitSkeleton";
	addAttr -ci true -sn "visCylinders" -ln "visCylinders" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "visBoxes" -ln "visBoxes" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "visBones" -ln "visBones" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "lockCenterJoints" -ln "lockCenterJoints" -dv 1 -min 0 -max 
		1 -at "bool";
	addAttr -ci true -sn "visGap" -ln "visGap" -dv 0.75 -min 0 -max 1 -at "double";
	addAttr -ci true -k true -sn "visGeo" -ln "visGeo" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visGeoType" -ln "visGeoType" -min 0 -max 3 -en "cylinders:boxes:spheres:bones" 
		-at "enum";
	addAttr -ci true -sn "visSpheres" -ln "visSpheres" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visPoleVector" -ln "visPoleVector" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visJointOrient" -ln "visJointOrient" -min 0 -max 1 
		-at "bool";
	addAttr -ci true -k true -sn "visJointAxis" -ln "visJointAxis" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "preRebuildScript" -ln "preRebuildScript" -dt "string";
	addAttr -ci true -sn "postRebuildScript" -ln "postRebuildScript" -dt "string";
	addAttr -ci true -sn "run" -ln "run" -dt "string";
	addAttr -ci true -m -im false -sn "drivingSystem" -ln "drivingSystem" -at "message";
	addAttr -ci true -m -sn "drivingSystem_ToeCurl_R" -ln "drivingSystem_ToeCurl_R" 
		-dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -m -sn "drivingSystem_ToeCurl_L" -ln "drivingSystem_ToeCurl_L" 
		-dv 1 -min 0 -max 1 -at "bool";
	setAttr -l on ".v";
	setAttr ".ove" yes;
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr ".visCylinders" yes;
	setAttr ".visGap" 1;
	setAttr ".run" -type "string" ";setAttr FKIKSpine_M.FKIKBlend 10;;setAttr FKIKSplineTail_M.FKIKBlend 10;setAttr FKIKSplineNeck_M.FKIKBlend 10;;setAttr FKIKSplineTail_M.FKIKBlend 0;";
createNode nurbsCurve -n "FitSkeletonShape" -p "FitSkeleton";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 29;
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		7.5862419835940322 4.6452334813828352e-016 -7.5862419835940136
		-1.224001133012428e-015 6.5693521897612033e-016 -10.728566300642843
		-7.5862419835940207 4.6452334813828372e-016 -7.5862419835940207
		-10.728566300642843 2.3983482861558806e-031 -4.0000682941509181e-015
		-7.5862419835940234 -4.6452334813828362e-016 7.586241983594018
		-3.2327264063532947e-015 -6.5693521897612052e-016 10.728566300642846
		7.5862419835940136 -4.6452334813828372e-016 7.5862419835940207
		10.728566300642843 -3.0336975024041557e-031 4.8711357008981667e-015
		7.5862419835940322 4.6452334813828352e-016 -7.5862419835940136
		-1.224001133012428e-015 6.5693521897612033e-016 -10.728566300642843
		-7.5862419835940207 4.6452334813828372e-016 -7.5862419835940207
		;
createNode joint -n "Root" -p "FitSkeleton";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.4995579999729969 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "centerBtwFeet" -ln "centerBtwFeet" -dv 1 -min 0 -max 
		1 -at "bool";
	addAttr -ci true -k true -sn "numMainExtras" -ln "numMainExtras" -min 0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 2.4133462603331756e-035 11.915606008834843 -2.8973267439447641 ;
	setAttr -l on ".tx";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".ro" 3;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -89.999999999991559 -87.243231448963996 -90.00000000000847 ;
	setAttr ".pa" -type "double3" 2.1569712459551385e-014 1.9864576294588575e-014 0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 1;
	setAttr -k on ".fat" 1.2753655952808685;
	setAttr ".fatYabs" 1.2753655910491943;
	setAttr ".fatZabs" 1.2753655910491943;

createNode joint -n "Spine1" -p "Root";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.2750682628243526 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 2.9044202498862486 5.3290705182007514e-015 4.049910102181424e-015 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" 8.4093982802619652e-017 -7.8618824678547458e-015 -2.1647650300711066e-013 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.5659497177708448e-013 -3.2860812018051546e-016 -1.2256724957934662 ;
	setAttr ".pa" -type "double3" 5.6267942770155683e-016 1.1140408275537263e-014 0 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Mid";
	setAttr -k on ".fat" 1.6489911912185808;
	setAttr ".fatYabs" 1.6489912271499634;
	setAttr ".fatZabs" 1.6489912271499634;

createNode joint -n "Hip" -p "Root";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.8275483175467393 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -1.8204744325977922 1.5621163102189453 -1.0712552534322328 ;
	setAttr ".r" -type "double3" 2.9018559659864991e-013 -1.1208815624885627e-012 -1.8383606058859157e-012 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -179.99999999999963 -6.7797600738050436e-029 61.014529026244773 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "HipBack";
	setAttr -k on ".fat" 1.0076254049975768;
	setAttr -k on ".fatZ" 0.7;
	setAttr ".fatYabs" 1.0076254606246948;
	setAttr ".fatZabs" 0.70533782243728638;
createNode joint -n "Knee" -p "Hip";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.3229548725558775 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 2.8034969353134027 -8.659739592076221e-014 5.4845017416482733e-014 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.9304790576350233e-014 -5.4847517140835419e-015 -47.950252172913181 ;
	setAttr ".pa" -type "double3" 0 7.3840344800430189e-005 0 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "HipBack";
	setAttr -k on ".fat" 1.0130997171573839;
	setAttr ".fatYabs" 1.0130996704101562;
	setAttr ".fatZabs" 1.0130996704101562;
createNode joint -n "Ankle" -p "Knee";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4338138454722005 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 4.459436476326994 -1.0835776720341528e-013 -9.4368957093138306e-014 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.6753643078118751e-016 -6.0912177576291953e-015 
		30.412364045317556 ;
	setAttr ".pa" -type "double3" 0 0.00039045212256848152 0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "FootBack";
	setAttr -k on ".fat" 0.4565498585786919;
	setAttr ".fatYabs" 0.45654985308647156;
	setAttr ".fatZabs" 0.45654985308647156;
createNode joint -n "Toes1" -p "Ankle";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4338138454722005 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 2.3694654117802831 5.3290705182007514e-015 -1.1339240213459334e-008 ;
	setAttr ".ro" 3;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.1433449548785909e-014 8.3584221010037642e-015 20.215520095520741 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "ToesBack";
	setAttr -k on ".fat" 0.26571601954479351;
	setAttr ".fatYabs" 0.26571601629257202;
	setAttr ".fatZabs" 0.26571601629257202;
createNode joint -n "Toes2" -p "Toes1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4338138454722005 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.91216519833711729 8.5265128291212022e-014 6.5702590035243702e-009 ;
	setAttr ".ro" 3;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.6753461009102797e-014 1.331017981848994e-006 14.64450604575195 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "ToesBack";
	setAttr -k on ".fat" 0.18571601954479355;
	setAttr ".fatYabs" 0.18571601808071136;
	setAttr ".fatZabs" 0.18571601808071136;
createNode joint -n "Toes3" -p "Toes2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4338138454722005 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.51170976469645391 -1.1812772982011666e-013 5.595524044110789e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 8.3223903983550845e-015 1.312593551099621e-006 14.93321617956977 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "QToesBack";
	setAttr -k on ".fat" 0.3120241707384987;
	setAttr ".fatYabs" 0.31202417612075806;
	setAttr ".fatZabs" 0.31202417612075806;
createNode joint -n "Toes4" -p "Toes3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4338138454722005 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.5241711300253189 -1.3322676295501878e-014 7.1054273576010019e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 8.3223911566584117e-015 -8.8461254829322528e-015 6.3611093629270335e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "ToesEndBack";
	setAttr -k on ".fat" 0.12202417073849868;
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "backFootSideOuter" -p "Toes3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.12202417073849868 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.14186618231232595 -0.23136005224825462 0.38046568260791735 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -4.3998279483473198e-015 -1.0535180931764141e-014 
		0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "PinkyToe";
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "backFootSideInner" -p "Toes3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.12202417073849868 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.14186620852425191 -0.23136006172481416 -0.36026790864370972 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -4.3998279483473198e-015 -1.0535180931764141e-014 
		0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "BigToe";
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "backHeel" -p "Toes3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.5765498585786919 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.022326682767691786 -0.30674516233590943 1.9505195236746431e-008 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -4.3998279483473198e-015 -1.0535180931764141e-014 
		0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Heel";
	setAttr ".fatYabs" 0.57654988765716553;
	setAttr ".fatZabs" 0.57654988765716553;
createNode joint -n "Tail0" -p "Root";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "flipOrient" -ln "flipOrient" -dv 1 -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" -1.9789093139171294 0.17687585334833678 -1.7533361365771902e-016 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" -5.3212598850360622e-012 1.1439526501517328e-013 -3.8631812299726298e-012 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -179.99999999999866 4.5198400492033624e-029 176.71065499317697 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "0Tail";
	setAttr -k on ".fat" 0.61012085369249336;
	setAttr ".fatYabs" 0.61012083292007446;
	setAttr ".fatZabs" 0.61012083292007446;
createNode joint -n "Tail1" -p "Tail0";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.8 -9.0594198809412774e-014 -2.6677260007707317e-015 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 6.942002611249894e-013 0 1.4908850069360232e-015 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "30";
	setAttr -k on ".fat" 0.61012085369249336;
	setAttr ".fatYabs" 0.61012083292007446;
	setAttr ".fatZabs" 0.61012083292007446;
createNode joint -n "Tail2" -p "Tail1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.8 -1.2434497875801753e-013 -5.937319693353081e-016 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 6.942002611249894e-013 0 1.4908850069360232e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "1";
	setAttr -k on ".fat" 0.61012085369249336;
	setAttr ".fatYabs" 0.61012083292007446;
	setAttr ".fatZabs" 0.61012083292007446;
createNode joint -n "Tail3" -p "Tail2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.8 1.7763568394002505e-013 -3.2781624433392505e-016 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 6.942002611249894e-013 0 1.4908850069360232e-015 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "29";
	setAttr -k on ".fat" 0.61012085369249336;
	setAttr ".fatYabs" 0.61012083292007446;
	setAttr ".fatZabs" 0.61012083292007446;
createNode joint -n "Tail4" -p "Tail3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.8 -4.6185277824406512e-014 -4.1914728210213309e-017 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 6.942002611249894e-013 0 1.4908850069360232e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "2";
	setAttr -k on ".fat" 0.61012085369249336;
	setAttr ".fatYabs" 0.61012083292007446;
	setAttr ".fatZabs" 0.61012083292007446;
createNode joint -n "Tail5" -p "Tail4";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.8 1.6342482922482304e-013 1.2342948931725332e-017 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" -2.8162251550177665e-017 -2.4654011869899186e-014 -8.3438870298181487e-012 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0 1.0288222909613333e-014 0 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "28";
	setAttr -k on ".fat" 0.61012085369249336;
	setAttr ".fatYabs" 0.61012083292007446;
	setAttr ".fatZabs" 0.61012083292007446;
createNode joint -n "Tail6" -p "Tail5";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.8 -3.907985046680551e-014 1.6932456342054755e-016 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "3";
	setAttr -k on ".fat" 0.61012085369249336;
	setAttr ".fatYabs" 0.61012083292007446;
	setAttr ".fatZabs" 0.61012083292007446;

createNode joint -n "Chest" -p "Spine1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.1396060751118258 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "freeOrient" -ln "freeOrient" -dv 1 -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 3.3872490771422541 -5.3290705182007514e-015 4.6478386994907166e-016 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.7010923639453472e-013 -3.9466586198167212e-017 4.5720473546038052e-015 ;
	setAttr ".pa" -type "double3" 8.9511097085308541e-017 1.5252296155034558e-015 0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Chest";
	setAttr -k on ".fat" 2.154827519059884;
	setAttr ".fatYabs" 2.1548275947570801;
	setAttr ".fatZabs" 2.1548275947570801;
createNode joint -n "Neck" -p "Chest";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 4.4519209697165998 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 2.0484789643624062 0.8789618720571557 -4.2649382742505038e-017 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -5.8042780822911093e-015 -9.3336510223478097e-015 
		-49.543952872591028 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "0Neck";
	setAttr -k on ".fat" 1.1616945230750493;
	setAttr ".fatYabs" 1.1616945266723633;
	setAttr ".fatZabs" 1.1616945266723633;
createNode joint -n "Neck1" -p "Neck";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.7716945230750494 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 1.1272805685144593 -1.2789769243681803e-013 -2.9763993996988903e-016 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" -2.8752897411831981e-015 1.2678020803062504e-014 6.3706510269714241e-012 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.7520405801516316e-014 2.0303962707326986e-014 -25.556283015515067 ;
	setAttr -k on ".fat" 0.62937978919853788;
	setAttr ".fatYabs" 0.62937980890274048;
	setAttr ".fatZabs" 0.62937980890274048;
createNode joint -n "Neck2" -p "Neck1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.5393797891985381 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 1.3451241830051561 1.496580637194711e-013 -2.9763993997060532e-016 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" 5.8578335667932405e-016 1.2664842844428522e-014 -8.6678066456584469e-012 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.3799174008640559e-015 -1.7246358850933655e-015 5.2963958996224347 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "1";
	setAttr -k on ".fat" 0.59706505532202669;
	setAttr ".fatYabs" 0.5970650315284729;
	setAttr ".fatZabs" 0.5970650315284729;
createNode joint -n "Neck3" -p "Neck2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.3070650553220269 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 1.3465238048584691 -2.0317081350640365e-013 -2.9763993997011622e-016 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" 2.8340089257335648e-015 1.4778240361278853e-014 -3.3936518451215723e-012 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.1759700682122948e-014 3.3091803733055255e-014 21.711524171784454 ;
	setAttr -k on ".fat" 0.56475032144551562;
	setAttr ".fatYabs" 0.5647503137588501;
	setAttr ".fatZabs" 0.5647503137588501;
createNode joint -n "Neck4" -p "Neck3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.0747503214455159 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 1.1539609559680102 -6.5725203057809267e-014 -2.9763993997034973e-016 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" 2.5419648903297303e-015 4.2395413952145924e-014 1.7290290387106037e-011 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.2953698642482539e-015 1.9108626303797051e-014 6.8625207754046622 ;
	setAttr -k on ".fat" 0.5;
	setAttr ".fatYabs" 0.5;
	setAttr ".fatZabs" 0.5;
createNode joint -n "Head" -p "Neck4";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.1940176038839925 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.84404117143753332 2.5579538487363607e-013 -6.2453945403735835e-016 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.2708844290335833e-016 -7.0778340241243898e-015 
		-27.871911679828063 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "2";
	setAttr ".radi" 0.5;
	setAttr -k on ".fat" 0.69000000000000017;
	setAttr ".fatYabs" 0.68999999761581421;
	setAttr ".fatZabs" 0.68999999761581421;

createNode joint -n "HeadEnd" -p "Head";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.1940176038839925 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 1.4569900796112343 -7.6827433304060833e-014 5.1753061076250214e-016 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.2708844290331312e-016 -7.0778340241243898e-015 
		6.3611093629270351e-015 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "18";
	setAttr ".radi" 0.5;
	setAttr -k on ".fat" 0.61012085369249336;
	setAttr ".fatYabs" 0.61012083292007446;
	setAttr ".fatZabs" 0.61012083292007446;

createNode joint -n "Jaw" -p "Head";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.5 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -0.24089891886712422 0.52400830481344718 -3.5130953915393472e-016 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" 2.1389944425611722e-016 1.391184153239001e-016 -3.3204990874479116e-012 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -6.9320855729565815e-015 -1.5610683822529761e-016 
		113.92071952910479 ;
	setAttr ".otp" -type "string" "16";
	setAttr ".radi" 0.54466925561379975;
	setAttr -k on ".fat" 0.61012085369249336;
	setAttr ".fatYabs" 0.61012083292007446;
	setAttr ".fatZabs" 0.61012083292007446;
createNode joint -n "JawEnd" -p "Jaw";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.5 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 3.5014273099578812 -2.0250467969162855e-013 -8.50172600622889e-018 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 8.2679231706232923e-015 8.6252638478711083e-015 -3.1805546814635168e-015 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "6";
	setAttr ".radi" 0.54466925561379975;
	setAttr -k on ".fat" 0.61012085369249336;
	setAttr ".fatYabs" 0.61012083292007446;
	setAttr ".fatZabs" 0.61012083292007446;
createNode joint -n "Eye" -p "Head";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.13852234887235859 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "noFlip" -ln "noFlip" -dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.045550605677764366 1.3620906846319438 -0.46304908429546332 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -5.0139721889036865e-005 34.638847582582443 67.570522454106708 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Eye";
	setAttr ".radi" 0.5;
	setAttr -k on ".fat" 0.16903074749898564;
	setAttr ".fatYabs" 0.16903074085712433;
	setAttr ".fatZabs" 0.16903074085712433;
createNode joint -n "EyeEnd" -p "Eye";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.13852234887235859 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.16903074749906999 -1.3677947663381929e-013 6.2172489379008766e-014 ;
	setAttr ".ro" 1;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.1541188591555056e-014 1.272221872585407e-014 4.3990935315742402e-015 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "16";
	setAttr ".radi" 0.5;
	setAttr -k on ".fat" 0.16903074749898564;
	setAttr ".fatYabs" 0.16903074085712433;
	setAttr ".fatZabs" 0.16903074085712433;

createNode joint -n "Scapula" -p "Chest";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.5609724984406954 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.21267461962659606 -0.20640370066739777 -1.0712552534321855 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 180 7.7341534202240892e-015 53.301948963184188 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "LegAimFront";
	setAttr ".radi" 0.51205178940387641;
	setAttr -k on ".fat" 2.0866133196283276;
	setAttr -k on ".fatZ" 0.82;
	setAttr ".fatYabs" 2.0866134166717529;
	setAttr ".fatZabs" 1.7110229730606079;
createNode joint -n "Shoulder" -p "Scapula";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.6799301464676359 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "flipOrient" -ln "flipOrient" -dv 1 -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 3.4530103015612283 -1.5987211554602254e-014 1.6875389974302379e-014 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.6807091988115542e-014 -1.1164692398989243e-014 -73.061221192668015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "HipFront";
	setAttr ".radi" 0.5;
	setAttr -k on ".fat" 1.1651238878958825;
	setAttr ".fatYabs" 1.1651239395141602;
	setAttr ".fatZabs" 1.1651239395141602;
createNode joint -n "Elbow" -p "Shoulder";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4590705413009739 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 2.6067071895016021 4.4408920985006262e-015 9.6367358537463588e-014 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.8596759241284386e-014 3.9957868027761017e-015 44.527481032642562 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "4";
	setAttr ".radi" 0.51398329861619807;
	setAttr -k on ".fat" 0.74518128053874;
	setAttr ".fatYabs" 0.74518126249313354;
	setAttr ".fatZabs" 0.74518126249313354;
createNode joint -n "Wrist" -p "Elbow";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4590705413009739 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 3.0879071168677634 0 -6.8611782921834674e-014 ;
	setAttr ".ro" 3;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.2084337772288106e-014 1.0620222408048462e-015 -13.99429249312572 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "FootFront";
	setAttr ".radi" 0.53241073775331582;
	setAttr -k on ".fat" 0.53012085369249329;
	setAttr ".fatYabs" 0.530120849609375;
	setAttr ".fatZabs" 0.530120849609375;
createNode joint -n "Fingers1" -p "Wrist";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4590705413009739 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 2.4234994292939507 -1.1990408665951691e-014 -7.7715611723760958e-014 ;
	setAttr ".ro" 3;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 8.2783080520161333e-017 -8.7790423015104718e-015 24.353300014332383 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "";
	setAttr ".radi" 0.53241073775331582;
	setAttr -k on ".fat" 0.2301208536924933;
	setAttr ".fatYabs" 0.23012085258960724;
	setAttr ".fatZabs" 0.23012085258960724;
createNode joint -n "Fingers2" -p "Fingers1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4590705413009739 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.88390185315844105 2.8421709430404007e-014 3.7747582837255322e-015 ;
	setAttr ".ro" 3;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.2715967672646496e-014 1.2532760204987562e-014 16.739009391664492 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "";
	setAttr ".radi" 0.53241073775331582;
	setAttr -k on ".fat" 0.2301208536924933;
	setAttr ".fatYabs" 0.23012085258960724;
	setAttr ".fatZabs" 0.23012085258960724;
createNode joint -n "Fingers3" -p "Fingers2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4590705413009739 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.46963099912008666 -1.4832579608992091e-013 7.3274719625260332e-015 ;
	setAttr ".r" -type "double3" 1.6710569033652583e-013 7.48328955108339e-013 5.0252763967123675e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -5.3743306975438332e-015 4.1548104081916106e-015 25.175840105142132 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "QToesFront";
	setAttr ".radi" 0.5;
	setAttr -k on ".fat" 0.35202417073849873;
	setAttr ".fatYabs" 0.35202416777610779;
	setAttr ".fatZabs" 0.35202416777610779;
createNode joint -n "Fingers4" -p "Fingers3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4590705413009739 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.5780284642552882 4.8849813083506888e-015 -7.7715611723760958e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.4915994741934542e-014 -1.06279266827649e-014 3.1805546814635176e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "ToesEndFront";
	setAttr ".radi" 0.5;
	setAttr -k on ".fat" 0.12202417073849868;
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "frontFootSideInner" -p "Fingers3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.12202417073849868 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.19115907549669764 -0.23587782963565163 -0.35506227062657125 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -8.5548853790075512e-015 -1.1646368802054768e-014 
		3.1805546814635168e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "BigToe";
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "frontFootSideOuter" -p "Fingers3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.12202417073849868 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.19115907549670164 -0.23587782963564852 0.38240680561196738 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -8.5548853790075512e-015 -1.1646368802054768e-014 
		3.1805546814635168e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "PinkyToe";
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "frontHeel" -p "Fingers3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.5765498585786919 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.066331824198025302 -0.30621621281599376 -3.1086244689504383e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -8.5548853790075512e-015 -1.1646368802054768e-014 
		3.1805546814635168e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Heel";
	setAttr ".fatYabs" 0.57654988765716553;
	setAttr ".fatZabs" 0.57654988765716553;
createNode lightLinker -s -n "lightLinker1";
	setAttr -s 2 ".lnk";
	setAttr -s 2 ".slnk";
createNode displayLayerManager -n "layerManager";
	setAttr ".cdl" 1;
	setAttr -s 14 ".dli[1:13]"  1 2 3 4 5 6 7 8 
		9 10 11 12 13;
createNode displayLayer -n "defaultLayer";
createNode renderLayer -n "defaultRenderLayer";
	setAttr ".g" yes;

connectAttr "Root.s" "Hip.is";
connectAttr "Hip.s" "Knee.is";
connectAttr "Knee.s" "Ankle.is";
connectAttr "Ankle.s" "Toes1.is";
connectAttr "Toes1.s" "Toes2.is";
connectAttr "Toes2.s" "Toes3.is";
connectAttr "Toes3.s" "Toes4.is";
connectAttr "Toes3.s" "backFootSideOuter.is";
connectAttr "Toes3.s" "backFootSideInner.is";
connectAttr "Toes3.s" "backHeel.is";
connectAttr "Root.s" "Tail0.is";
connectAttr "Tail0.s" "Tail1.is";
connectAttr "Tail1.s" "Tail2.is";
connectAttr "Tail2.s" "Tail3.is";
connectAttr "Tail3.s" "Tail4.is";
connectAttr "Tail4.s" "Tail5.is";
connectAttr "Tail5.s" "Tail6.is";
connectAttr "Root.s" "Spine1.is";
connectAttr "Spine1.s" "Chest.is";
connectAttr "Chest.s" "Neck.is";
connectAttr "Neck.s" "Neck1.is";
connectAttr "Neck1.s" "Neck2.is";
connectAttr "Neck2.s" "Neck3.is";
connectAttr "Neck3.s" "Neck4.is";
connectAttr "Neck4.s" "Head.is";
connectAttr "Head.s" "Jaw.is";
connectAttr "Jaw.s" "JawEnd.is";
connectAttr "Head.s" "Eye.is";
connectAttr "Eye.s" "EyeEnd.is";
connectAttr "Head.s" "HeadEnd.is";
connectAttr "Chest.s" "Scapula.is";
connectAttr "Scapula.s" "Shoulder.is";
connectAttr "Shoulder.s" "Elbow.is";
connectAttr "Elbow.s" "Wrist.is";
connectAttr "Wrist.s" "Fingers1.is";
connectAttr "Fingers1.s" "Fingers2.is";
connectAttr "Fingers2.s" "Fingers3.is";
connectAttr "Fingers3.s" "Fingers4.is";
connectAttr "Fingers3.s" "frontFootSideInner.is";
connectAttr "Fingers3.s" "frontFootSideOuter.is";
connectAttr "Fingers3.s" "frontHeel.is";
relationship "link" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
connectAttr "layerManager.dli[0]" "defaultLayer.id";
connectAttr "defaultRenderLayer.msg" ":defaultRenderingList1.r" -na;
// End of horse.ma
