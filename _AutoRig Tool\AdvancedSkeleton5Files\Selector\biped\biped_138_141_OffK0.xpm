/* XPM */
static char *biped_138_141_OffK0[] = {
/* columns rows colors chars-per-pixel */
"20 41 139 2",
"   c gray54",
".  c #8B8A8B",
"X  c #8B8B8B",
"o  c gray55",
"O  c #8D8C8C",
"+  c #8D8D8D",
"@  c #8D8D8E",
"#  c gray56",
"$  c #909090",
"%  c #939292",
"&  c #939393",
"*  c gray59",
"=  c gray61",
"-  c #A5A5A5",
";  c #ABAAAB",
":  c #AEAEAE",
">  c #B1B1B1",
",  c #B7B7B7",
"<  c #BBBBBC",
"1  c #BEBDBE",
"2  c gray75",
"3  c #C4C4C3",
"4  c #C5C5C6",
"5  c #CACAC9",
"6  c #CBCBCB",
"7  c #CDCECE",
"8  c #CECECD",
"9  c #D3D4D3",
"0  c #D4D5D5",
"q  c #D8D8D7",
"w  c #D8D9D8",
"e  c #D9D8D8",
"r  c gray85",
"t  c #D9DADB",
"y  c #DAD9DA",
"u  c #DADADA",
"i  c #DADBDC",
"p  c #DDDDDD",
"a  c #DFDEE0",
"s  c #E1E1E1",
"d  c #E3E3E2",
"f  c #E3E3E4",
"g  c #E3E4E3",
"h  c #E4E3E3",
"j  c #E6E4E6",
"k  c #E7E6E7",
"l  c #E9E8E9",
"z  c #ECEBEA",
"x  c #EDECEC",
"c  c #EEEEEE",
"v  c #EFEFEF",
"b  c #F1F0EF",
"n  c #F1F0F1",
"m  c #F1F1F1",
"M  c #F2F2F1",
"N  c #F4F3F4",
"B  c #F4F4F4",
"V  c #F4F4F5",
"C  c #F4F5F5",
"Z  c #F5F4F4",
"A  c gray96",
"S  c #F6F7F6",
"D  c #F7F8F8",
"F  c #F8F7F7",
"G  c #F8F8F8",
"H  c #F8F8F9",
"J  c #F9F9FA",
"K  c #F9FAF9",
"L  c #F9FBFB",
"P  c #FAF9F9",
"I  c #FAF9FA",
"U  c #FAFAF9",
"Y  c gray98",
"T  c #FAFAFB",
"R  c #FBFAFA",
"E  c #FBFAFB",
"W  c #FBFBFC",
"Q  c #FBFCFB",
"!  c #FBFCFC",
"~  c #FCFBFB",
"^  c #FCFBFC",
"/  c #FCFBFD",
"(  c #FCFCFD",
")  c #FCFDFD",
"_  c #FDFCFC",
"`  c #FDFCFD",
"'  c #FDFDFC",
"]  c #FDFDFD",
"[  c #FCFCFE",
"{  c #FCFDFE",
"}  c #FDFCFE",
"|  c #FDFCFF",
" . c #FDFDFE",
".. c #FDFDFF",
"X. c #FCFEFD",
"o. c #FCFFFC",
"O. c #FCFFFD",
"+. c #FDFEFC",
"@. c #FDFEFD",
"#. c #FDFFFC",
"$. c #FDFFFD",
"%. c #FCFEFE",
"&. c #FCFEFF",
"*. c #FCFFFF",
"=. c #FDFEFE",
"-. c #FDFEFF",
";. c #FDFFFE",
":. c #FDFFFF",
">. c #FEFCFC",
",. c #FEFCFD",
"<. c #FEFDFC",
"1. c #FEFDFD",
"2. c #FFFCFD",
"3. c #FFFDFC",
"4. c #FFFDFD",
"5. c #FEFCFE",
"6. c #FEFCFF",
"7. c #FEFDFE",
"8. c #FEFDFF",
"9. c #FFFCFE",
"0. c #FFFCFF",
"q. c #FFFDFE",
"w. c #FFFDFF",
"e. c #FEFEFC",
"r. c #FEFEFD",
"t. c #FEFFFC",
"y. c #FEFFFD",
"u. c #FFFEFC",
"i. c #FFFEFD",
"p. c #FFFFFC",
"a. c #FFFFFD",
"s. c #FEFEFE",
"d. c #FEFEFF",
"f. c #FEFFFE",
"g. c #FEFFFF",
"h. c #FFFEFE",
"j. c #FFFEFF",
"k. c #FFFFFE",
"l. c gray100",
/* pixels */
"2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 ",
"2 f [ >.>.>.>.>.>.>.>.>.>.>.>.>.>.>.>.2 ",
"2 d E >.[ >.>.>.>.>.>.>.>.>.>.>.>.>.>.2 ",
"2 a E >.>.>.>.>.>.>.>.>.>.>.>.>.>.>.>.2 ",
"2 i D >.>.>.>.>.>.>.>.>.>.>.>.>.>.>.>.2 ",
"2 t D [ >.>.>.>.[ >.>.>.>.>.[ >.>.>.>.2 ",
"2 0 D [ >.>.>.>.[ >.>.>.>.>.[ [ >.[ >.2 ",
"2 7 D >.>.>.>.>.>.>.>.>.>.>.[ >.>.[ [ 2 ",
"2 6 C >.>.>.[ >.>.>.>.>.>.>.>.>.>.>.>.2 ",
"2 6 C >.>.[ >.>.>.>.>.>.>.>.[ >.>.>.[ 2 ",
"2 6 C >.>.>.>.>.>.>.[ >.>.[ >.[ >.>.[ 2 ",
"2 6 C >.>.>.>.>.>.>.>.>.>.>.>.>.>.>.[ 2 ",
"2 6 C >.>.>.>.>.>.>.>.>.>.>.>.>.[ >.>.2 ",
"2 6 C >.>.>.>.>.>.[ >.>.>.>.>.>.>.[ [ 2 ",
"2 6 C >.>.>.>.>.>.>.[ [ [ >.[ >.>.>.>.2 ",
"2 6 C >.>.>.>.[ [ >.>.[ >.>.>.>.>.>.>.2 ",
"2 6 C [ >.>.>.>.>.>.>.>.>.>.>.>.>.>.>.2 ",
"2 3 M >.>.>.>.>.>.>.>.>.>.>.>.>.>.>.>.2 ",
"2 2 v >.>.>.>.>.>.[ >.[ >.>.[ >.>.>.>.2 ",
"2 1 m >.>.>.>.>.[ >.>.>.>.[ >.>.>.>.>.2 ",
"2 1 v >.>.>.>.>.>.>.>.>.>.>.>.>.>.>.[ 2 ",
"2 , v [ >.>.>.>.>.>.>.>.>.>.>.>.[ >.>.2 ",
"2 : z [ >.>.>.>.>.>.>.>.>.>.>.>.[ [ >.2 ",
"2 - l >.>.>.>.>.>.>.>.>.>.>.>.>.[ >.>.2 ",
"2 = j >.>.>.>.>.[ >.[ [ [ >.>.>.>.>.>.2 ",
"2 * g >.>.>.>.[ >.>.>.[ >.>.>.>.[ >.>.2 ",
"2 & d [ >.>.>.>.>.>.[ >.[ >.>.>.>.[ >.2 ",
"2 $ i [ [ >.>.>.>.>.>.>.>.[ >.>.[ >.>.2 ",
"2 O u L >.[ >.>.>.[ >.[ >.>.>.>.[ >.[ 2 ",
"2 X u L >.>.>.>.>.>.[ >.[ >.>.>.>.>.>.2 ",
"2 O w D >.>.>.[ >.[ >.[ >.>.>.>.>.[ [ 2 ",
"2 o w [ >.>.>.>.[ >.[ >.[ >.>.o.[ [ [ 2 ",
"2 o u E >.>.>.>.>.>.>.>.>.>.>.>.>.>.[ 2 ",
"2 o u E >.>.>.>.>.>.>.>.>.>.>.[ [ >.L 2 ",
"2 X 9 C >.>.>.>.>.>.>.>.>.>.>.>.>.[ [ 2 ",
"2 X 8 C [ [ >.>.>.>.>.>.[ >.>.>.>.[ D 2 ",
"2 X 4 m >.>.>.>.>.>.>.>.>.>.>.>.>.>.[ 2 ",
"2 o 2 z >.>.>.>.>.>.>.>.>.>.>.>.[ [ L 2 ",
"2 & : k >.>.>.>.>.>.>.>.>.>.>.>.>.[ L 2 ",
"2 & ; h >.>.>.>.>.>.>.>.[ >.>.>.>.>.E 2 ",
"2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 "
};
