/* XPM */
static char *biped_104_96_OffK1[] = {
/* columns rows colors chars-per-pixel */
"30 30 98 2",
"   c #9B919E",
".  c #9B919F",
"X  c #9C909E",
"o  c #9C919E",
"O  c #9D909E",
"+  c #9D919E",
"@  c #9E909D",
"#  c #9F909D",
"$  c #9E909E",
"%  c #8E93A3",
"&  c #8C93A4",
"*  c #8D93A4",
"=  c #9492A1",
"-  c #9592A1",
";  c #9692A1",
":  c #A38F9C",
">  c #A48F9B",
",  c #A58F9B",
"<  c #A68F9B",
"1  c #A78F9A",
"2  c #AF8D97",
"3  c #A98E99",
"4  c #A88F9A",
"5  c #A98E9A",
"6  c #A98F9A",
"7  c #AA8E99",
"8  c #AF8D98",
"9  c #A0909D",
"0  c #A1909C",
"q  c #A1909D",
"w  c #A2909C",
"e  c #A3909C",
"r  c #B08D97",
"t  c #B18D96",
"y  c #B18D97",
"u  c #B28D96",
"i  c #B38C96",
"p  c #B38D96",
"a  c #B48C95",
"s  c #B58C95",
"d  c #B48D96",
"f  c #BA8B93",
"g  c #B88C94",
"h  c #BD8B92",
"j  c #BE8A91",
"k  c #BF8A91",
"l  c gray75",
"z  c #C5898F",
"x  c #C8898E",
"c  c #C9898E",
"v  c #CA888D",
"b  c #CA898D",
"n  c #CB888D",
"m  c #CB898D",
"M  c #CF888B",
"N  c #CC888D",
"B  c #CD888C",
"V  c #CD888D",
"C  c #CE888C",
"Z  c #CF888C",
"A  c #C08A91",
"S  c #C18A91",
"D  c #C28A90",
"F  c #C28A91",
"G  c #C38A90",
"H  c #C48990",
"J  c #C48A90",
"K  c #C58A90",
"L  c #D2878B",
"P  c #D3878A",
"I  c #D48789",
"U  c #D58789",
"Y  c #D4878A",
"T  c #D5878A",
"R  c #D68789",
"E  c #D78688",
"W  c #D78689",
"Q  c #D78789",
"!  c #D0888C",
"~  c #DB8687",
"^  c #DC8587",
"/  c #DD8586",
"(  c #DD8587",
")  c #DC8687",
"_  c #DE8586",
"`  c #DF8586",
"'  c #D88688",
"]  c #D88689",
"[  c #D98688",
"{  c #DA8688",
"}  c #E08585",
"|  c #E18485",
" . c #E18585",
".. c #E08586",
"X. c #E28484",
"o. c #E28485",
"O. c #E38484",
"+. c #E38585",
/* pixels */
"l l l l l l l l l l l l l l l l l l l l l l l l l l l l l l ",
"l O.O.O.O.O.O.O.O.O.N a e + % % + w a Z ^ O.O.O.O.O.O.O.O.l ",
"l O.O.O.O.O.+.^ h + ; < u g F F g u w = w h ^ O.O.O.O.O.O.l ",
"l O.O.O.O.O.N > + u m T [ ~ ^ O.~ [ M m 8 + < N O.O.O.O.O.l ",
"l O.O.O.O.F w 6 K T O.O.O.O.O.O.O.O.O.O.T F 6 e x ^ O.O.O.l ",
"l O.O.O.F > 2 N O.O.O.O.O.O.O.O.O.O.O.O.O.O.N 8 e x O.O.O.l ",
"l O.O.m > u L O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.M 2 < m X.O.l ",
"l O.^ 4 4 M O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.N 4 4 ^ O.l ",
"l +.F + K ^ O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.^ z + F O.l ",
"l ^ w 8 R O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.T 2 + ^ l ",
"l N ; m O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.X.m ; M l ",
"l u e M O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.X.L < u l ",
"l w u [ O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.~ u + l ",
"l ; h ^ O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.^ h = l ",
"l % F ^ O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.X.X.^ z % l ",
"l % z ^ O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.X.^ z % l ",
"l ; h ^ O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.^ h ; l ",
"l + u E O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.~ u + l ",
"l u < I O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.X.X.L : u l ",
"l m . x X.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.X.X.m ; C l ",
"l ^ w u E X.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.+.[ u : ^ l ",
"l O.F + x ^ O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.^ x + F O.l ",
"l O.^ > 2 M X.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.O.C 4 4 ^ O.l ",
"l O.O.m e u M O.O.O.O.O.O.O.O.O.O.O.O.O.O.X.X.L u < N O.O.l ",
"l O.O.O.z > u N O.O.O.O.+.O.O.O.O.O.O.O.O.X.C 8 : F X.X.O.l ",
"l O.O.O.^ z w 6 x R O.O.+.+.O.O.O.O.X.X.T K 4 : F X.O.X.O.l ",
"l O.O.O.O.+.N > + a m M [ ~ O.^ ~ [ T m 8 + < m +.O.O.O.O.l ",
"l O.O.O.O.O.O.[ h + ; > a f F F f u w = + h [ O.O.O.O.O.O.l ",
"l O.O.O.O.O.O.O.O.O.N a w . & & . : u N O.O.O.O.O.O.O.O.O.l ",
"l l l l l l l l l l l l l l l l l l l l l l l l l l l l l l "
};
