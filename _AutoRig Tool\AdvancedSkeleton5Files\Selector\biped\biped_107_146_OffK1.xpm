/* XPM */
static char *biped_107_146_OffK1[] = {
/* columns rows colors chars-per-pixel */
"15 15 46 1",
"  c #4444AA",
". c #5044A5",
"X c #5145A6",
"o c #5344A4",
"O c #5444A4",
"+ c #5747A4",
"@ c #6046A0",
"# c #6047A0",
"$ c #965891",
"% c #9A6294",
"& c #9B6294",
"* c #B0608A",
"= c #B1618A",
"- c #B2618A",
"; c #B3628A",
": c #B56289",
"> c #B66389",
", c #B76B8C",
"< c #B86E8D",
"1 c #B06F90",
"2 c #B16F90",
"3 c #B26F90",
"4 c #B4708F",
"5 c #B5708F",
"6 c #B6708E",
"7 c #B6718E",
"8 c #BF718B",
"9 c #BC708C",
"0 c #BD708C",
"q c #B07090",
"w c #B17090",
"e c #B27090",
"r c #B37190",
"t c #D67985",
"y c #D67D87",
"u c #D67E87",
"i c #DA7C85",
"p c #DB7C85",
"a c gray75",
"s c #DA8086",
"d c #DB8086",
"f c #DB8186",
"g c #DD8185",
"h c #DC8086",
"j c #DE8185",
"k c #E38484",
/* pixels */
"aaaaaaaaaaaaaaa",
"akkkkd5+rhkkkka",
"akkkkd1 wskkkka",
"akkkkd1 wskkkka",
"akkkkdw rhkkkka",
"aggkgy1 1igggga",
"at<<9,& &<<<98a",
"aOo+.+. oO++.@a",
"a:*===$ $===-:a",
"atiiit, ,tipipa",
"akkkkg5 <gkkkka",
"akkkkd1 wdkkkka",
"akkkkiw wskkkka",
"akkkkdr.rskkkka",
"aaaaaaaaaaaaaaa"
};
