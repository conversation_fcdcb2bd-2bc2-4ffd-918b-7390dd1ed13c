import maya.cmds as mc

origJnts = mc.ls(sl=True)
origJnts.reverse()

fkJnts = mc.duplicate(origJnts, po=True, f=True)

for i in range(0, len(origJnts)):
    fkJntName = origJnts[i].replace("jnt", "jnt_drv_fk")
    mc.rename(fkJnts[i], fkJntName)
    fkJnts[i] = fkJntName

ikJnts = mc.duplicate(origJnts, po=True, f=True)
for i in range(0, len(origJnts)):
    ikJntName = origJnts[i].replace("jnt", "jnt_drv_ik")
    mc.rename(ikJnts[i], ikJntName)
    ikJnts[i] = ikJntName

    
ikfkBlendName = origJnts[-1].replace("jnt", "ac_ikfkBlend")
mc.curve(d=1, n=ikfkBlendName, p=[(-1, 0, 1), (-1, 0, 3), (1, 0, 3), (1, 0, 1), (3, 0, 1), (3, 0, -1), (1, 0, -1), (1, 0, -3), (-1, 0, -3),(-1, 0, -1),(-3, 0, -1), (-3, 0, 1), (-1, 0, 1)])
mc.addAttr(ikfkBlendName, ln="ikfkBlend", min=0, max=1, k=True)
mc.setAttr(ikfkBlendName+".translateX", l=True, k=False, cb=False)
mc.setAttr(ikfkBlendName+".translateY", l=True, k=False, cb=False)
mc.setAttr(ikfkBlendName+".translateZ", l=True, k=False, cb=False)

mc.setAttr(ikfkBlendName+".scaleX", l=True, k=False, cb=False)
mc.setAttr(ikfkBlendName+".scaleY", l=True, k=False, cb=False)
mc.setAttr(ikfkBlendName+".scaleZ", l=True, k=False, cb=False)

mc.setAttr(ikfkBlendName+".rotateX", l=True, k=False, cb=False)
mc.setAttr(ikfkBlendName+".rotateY", l=True, k=False, cb=False)
mc.setAttr(ikfkBlendName+".rotateZ", l=True, k=False, cb=False)

mc.setAttr(ikfkBlendName+".visibility", l=True, k=False, cb=False)

for i in range(0, len(origJnts)):
    blendColorName = origJnts[i].replace("jnt", "blendColors")
    mc.createNode("blendColors", n = blendColorName)
    mc.connectAttr(ikJnts[i]+".rotate", blendColorName + ".color1")
    mc.connectAttr(fkJnts[i]+".rotate", blendColorName + ".color2")
    mc.connectAttr(blendColorName+".output", origJnts[i]+ ".rotate")
    mc.connectAttr(ikfkBlendName+".ikfkBlend", blendColorName+".blender")
    
#create the arrow
ikEndCtrl = ikJnts[0].replace("jnt", "ac_ik")
mc.curve(d=1,n = ikEndCtrl, p=[(-1,0,-1), (-1,0,-3),(-2,0,-3),(0,0,-5),(2,0,-3),(1,0,-3),(1,0,-1),(3,0,-1),(3,0,-2),(5,0,0),(3,0,2),(3,0,1),(1,0,1),(1,0,3),(2,0,3),(0,0,5),(-2,0,3),(-1,0,3),(-1,0,1),(-3,0,1),(-3,0,2),(-5,0,0),(-3,0,-2),(-3,0,-1),(-1,0,-1)])
ikEndCtrlGrp = ikEndCtrl+"_grp"
mc.group(ikEndCtrl, n=ikEndCtrlGrp)
mc.matchTransform(ikEndCtrlGrp, ikJnts[0])
mc.orientConstraint(ikEndCtrl, ikJnts[0])
mc.setAttr(ikEndCtrlGrp+".scaleX", 16)
mc.setAttr(ikEndCtrlGrp+".scaleY", 16)
mc.setAttr(ikEndCtrlGrp+".scaleZ", 16)
    
ikPoleVec = ikJnts[1].replace("jnt", "ac_ik")
mc.curve(n=ikPoleVec, p=[(-0.5, 0.5, 0.5), (0.5, 0.5, 0.5), (0.5, 0.5, -0.5), (-0.5, 0.5, -0.5), (-0.5, 0.5, 0.5), (-0.5, -0.5, 0.5), (-0.5, -0.5, -0.5), (-0.5, 0.5, -0.5), (0.5, 0.5, -0.5), (0.5, -0.5, -0.5), (-0.5, -0.5, -0.5), (-0.5, -0.5, 0.5), (0.5, -0.5, 0.5), (0.5, 0.5, 0.5), (0.5, -0.5, 0.5), (0.5, -0.5, -0.5)], d=1)
ikPoleVecGrp = ikPoleVec+"_grp"
mc.group(ikPoleVec, n=ikPoleVecGrp)

PoleVecPos = list(mc.xform(ikJnts[-1], q=True, t=True, ws=True))
mc.setAttr(ikPoleVecGrp+".translateX", PoleVecPos[0])
mc.setAttr(ikPoleVecGrp+".translateY", PoleVecPos[1])
mc.setAttr(ikPoleVecGrp+".translateZ", PoleVecPos[2])
    
