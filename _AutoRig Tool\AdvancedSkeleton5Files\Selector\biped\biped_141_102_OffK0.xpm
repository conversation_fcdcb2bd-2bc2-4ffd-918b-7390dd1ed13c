/* XPM */
static char *biped_141_102_OffK0[] = {
/* columns rows colors chars-per-pixel */
"38 18 115 2",
"   c gray54",
".  c #8B8B8B",
"X  c gray55",
"o  c #8D8D8C",
"O  c gray56",
"+  c #909090",
"@  c #909191",
"#  c gray57",
"$  c #929292",
"%  c #959595",
"&  c gray59",
"*  c #9A9A9A",
"=  c #9B9B9B",
"-  c gray61",
";  c gray62",
":  c #AAAAAB",
">  c gray67",
",  c gray75",
"<  c #CACACB",
"1  c #CBCBCA",
"2  c #D5D6D5",
"3  c #D6D5D6",
"4  c gray84",
"5  c #D8D6D7",
"6  c #D9D7D8",
"7  c #D8D8D7",
"8  c #DAD9D9",
"9  c #DDDCDC",
"0  c #DEDEDD",
"q  c #DFDEDF",
"w  c #DFDFDE",
"e  c #E0DFDF",
"r  c gray88",
"t  c #E1E0E0",
"y  c #E1E1E0",
"u  c #E5E5E4",
"i  c gray90",
"p  c #E6E5E5",
"a  c #E6E6E5",
"s  c #E9E9EB",
"d  c #EAE9E9",
"f  c #EBE9E9",
"g  c #EAEBEA",
"h  c #EDEDEB",
"j  c gray93",
"k  c #F6F7F5",
"l  c #F6F6F7",
"z  c #F9F8F7",
"x  c #F8F8F8",
"c  c #F8F9F8",
"v  c #F8F9F9",
"b  c #F9F9F8",
"n  c #FAF8F8",
"m  c #FAFAF8",
"M  c #FAFAF9",
"N  c #FAFAFB",
"B  c #FAFBFA",
"V  c #FBFAFA",
"C  c #FBFBFA",
"Z  c #FBFBFB",
"A  c #FBFBFC",
"S  c #FBFCFB",
"D  c #FBFCFC",
"F  c #FCFBFC",
"G  c #FDFBFD",
"H  c #FCFDFC",
"J  c #FCFDFD",
"K  c #FDFCFC",
"L  c #FDFCFD",
"P  c #FDFDFC",
"I  c #FDFDFD",
"U  c #FCFDFE",
"Y  c #FCFDFF",
"T  c #FDFCFE",
"R  c #FDFCFF",
"E  c #FDFDFE",
"W  c #FDFDFF",
"Q  c #FCFEFD",
"!  c #FCFFFD",
"~  c #FDFEFC",
"^  c #FDFEFD",
"/  c #FDFFFC",
"(  c #FDFFFD",
")  c #FCFEFF",
"_  c #FDFEFE",
"`  c #FDFEFF",
"'  c #FDFFFE",
"]  c #FDFFFF",
"[  c #FEFCFD",
"{  c #FEFDFC",
"}  c #FEFDFD",
"|  c #FFFCFD",
" . c #FFFDFC",
".. c #FFFDFD",
"X. c #FEFCFE",
"o. c #FEFCFF",
"O. c #FEFDFE",
"+. c #FEFDFF",
"@. c #FFFCFE",
"#. c #FFFCFF",
"$. c #FFFDFE",
"%. c #FFFDFF",
"&. c #FEFEFC",
"*. c #FEFEFD",
"=. c #FEFFFD",
"-. c #FFFEFD",
";. c #FFFFFD",
":. c #FEFEFE",
">. c #FEFEFF",
",. c #FEFFFE",
"<. c #FEFFFF",
"1. c #FFFEFE",
"2. c #FFFEFF",
"3. c #FFFFFE",
"4. c gray100",
/* pixels */
", , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , ",
", - & s :.:.:.:.:.G V :.:.:.:.:.:.:.:.:.:.G G :.:.:./ :.G :.:.:.G :.f & = , ",
", - $ a S :.:.:.:.:.G :.:.:.:.:.A :.:.:.:.:./ :./ / / :.G / :.:.:./ a O - , ",
", - o y S :.:./ :.:.:.:.:.:.:.:.:.:.:.:.:.:.:./ :.G G G G :.:.:.:.G e o - , ",
", ; . 0 S :.:./ :.:.:.G :.G :.:.:.:.:.:.:.:.:.:.:.:.G :.:.:.:.:./ V 9   - , ",
", -   8 z :.:.:.:.:.:.:.:.:.:.G :.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:./ N 5   ; , ",
", ;   2 c :.:.:.:.A :.G :.:.:.:.:.:.:.:.V G :.:.:.:.:.:.:.:.:.:./ z 2   ; , ",
", ;   2 c :.:.:.:.:.:.:.:.G G G :.:.:.:./ :.:.:.:.:.:.A :.:.A :.:.N 4 o ; , ",
", ; . 4 N :./ / :.:.A :.:.:.:.:.:.:.:.:.:.G :.:.:.:.:.:.:.:.:.:./ z 2   - , ",
", ; . 4 z :.:.:.G :.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.z 2   - , ",
", -   7 n :.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:.:./ :.c 4   ; , ",
", = $ r N :.G :.A :.:.:.:.A :.:.:.:.:.:.:.:.:.:.:.:.:.:.G G G / :.S q $ - , ",
", $ > j :.:.:.G :.:.:.:.:.A :.:.:.:.:.:.:.G :.:.:.:./ / :.:./ V :.A j : O , ",
", o 1 l :.:.:.:.:.A :.A :.:.A A :./ :./ :.:.:.:.G :.:.:.:./ :.:.G :.k < . , ",
",   w N :.:.G G :.:.:.:.A A :.:./ :.:.:.:.:.:.:.:.:.:./ :.:.:.:.G A N q   , ",
", . a A :.:.:.G S :.:.:.:.:.:.:./ :.:.G G :.G :.:.:.:.:.:./ :.G :.:.:.a   , ",
", $ f / :.:.G G / :.:.:.:.:.:.:.G :.:.G :.:.:.:.G :.:.:.:.:.:.:.:.G G f $ , ",
", , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , , "
};
