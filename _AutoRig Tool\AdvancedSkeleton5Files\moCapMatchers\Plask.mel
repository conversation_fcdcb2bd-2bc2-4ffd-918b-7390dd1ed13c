//Mel script for Plask MoCapMather (https://app.plask.ai)
//Plask uses non-zero rotation values in "Bind Pose" for some joints.
//Compensating for this here:

global proc MoCapZeroOut ()
{
print "// executing custom code for Plask MoCapMather ZeroOut.\n";

string $nameSpaceB=`textField -q -tx asMappingUINameSpacesTextFieldB1`;
if (`objExists ($nameSpaceB+"hips")`) setAttr ($nameSpaceB+"hips.r") -type float3 -90 0 90;
if (`objExists ($nameSpaceB+"rightUpLeg")`) setAttr ($nameSpaceB+"rightUpLeg.rz") 180;
if (`objExists ($nameSpaceB+"leftUpLeg")`) setAttr ($nameSpaceB+"leftUpLeg.rz") 180;
if (`objExists ($nameSpaceB+"rightFoot")`) setAttr ($nameSpaceB+"rightFoot.rz") -90;
if (`objExists ($nameSpaceB+"leftFoot")`) setAttr ($nameSpaceB+"leftFoot.rz") -90;
if (`objExists ($nameSpaceB+"rightShoulder")`) setAttr ($nameSpaceB+"rightShoulder.r") -type float3 90 90 0;
if (`objExists ($nameSpaceB+"leftShoulder")`) setAttr ($nameSpaceB+"leftShoulder.r") -type float3 -90 -90 0;
}