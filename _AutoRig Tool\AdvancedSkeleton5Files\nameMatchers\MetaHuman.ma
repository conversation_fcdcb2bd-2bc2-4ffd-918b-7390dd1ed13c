//Maya ASCII 2012 scene
//Name: MetaHuman.ma
//Last modified: Fri, May 06, 2022 12:14:12 PM
//Codeset: 1252
requires maya "2012";
requires "stereoCamera" "10.0";
currentUnit -l centimeter -a degree -t pal;
fileInfo "application" "maya";
fileInfo "product" "Maya 2012";
fileInfo "version" "2012 x64";
fileInfo "cutIdentifier" "201201172029-821146";
fileInfo "osv" "Microsoft Home Premium Edition, 64-bit  (Build 9200)\n";
createNode transform -s -n "persp";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -31.992462368193355 25.721352485387445 45.108790459537836 ;
	setAttr ".r" -type "double3" -17.738352729646163 -34.600000000021026 0 ;
	setAttr ".rp" -type "double3" 0 1.4210854715202004e-014 7.1054273576010019e-015 ;
	setAttr ".rpt" -type "double3" -1.0509691225740242e-014 5.6607642906554236e-015 
		1.5195103972627611e-014 ;
createNode camera -s -n "perspShape" -p "persp";
	setAttr -k off ".v" no;
	setAttr ".fl" 34.999999999999979;
	setAttr ".ncp" 1;
	setAttr ".coi" 55.378514308312518;
	setAttr ".imn" -type "string" "persp";
	setAttr ".den" -type "string" "persp_depth";
	setAttr ".man" -type "string" "persp_mask";
	setAttr ".tp" -type "double3" -2.0411491882433546 8.8491425054916082 1.6918903377324019 ;
	setAttr ".hc" -type "string" "viewSet -p %camera";
createNode transform -s -n "top";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -2.0411491882433559 196.40480710857878 1.6918903377324437 ;
	setAttr ".r" -type "double3" -89.999999999999986 0 0 ;
createNode camera -s -n "topShape" -p "top";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 100.1;
	setAttr ".ow" 24.565578017353737;
	setAttr ".imn" -type "string" "top";
	setAttr ".den" -type "string" "top_depth";
	setAttr ".man" -type "string" "top_mask";
	setAttr ".hc" -type "string" "viewSet -t %camera";
	setAttr ".o" yes;
createNode transform -s -n "front";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -2.0411491882433559 8.8491425054916135 135.1611800278512 ;
createNode camera -s -n "frontShape" -p "front";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 100.1;
	setAttr ".ow" 38.14429574522314;
	setAttr ".imn" -type "string" "front";
	setAttr ".den" -type "string" "front_depth";
	setAttr ".man" -type "string" "front_mask";
	setAttr ".hc" -type "string" "viewSet -f %camera";
	setAttr ".o" yes;
createNode transform -s -n "side";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 155.46197521005064 8.8491425054916135 1.6918903377324384 ;
	setAttr ".r" -type "double3" 0 89.999999999999986 0 ;
createNode camera -s -n "sideShape" -p "side";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 100.1;
	setAttr ".ow" 38.08719350608358;
	setAttr ".imn" -type "string" "side";
	setAttr ".den" -type "string" "side_depth";
	setAttr ".man" -type "string" "side_mask";
	setAttr ".hc" -type "string" "viewSet -s %camera";
	setAttr ".o" yes;
createNode transform -n "FitSkeleton";
	addAttr -ci true -sn "visCylinders" -ln "visCylinders" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "visBoxes" -ln "visBoxes" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "visBones" -ln "visBones" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "lockCenterJoints" -ln "lockCenterJoints" -dv 1 -min 0 -max 
		1 -at "bool";
	addAttr -ci true -sn "visGap" -ln "visGap" -dv 0.75 -min 0 -max 1 -at "double";
	addAttr -ci true -m -im false -sn "drivingSystem" -ln "drivingSystem" -at "message";
	addAttr -ci true -m -sn "drivingSystem_Fingers_R" -ln "drivingSystem_Fingers_R" 
		-dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -m -sn "drivingSystem_Fingers_L" -ln "drivingSystem_Fingers_L" 
		-dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visGeo" -ln "visGeo" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visGeoType" -ln "visGeoType" -min 0 -max 3 -en "cylinders:boxes:spheres:bones" 
		-at "enum";
	addAttr -ci true -sn "visSpheres" -ln "visSpheres" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visPoleVector" -ln "visPoleVector" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visJointOrient" -ln "visJointOrient" -min 0 -max 1 
		-at "bool";
	addAttr -ci true -k true -sn "visJointAxis" -ln "visJointAxis" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "objectsSkin" -ln "objectsSkin" -dt "string";
	addAttr -ci true -sn "objectsAll" -ln "objectsAll" -dt "string";
	addAttr -ci true -sn "objectsRightEye" -ln "objectsRightEye" -dt "string";
	addAttr -ci true -sn "objectsLeftEye" -ln "objectsLeftEye" -dt "string";
	addAttr -ci true -sn "gameEngine" -ln "gameEngine" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "primaryAxis" -ln "primaryAxis" -min 0 -max 5 -en "X:Y:Z:-X:-Y:-Z" 
		-at "enum";
	addAttr -ci true -sn "secondaryAxis" -ln "secondaryAxis" -dv 1 -min 0 -max 5 -en 
		"X:Y:Z:-X:-Y:-Z" -at "enum";
	addAttr -ci true -sn "worldmatch" -ln "worldmatch" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "preRebuildScript" -ln "preRebuildScript" -dt "string";
	addAttr -ci true -sn "postRebuildScript" -ln "postRebuildScript" -dt "string";
	addAttr -ci true -sn "zUpAxis" -ln "zUpAxis" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "mirTrans" -ln "mirTrans" -min 0 -max 1 -at "bool";
	setAttr -l on ".v";
	setAttr ".ove" yes;
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr ".visCylinders" yes;
	setAttr ".lockCenterJoints" no;
	setAttr ".visGap" 1;
	setAttr -s 35 ".drivingSystem";
	setAttr -s 17 ".drivingSystem_Fingers_R";
	setAttr -s 18 ".drivingSystem_Fingers_L";
	setAttr -s 18 ".drivingSystem_Fingers_L";
createNode nurbsCurve -n "FitSkeletonShape" -p "FitSkeleton";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 29;
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		2.3038181771802018 1.4106817782506096e-016 -2.3038181771801982
		-3.7170921587018218e-016 1.9950053029946086e-016 -3.2580909114099006
		-2.3038181771801995 1.4106817782506103e-016 -2.3038181771801995
		-3.2580909114099006 5.7810262571592305e-032 -9.4411323512774565e-016
		-2.3038181771802 -1.4106817782506101e-016 2.3038181771801987
		-9.8172637689561278e-016 -1.9950053029946088e-016 3.258090911409901
		2.3038181771801982 -1.4106817782506103e-016 2.3038181771801995
		3.2580909114099006 -1.0715212399640087e-031 1.7499269841884922e-015
		2.3038181771802018 1.4106817782506096e-016 -2.3038181771801982
		-3.7170921587018218e-016 1.9950053029946086e-016 -3.2580909114099006
		-2.3038181771801995 1.4106817782506103e-016 -2.3038181771801995
		;
createNode joint -n "Root" -p "FitSkeleton";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "centerBtwFeet" -ln "centerBtwFeet" -dv 1 -min 0 -max 
		1 -at "bool";
	addAttr -ci true -k true -sn "numMainExtras" -ln "numMainExtras" -min 0 -at "long";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.7000000000000002 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 8.2460314548687276e-020 9.3978848659380922 0.22352488511729521 ;
	setAttr ".r" -type "double3" 9.5416640443905535e-015 1.2722218725854065e-014 1.9083328088781101e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 89.999999999999972 3.6331067333020681 89.999999999999986 ;
	setAttr ".dl" yes;
	setAttr ".typ" 1;
	setAttr ".otp" -type "string" "Mid";
	setAttr ".fatYabs" 1.1050000190734863;
	setAttr -k on ".fat" 1.6660000000000001;
	setAttr -k on ".fatFront" 0.64999999999999991;
	setAttr -k on ".fatWidth";
createNode joint -n "Hip" -p "Root";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "twistJoints" -ln "twistJoints" -dv 2 -min 0 -max 10 
		-at "long";
	addAttr -ci true -k true -sn "bendyJoints" -ln "bendyJoints" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.87 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" -0.23184116760210038 0.011709803586535616 -0.97697093301666715 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 8.408538378283577 176.87446015050378 3.5601334487861624 ;
	setAttr ".dl" yes;
	setAttr ".typ" 2;
	setAttr ".fatYabs" 0.87000000476837158;
	setAttr -k on ".fat" 0.85259999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "Knee" -p "Hip";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.6 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 4.247443642104308 -5.9952043329758453e-015 -1.3322676295501878e-015 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.5444437451708128e-014 1.1032549051326569e-014 -5.0048444112325861 ;
	setAttr ".fatYabs" 0.60000002384185791;
	setAttr -k on ".fat" 0.58800000000000008;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "Ankle" -p "Knee";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "worldOrient" -ln "worldOrient" -min 0 -max 5 -en "xUp:yUp:zUp:xDown:yDown:zDown" 
		-at "enum";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.36999999999999988 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 4.1373591371870848 -2.6298407895808396e-015 -3.3306690738754696e-015 ;
	setAttr ".ro" 3;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0.09341902666283676 3.0812202665700834 4.4750600598882855 ;
	setAttr ".pa" -type "double3" 3.1147589914174403 -1.2104724556304993 -11.405913270501992 ;
	setAttr ".dl" yes;
	setAttr ".typ" 4;
	setAttr ".fatYabs" 0.37000000476837158;
	setAttr -k on ".worldOrient" 3;
	setAttr -k on ".fat" 0.36259999999999992;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "Heel" -p "Ankle";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.36999999999999988 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.80252144353017507 -0.65626566540624232 -0.0076078589425090382 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -0.11823651824766351 89.306938320946657 88.07343559651467 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Heel";
	setAttr -k on ".fat" 0.36259999999999992;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "Toes" -p "Ankle";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.69398398264718342 1.2943143737032095 -7.8381745538536052e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0.091814729368993639 -0.68702877233107817 80.461338428558605 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr ".fatYabs" 0.30000001192092896;
	setAttr -k on ".fat" 0.29399999999999993;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "FootSideInner" -p "Toes";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.022869567131138524 -0.16849110729441502 -0.39200000001578428 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.4294267241786742e-009 90.000074725077667 7.7297836404762954 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "BigToe";
	setAttr -k on ".fat" 0.29399999999999993;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "FootSideOuter" -p "Toes";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.022870580331863399 -0.16849096976795658 0.39200000002629798 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.4294267241786742e-009 90.000074725077667 7.7297836404762954 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "PinkyToe";
	setAttr -k on ".fat" 0.29399999999999993;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "ToesEnd" -p "Toes";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.61436297403860229 1.3322676295501878e-015 1.7763568394002505e-015 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "ToesEnd";
	setAttr ".fatYabs" 0.30000001192092896;
	setAttr -k on ".fat" 0.29399999999999993;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "Spine1" -p "Root";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 17 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.36035124733312429 -1.1102230246251565e-016 -1.0520216523840305e-016 ;
	setAttr ".r" -type "double3" -8.3857321447530095e-016 -6.6113915262003324e-015 6.8381925651465615e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -8.1006770079450953e-016 -1.277686925678943e-014 14.457322528975755 ;
	setAttr -k on ".fat" 1.6660000000000001;
	setAttr -k on ".fatFront" 0.64999999999999991;
	setAttr -k on ".fatWidth";
createNode joint -n "Spine2" -p "Spine1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 17 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.66591560625007418 1.3322676295501878e-015 7.684036823900925e-017 ;
	setAttr ".r" -type "double3" -1.4380307307899388e-015 4.7550128866744883e-014 8.7465253740246703e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.1500739436420041e-014 -4.980538981556931e-015 -3.4644695362522868 ;
	setAttr -k on ".fat" 1.6660000000000001;
	setAttr -k on ".fatFront" 0.64999999999999991;
	setAttr -k on ".fatWidth";
createNode joint -n "Spine3" -p "Spine2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 17 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.7093463093175103 -2.2204460492503131e-016 -5.8869097699414702e-016 ;
	setAttr ".r" -type "double3" -8.0885741485838934e-015 8.4419402488687392e-014 -3.9597905784220783e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.0377365304718054e-017 -6.3445139823128789e-015 -10.946079305083995 ;
	setAttr -k on ".fat" 1.6660000000000001;
	setAttr -k on ".fatFront" 0.64999999999999991;
	setAttr -k on ".fatWidth";
createNode joint -n "Spine4" -p "Spine3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 17 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.83534153849636006 -5.9952043329758453e-015 -1.2307893208389316e-015 ;
	setAttr ".r" -type "double3" 2.0867679098580227e-013 -4.0722293268328395e-012 6.3611093629196175e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 4.9398186833040746e-015 -3.5872867342838387e-015 -5.8669831288477674 ;
	setAttr -k on ".fat" 1.6660000000000001;
	setAttr -k on ".fatFront" 0.64999999999999991;
	setAttr -k on ".fatWidth";
createNode joint -n "Chest" -p "Spine4";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.7000000000000002 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 1.9051003896255185 -4.4408920985006262e-016 1.354027424554423e-013 ;
	setAttr ".r" -type "double3" -4.6869978927905993e-014 7.8821845389221198e-012 5.4029672651361164e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.6041898067760327e-015 -9.525926112840642e-015 -0.68138986456947681 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Chest";
	setAttr ".fatYabs" 1.1050000190734863;
	setAttr -k on ".fat" 1.6660000000000001;
	setAttr -k on ".fatFront" 0.64999999999999991;
	setAttr -k on ".fatWidth";
createNode joint -n "Neck" -p "Chest";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -max 10 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.32 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 1.1650010168786764 5.3290705182007514e-015 -1.6026927430103308e-013 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.6422499914952775e-015 -7.8373262842753892e-015 23.928403971192527 ;
	setAttr ".pa" -type "double3" -1.7940447748746266e-016 6.8425179703803005e-015 0 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "37";
	setAttr ".fatYabs" 1;
	setAttr -k on ".fat" 0.31360000000000005;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "Head" -p "Neck";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "global" -ln "global" -min 0 -max 10 -at "long";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.32 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.50080543424225432 -7.1054273576010019e-015 -3.6259293483846157e-012 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 8.8368361248436597e-015 -6.9963945983795756e-015 -1.9135285534857409 ;
	setAttr ".otp" -type "string" "36";
	setAttr ".fatYabs" 1;
	setAttr -k on ".fat" 0.31360000000000005;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "HeadEnd" -p "Head";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.35 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.48147115144821839 3.9968028886505635e-015 1.3354973866899578e-012 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "23";
	setAttr ".fatYabs" 0.34999999403953552;
	setAttr -k on ".fat" 0.343;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "Scapula" -p "Chest";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.65 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.54058957799118978 -0.12885149904992277 -0.1399315163767135 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 16.733839798016554 99.170123184599873 26.873008197577043 ;
	setAttr ".otp" -type "string" "PropA1";
	setAttr ".fatYabs" 0.64999997615814209;
	setAttr -k on ".fat" 0.637;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "Shoulder" -p "Scapula";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "twistJoints" -ln "twistJoints" -dv 2 -min 0 -max 10 
		-at "long";
	addAttr -ci true -k true -sn "bendyJoints" -ln "bendyJoints" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.65 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 1.7453433392965245 3.8885561437496108e-014 -3.1974423109204508e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 4.3371908420707417 46.028197656351082 4.3583982319389643 ;
	setAttr ".pa" -type "double3" -4.1293130717023516e-007 0 0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 10;
	setAttr ".fatYabs" 0.64999997615814209;
	setAttr -k on ".fat" 0.637;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "Elbow" -p "Shoulder";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "twistJoints" -ln "twistJoints" -dv 2 -min 0 -max 10 
		-at "long";
	addAttr -ci true -k true -sn "bendyJoints" -ln "bendyJoints" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.44999999999999984 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 2.7215280880988226 1.4432899320127035e-015 4.9737991503207013e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.9083328088781094e-014 -1.1927080055488184e-015 
		38.978821580626445 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "22";
	setAttr ".fatYabs" 0.44999998807907104;
	setAttr -k on ".fat" 0.44099999999999984;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "Wrist" -p "Elbow";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.16999999999999987 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 2.6705989942997403 7.9936057773011271e-015 -1.5987211554602254e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -23.066861090942158 -8.9988996622760826 5.7848197354442377 ;
	setAttr ".dl" yes;
	setAttr ".typ" 12;
	setAttr ".fatYabs" 0.39269998669624329;
	setAttr -k on ".fat" 0.16659999999999989;
	setAttr -k on ".fatFront" 2.3100000000000005;
	setAttr -k on ".fatWidth";
createNode joint -n "MiddleFinger0" -p "Wrist";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.12 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.33945320171358251 1.7763568394002505e-015 -7.1054273576010019e-015 ;
	setAttr ".r" -type "double3" 2.5141290833631268e-013 -4.8655032201357059e-013 -3.0404114908115405e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -4.3688547083115292 10.273416047475315 -3.1580203770116175 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "17";
	setAttr -k on ".fat" 0.1176;
createNode joint -n "MiddleFinger1" -p "MiddleFinger0";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.59763986936629143 -4.9960036108132044e-016 8.8817841970012523e-015 ;
	setAttr ".r" -type "double3" -8.2495637050457902e-014 7.8620578436598768e-013 3.0195391007144203e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 4.7966719129393534 31.175771024584584 5.2606372692390737 ;
	setAttr ".pa" -type "double3" -2.490303168013669e-017 3.8068719241856406 -4.0949047407001542 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "21";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "MiddleFinger2" -p "MiddleFinger1";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.50656116010765206 1.1102230246251565e-015 -5.3290705182007514e-015 ;
	setAttr ".r" -type "double3" 3.1025316994338108e-013 -2.7729342965254844e-012 2.2621695171908513e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.8201337612954411 20.67468253608104 2.0034078715240109 ;
	setAttr ".pa" -type "double3" 0 0 2.5199999009299203 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "20";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "MiddleFinger3" -p "MiddleFinger2";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.24245659553849386 4.4408920985006262e-016 7.9936057773011271e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -6.3611093629270351e-015 4.7708320221952736e-015 -4.7708320221952736e-015 ;
	setAttr ".pa" -type "double3" 0 0 3.6712939054552742 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "19";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "MiddleFinger4" -p "MiddleFinger3";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.20515912374074752 2.2204460492503131e-016 8.7041485130612273e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "18";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "IndexFinger0" -p "Wrist";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.12 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.34950816534109075 0.21502631036535824 -0.036744702672232066 ;
	setAttr ".r" -type "double3" 6.0728715949193396e-014 -4.9516018292862693e-013 1.4640490768111724e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -4.5775800194752394 12.226528720106359 2.0475009792105241 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "17";
	setAttr -k on ".fat" 0.1176;
createNode joint -n "IndexFinger1" -p "IndexFinger0";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.57646567914075364 3.6637359812630166e-015 3.5527136788005009e-015 ;
	setAttr ".r" -type "double3" -3.0414054141495092e-014 4.3968683662888065e-014 -5.7816520568978972e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.8359874861832397 22.834432944720803 3.3297368306777706 ;
	setAttr ".pa" -type "double3" 0.065532877363568762 20.527688987272207 -2.5422327562497964 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "8";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "IndexFinger2" -p "IndexFinger1";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.39983340524218125 -5.773159728050814e-015 -1.7763568394002505e-015 ;
	setAttr ".r" -type "double3" -8.8409480911305021e-014 7.7879484347734314e-013 1.7368810330804613e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.2344572587387663 14.807080766986081 -1.6039142647681539 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "7";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "IndexFinger3" -p "IndexFinger2";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.25431747111569258 1.8873791418627661e-015 -3.5527136788005009e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.180554681463516e-015 3.1805546814635168e-015 1.5902773407317584e-015 ;
	setAttr ".pa" -type "double3" 0 0 5.7600000490223469 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "6";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "IndexFinger4" -p "IndexFinger3";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.18176866721496676 -5.440092820663267e-015 4.3520742565306136e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "5";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "ThumbFinger1" -p "Wrist";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.17474503907180172 0.24408774766095354 -0.17232702867340244 ;
	setAttr ".r" -type "double3" -5.9834184945031068e-014 -6.0430538947806828e-013 -2.5444437451708104e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -49.671410711085876 24.76136037590382 41.92794459974413 ;
	setAttr ".pa" -type "double3" -34.462082586865911 -8.7285733235282201 -1.7903981777634761 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "4";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "ThumbFinger2" -p "ThumbFinger1";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.42902683924793683 -5.3290705182007514e-015 7.1054273576010019e-015 ;
	setAttr ".r" -type "double3" -2.6224667272000288e-013 2.0619855919002439e-012 2.4270613989580456e-012 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.1031112907291893 24.798545610081419 -0.85551312070425467 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "3";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "ThumbFinger3" -p "ThumbFinger2";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.30242331781210741 1.5099033134902129e-014 -1.1546319456101628e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -7.9513867036587903e-015 0 0 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "2";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "ThumbFinger4" -p "ThumbFinger3";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.19922366839855421 -2.6645352591003757e-015 6.2172489379008766e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "1";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "RingFinger0" -p "Wrist";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.32820744557129711 -0.1244895293188033 -0.020147886025387862 ;
	setAttr ".r" -type "double3" 3.1129678944824269e-013 -3.9635177909394105e-013 -2.8938077984628326e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.7612607765409529 10.307597975560302 -14.921471134649243 ;
	setAttr ".pa" -type "double3" -0.07133019936876682 -2.835223641928581 -1.4417652325251511 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "16";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "RingFinger1" -p "RingFinger0";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.55329836559271017 -1.1102230246251565e-015 3.5527136788005009e-015 ;
	setAttr ".r" -type "double3" -1.1032549051326499e-013 7.8440429831593987e-013 1.0774128983457588e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 10.462662093711348 28.971812240059172 7.4644844343908732 ;
	setAttr ".pa" -type "double3" -0.07133019936876682 -2.835223641928581 -1.4417652325251511 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "16";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr ".fatFrontAbs" 0.11999999731779099;
	setAttr ".fatWidthAbs" 0.11999999731779099;
createNode joint -n "RingFinger2" -p "RingFinger1";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.48775260653753882 -4.4408920985006262e-016 -7.1054273576010019e-015 ;
	setAttr ".r" -type "double3" 4.5283457878380374e-013 7.9093041448301717e-013 6.578778573939724e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.6816849383761932 18.958889905544108 -0.37672055750822614 ;
	setAttr ".pa" -type "double3" 0 0 -2.1600000310934706 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "15";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr ".fatFrontAbs" 0.11999999731779099;
	setAttr ".fatWidthAbs" 0.11999999731779099;
createNode joint -n "RingFinger3" -p "RingFinger2";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.2219675134253567 4.4408920985006262e-015 -2.6645352591003757e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -9.5416640443905503e-015 -9.5416640443905535e-015 
		-3.1805546814635176e-015 ;
	setAttr ".pa" -type "double3" 0 0 4.3200001190538568 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "14";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr ".fatFrontAbs" 0.11999999731779099;
	setAttr ".fatWidthAbs" 0.11999999731779099;
createNode joint -n "RingFinger4" -p "RingFinger3";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.18981967726341509 -6.2172489379008766e-015 -1.7763568394002505e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "13";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr ".fatFrontAbs" 0.11999999731779099;
	setAttr ".fatWidthAbs" 0.11999999731779099;
createNode joint -n "PinkyFinger0" -p "Wrist";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.12 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.31074353338034344 -0.25109579853028596 -0.041549028521423281 ;
	setAttr ".r" -type "double3" -3.7371517507195438e-014 -2.3933673978012977e-013 -4.2142349529391588e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 22.619314444288548 20.626824653609788 -22.5706204570027 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "17";
	setAttr -k on ".fat" 0.1176;
	setAttr -k on ".fatFront";
	setAttr -k on ".fatWidth";
createNode joint -n "PinkyFinger1" -p "PinkyFinger0";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.48640908542000094 -2.6645352591003757e-015 1.7763568394002505e-015 ;
	setAttr ".r" -type "double3" 3.430277920125251e-014 6.1982301759192725e-013 -9.144094709207592e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -15.008203656891821 16.382835768228276 -2.5133039179925847 ;
	setAttr ".pa" -type "double3" -0.21586850671656455 -15.856897343794616 -7.9762775885025459 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "12";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr ".fatFrontAbs" 0.11999999731779099;
	setAttr ".fatWidthAbs" 0.11999999731779099;
createNode joint -n "PinkyFinger2" -p "PinkyFinger1";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.37397156121469877 -4.4408920985006262e-016 -3.5527136788005009e-015 ;
	setAttr ".r" -type "double3" 1.0535587382349123e-014 4.4488008606970932e-013 3.1527248280007108e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 14.96577739689136 21.278172353647321 0.66057995858266161 ;
	setAttr ".pa" -type "double3" 0 0 0.71999997359174039 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "11";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr ".fatFrontAbs" 0.11999999731779099;
	setAttr ".fatWidthAbs" 0.11999999731779099;
createNode joint -n "PinkyFinger3" -p "PinkyFinger2";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.19991996421076763 4.4408920985006262e-016 -8.8817841970012523e-016 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.0734372049939372e-014 -5.3671860249696843e-015 
		1.5902773407317584e-015 ;
	setAttr ".pa" -type "double3" 0 0 5.7599997887354624 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "10";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr ".fatFrontAbs" 0.11999999731779099;
	setAttr ".fatWidthAbs" 0.11999999731779099;
createNode joint -n "PinkyFinger4" -p "PinkyFinger3";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatFront" -ln "fatFront" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatWidth" -ln "fatWidth" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatFrontAbs" -ln "fatFrontAbs" -at "double";
	addAttr -ci true -sn "fatWidthAbs" -ln "fatWidthAbs" -at "double";
	setAttr ".t" -type "double3" 0.17436493422877497 -6.2172489379008766e-015 6.8389738316909643e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "9";
	setAttr ".fatYabs" 0.11999999731779099;
	setAttr -k on ".fat" 0.11759999999999991;
	setAttr ".fatFrontAbs" 0.11999999731779099;
	setAttr ".fatWidthAbs" 0.11999999731779099;
createNode lightLinker -s -n "lightLinker1";
	setAttr -s 11 ".lnk";
	setAttr -s 11 ".slnk";
createNode displayLayerManager -n "layerManager";
	setAttr -s 2 ".dli[1]"  1;
createNode animCurveUA -n "SDK1FKIndexFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKIndexFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKIndexFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  0.98788672685623169 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0.15517689287662506 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 0.98788672685623169;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0.15517689287662506;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKRingFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKRingFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -5 -20 0 0 10 40;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -5 29.999999999999996 0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -5 14.999999999999998 0 0 10 -29.999999999999996;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -5 -20 0 0 10 40;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -5 29.999999999999996 0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -5 14.999999999999998 0 0 10 -29.999999999999996;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKCup_L_rotateX";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "cup" -ln "cup" -smn 0 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 65;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode displayLayer -n "defaultLayer";
createNode renderLayer -n "defaultRenderLayer";
	setAttr ".g" yes;
createNode renderLayerManager -n "renderLayerManager";
createNode renderLayer -n "defaultRenderLayer1";
	setAttr ".g" yes;
createNode lambert -n "asRed";
	setAttr ".c" -type "float3" 1 0 0 ;
createNode shadingEngine -n "asRedSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo1";
createNode lambert -n "asRed2";
	setAttr ".c" -type "float3" 1 0 1 ;
createNode shadingEngine -n "asRed2SG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo2";
createNode lambert -n "asGreen";
	setAttr ".c" -type "float3" 0 1 0 ;
createNode shadingEngine -n "asGreenSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo3";
createNode lambert -n "asGreen2";
	setAttr ".c" -type "float3" 1 1 0 ;
createNode shadingEngine -n "asGreen2SG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo4";
createNode lambert -n "asBlue";
	setAttr ".c" -type "float3" 0 0 1 ;
createNode shadingEngine -n "asBlueSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo5";
createNode lambert -n "asBlue2";
	setAttr ".c" -type "float3" 0 1 1 ;
createNode shadingEngine -n "asBlue2SG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo6";
createNode lambert -n "asWhite";
	setAttr ".c" -type "float3" 1 1 1 ;
createNode shadingEngine -n "asWhiteSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo7";
createNode lambert -n "asBlack";
	setAttr ".c" -type "float3" 0 0 0 ;
createNode shadingEngine -n "asBlackSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo8";
createNode lambert -n "asBones";
	setAttr ".c" -type "float3" 0.77999997 0.75999999 0.72000003 ;
createNode shadingEngine -n "asBonesSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo9";
createNode multiplyDivide -n "RootFat";
createNode multiplyDivide -n "HipFat";
createNode multiplyDivide -n "KneeFat";
createNode multiplyDivide -n "AnkleFat";
createNode multiplyDivide -n "ToesFat";
createNode multiplyDivide -n "ToesEndFat";
createNode multiplyDivide -n "FootSideOuterFat";
createNode multiplyDivide -n "FootSideInnerFat";
createNode multiplyDivide -n "HeelFat";
createNode multiplyDivide -n "ChestFat";
createNode multiplyDivide -n "ScapulaFat";
createNode multiplyDivide -n "ShoulderFat";
createNode multiplyDivide -n "ElbowFat";
createNode multiplyDivide -n "WristFat";
createNode multiplyDivide -n "CupFat";
createNode multiplyDivide -n "RingFinger1Fat";
createNode multiplyDivide -n "IndexFinger1Fat";
createNode multiplyDivide -n "IndexFinger2Fat";
createNode multiplyDivide -n "IndexFinger3Fat";
createNode multiplyDivide -n "ThumbFinger1Fat";
createNode multiplyDivide -n "ThumbFinger2Fat";
createNode multiplyDivide -n "ThumbFinger3Fat";
createNode multiplyDivide -n "MiddleFinger1Fat";
createNode multiplyDivide -n "MiddleFinger2Fat";
createNode multiplyDivide -n "MiddleFinger3Fat";
createNode multiplyDivide -n "NeckFat";
createNode multiplyDivide -n "HeadFat";
createNode multiplyDivide -n "HeadEndFat";
createNode multiplyDivide -n "Spine1Fat";
createNode multiplyDivide -n "Spine2Fat";
createNode multiplyDivide -n "Spine3Fat";
createNode multiplyDivide -n "Spine4Fat";
createNode multiplyDivide -n "ThumbFinger4Fat";
createNode multiplyDivide -n "IndexFinger4Fat";
createNode multiplyDivide -n "MiddleFinger4Fat";

connectAttr "SDK2FKIndexFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKRingFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKIndexFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKRingFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKCup_L_rotateX.drivingSystemOut" "FitSkeleton.drivingSystem" -na
		;
connectAttr "RootFat.oy" "Root.fatFrontAbs";
connectAttr "RootFat.oz" "Root.fatWidthAbs";
connectAttr "Root.s" "Hip.is";
connectAttr "HipFat.oy" "Hip.fatFrontAbs";
connectAttr "HipFat.oz" "Hip.fatWidthAbs";
connectAttr "Hip.s" "Knee.is";
connectAttr "KneeFat.oy" "Knee.fatFrontAbs";
connectAttr "KneeFat.oz" "Knee.fatWidthAbs";
connectAttr "Knee.s" "Ankle.is";
connectAttr "AnkleFat.oy" "Ankle.fatFrontAbs";
connectAttr "AnkleFat.oz" "Ankle.fatWidthAbs";
connectAttr "Ankle.s" "Heel.is";
connectAttr "HeelFat.oy" "Heel.fatFrontAbs";
connectAttr "HeelFat.oz" "Heel.fatWidthAbs";
connectAttr "Ankle.s" "Toes.is";
connectAttr "ToesFat.oy" "Toes.fatFrontAbs";
connectAttr "ToesFat.oz" "Toes.fatWidthAbs";
connectAttr "Toes.s" "FootSideInner.is";
connectAttr "FootSideInnerFat.oy" "FootSideInner.fatFrontAbs";
connectAttr "FootSideInnerFat.oz" "FootSideInner.fatWidthAbs";
connectAttr "Toes.s" "FootSideOuter.is";
connectAttr "FootSideOuterFat.oy" "FootSideOuter.fatFrontAbs";
connectAttr "FootSideOuterFat.oz" "FootSideOuter.fatWidthAbs";
connectAttr "Toes.s" "ToesEnd.is";
connectAttr "ToesEndFat.oy" "ToesEnd.fatFrontAbs";
connectAttr "ToesEndFat.oz" "ToesEnd.fatWidthAbs";
connectAttr "Root.s" "Spine1.is";
connectAttr "Spine1Fat.oy" "Spine1.fatFrontAbs";
connectAttr "Spine1Fat.oz" "Spine1.fatWidthAbs";
connectAttr "Spine1.s" "Spine2.is";
connectAttr "Spine2Fat.oy" "Spine2.fatFrontAbs";
connectAttr "Spine2Fat.oz" "Spine2.fatWidthAbs";
connectAttr "Spine2.s" "Spine3.is";
connectAttr "Spine3Fat.oy" "Spine3.fatFrontAbs";
connectAttr "Spine3Fat.oz" "Spine3.fatWidthAbs";
connectAttr "Spine3.s" "Spine4.is";
connectAttr "Spine4Fat.oy" "Spine4.fatFrontAbs";
connectAttr "Spine4Fat.oz" "Spine4.fatWidthAbs";
connectAttr "Spine4.s" "Chest.is";
connectAttr "ChestFat.oy" "Chest.fatFrontAbs";
connectAttr "ChestFat.oz" "Chest.fatWidthAbs";
connectAttr "Chest.s" "Neck.is";
connectAttr "NeckFat.oy" "Neck.fatFrontAbs";
connectAttr "NeckFat.oz" "Neck.fatWidthAbs";
connectAttr "Neck.s" "Head.is";
connectAttr "HeadFat.oy" "Head.fatFrontAbs";
connectAttr "HeadFat.oz" "Head.fatWidthAbs";
connectAttr "Head.s" "HeadEnd.is";
connectAttr "HeadEndFat.oy" "HeadEnd.fatFrontAbs";
connectAttr "HeadEndFat.oz" "HeadEnd.fatWidthAbs";
connectAttr "Chest.s" "Scapula.is";
connectAttr "ScapulaFat.oy" "Scapula.fatFrontAbs";
connectAttr "ScapulaFat.oz" "Scapula.fatWidthAbs";
connectAttr "Scapula.s" "Shoulder.is";
connectAttr "ShoulderFat.oy" "Shoulder.fatFrontAbs";
connectAttr "ShoulderFat.oz" "Shoulder.fatWidthAbs";
connectAttr "Shoulder.s" "Elbow.is";
connectAttr "ElbowFat.oy" "Elbow.fatFrontAbs";
connectAttr "ElbowFat.oz" "Elbow.fatWidthAbs";
connectAttr "Elbow.s" "Wrist.is";
connectAttr "WristFat.oy" "Wrist.fatFrontAbs";
connectAttr "WristFat.oz" "Wrist.fatWidthAbs";
connectAttr "Wrist.s" "MiddleFinger0.is";
connectAttr "MiddleFinger0.s" "MiddleFinger1.is";
connectAttr "MiddleFinger1Fat.oy" "MiddleFinger1.fatFrontAbs";
connectAttr "MiddleFinger1Fat.oz" "MiddleFinger1.fatWidthAbs";
connectAttr "MiddleFinger1.s" "MiddleFinger2.is";
connectAttr "MiddleFinger2Fat.oy" "MiddleFinger2.fatFrontAbs";
connectAttr "MiddleFinger2Fat.oz" "MiddleFinger2.fatWidthAbs";
connectAttr "MiddleFinger2.s" "MiddleFinger3.is";
connectAttr "MiddleFinger3Fat.oy" "MiddleFinger3.fatFrontAbs";
connectAttr "MiddleFinger3Fat.oz" "MiddleFinger3.fatWidthAbs";
connectAttr "MiddleFinger3.s" "MiddleFinger4.is";
connectAttr "MiddleFinger4Fat.oz" "MiddleFinger4.fatWidthAbs";
connectAttr "MiddleFinger4Fat.oy" "MiddleFinger4.fatFrontAbs";
connectAttr "Wrist.s" "IndexFinger0.is";
connectAttr "IndexFinger0.s" "IndexFinger1.is";
connectAttr "IndexFinger1Fat.oy" "IndexFinger1.fatFrontAbs";
connectAttr "IndexFinger1Fat.oz" "IndexFinger1.fatWidthAbs";
connectAttr "IndexFinger1.s" "IndexFinger2.is";
connectAttr "IndexFinger2Fat.oy" "IndexFinger2.fatFrontAbs";
connectAttr "IndexFinger2Fat.oz" "IndexFinger2.fatWidthAbs";
connectAttr "IndexFinger2.s" "IndexFinger3.is";
connectAttr "IndexFinger3Fat.oy" "IndexFinger3.fatFrontAbs";
connectAttr "IndexFinger3Fat.oz" "IndexFinger3.fatWidthAbs";
connectAttr "IndexFinger3.s" "IndexFinger4.is";
connectAttr "IndexFinger4Fat.oz" "IndexFinger4.fatWidthAbs";
connectAttr "IndexFinger4Fat.oy" "IndexFinger4.fatFrontAbs";
connectAttr "Wrist.s" "ThumbFinger1.is";
connectAttr "ThumbFinger1Fat.oy" "ThumbFinger1.fatFrontAbs";
connectAttr "ThumbFinger1Fat.oz" "ThumbFinger1.fatWidthAbs";
connectAttr "ThumbFinger1.s" "ThumbFinger2.is";
connectAttr "ThumbFinger2Fat.oy" "ThumbFinger2.fatFrontAbs";
connectAttr "ThumbFinger2Fat.oz" "ThumbFinger2.fatWidthAbs";
connectAttr "ThumbFinger2.s" "ThumbFinger3.is";
connectAttr "ThumbFinger3Fat.oy" "ThumbFinger3.fatFrontAbs";
connectAttr "ThumbFinger3Fat.oz" "ThumbFinger3.fatWidthAbs";
connectAttr "ThumbFinger3.s" "ThumbFinger4.is";
connectAttr "ThumbFinger4Fat.oz" "ThumbFinger4.fatWidthAbs";
connectAttr "ThumbFinger4Fat.oy" "ThumbFinger4.fatFrontAbs";
connectAttr "Wrist.s" "RingFinger0.is";
connectAttr "RingFinger1Fat.oy" "RingFinger0.fatFrontAbs";
connectAttr "RingFinger1Fat.oz" "RingFinger0.fatWidthAbs";
connectAttr "RingFinger0.s" "RingFinger1.is";
connectAttr "RingFinger1.s" "RingFinger2.is";
connectAttr "RingFinger2.s" "RingFinger3.is";
connectAttr "RingFinger3.s" "RingFinger4.is";
connectAttr "Wrist.s" "PinkyFinger0.is";
connectAttr "CupFat.oy" "PinkyFinger0.fatFrontAbs";
connectAttr "CupFat.oz" "PinkyFinger0.fatWidthAbs";
connectAttr "PinkyFinger0.s" "PinkyFinger1.is";
connectAttr "PinkyFinger1.s" "PinkyFinger2.is";
connectAttr "PinkyFinger2.s" "PinkyFinger3.is";
connectAttr "PinkyFinger3.s" "PinkyFinger4.is";
relationship "link" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asRedSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asRed2SG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asGreenSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asGreen2SG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asBlueSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asBlue2SG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asWhiteSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asBlackSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asBonesSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asRedSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asRed2SG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asGreenSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asGreen2SG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asBlueSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asBlue2SG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asWhiteSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asBlackSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asBonesSG.message" ":defaultLightSet.message";
connectAttr "FitSkeleton.drivingSystem_Fingers_L[6]" "SDK1FKIndexFinger3_L_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[5]" "SDK1FKIndexFinger2_L_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[4]" "SDK2FKIndexFinger1_L_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[5]" "SDK1FKIndexFinger2_R_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[6]" "SDK1FKIndexFinger3_R_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[4]" "SDK2FKIndexFinger1_R_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[8]" "SDK1FKMiddleFinger3_L_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[7]" "SDK1FKMiddleFinger2_L_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[9]" "SDK1FKMiddleFinger1_L_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[8]" "SDK1FKMiddleFinger3_R_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[9]" "SDK1FKMiddleFinger1_R_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[7]" "SDK1FKMiddleFinger2_R_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[11]" "SDK1FKRingFinger3_L_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[10]" "SDK1FKRingFinger2_L_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[12]" "SDK2FKRingFinger1_L_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[10]" "SDK1FKRingFinger2_R_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[11]" "SDK1FKRingFinger3_R_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[12]" "SDK2FKRingFinger1_R_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[14]" "SDK1FKPinkyFinger3_L_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[13]" "SDK1FKPinkyFinger2_L_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[15]" "SDK2FKPinkyFinger1_L_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[13]" "SDK1FKPinkyFinger2_R_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[15]" "SDK2FKPinkyFinger1_R_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[14]" "SDK1FKPinkyFinger3_R_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[17]" "SDK1FKThumbFinger3_L_rotateY.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[16]" "SDK1FKThumbFinger2_L_rotateY.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[17]" "SDK1FKThumbFinger3_R_rotateY.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[16]" "SDK1FKThumbFinger2_R_rotateY.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[1]" "SDK1FKIndexFinger1_R_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[0]" "SDK1FKPinkyFinger1_R_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[2]" "SDK1FKRingFinger1_R_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[1]" "SDK1FKIndexFinger1_L_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[0]" "SDK1FKPinkyFinger1_L_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[2]" "SDK1FKRingFinger1_L_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[3]" "SDK1FKCup_L_rotateX.cup";
connectAttr "layerManager.dli[0]" "defaultLayer.id";
connectAttr "renderLayerManager.rlmi[0]" "defaultRenderLayer1.rlid";
connectAttr "asRed.oc" "asRedSG.ss";
connectAttr "asRedSG.msg" "materialInfo1.sg";
connectAttr "asRed.msg" "materialInfo1.m";
connectAttr "asRed2.oc" "asRed2SG.ss";
connectAttr "asRed2SG.msg" "materialInfo2.sg";
connectAttr "asRed2.msg" "materialInfo2.m";
connectAttr "asGreen.oc" "asGreenSG.ss";
connectAttr "asGreenSG.msg" "materialInfo3.sg";
connectAttr "asGreen.msg" "materialInfo3.m";
connectAttr "asGreen2.oc" "asGreen2SG.ss";
connectAttr "asGreen2SG.msg" "materialInfo4.sg";
connectAttr "asGreen2.msg" "materialInfo4.m";
connectAttr "asBlue.oc" "asBlueSG.ss";
connectAttr "asBlueSG.msg" "materialInfo5.sg";
connectAttr "asBlue.msg" "materialInfo5.m";
connectAttr "asBlue2.oc" "asBlue2SG.ss";
connectAttr "asBlue2SG.msg" "materialInfo6.sg";
connectAttr "asBlue2.msg" "materialInfo6.m";
connectAttr "asWhite.oc" "asWhiteSG.ss";
connectAttr "asWhiteSG.msg" "materialInfo7.sg";
connectAttr "asWhite.msg" "materialInfo7.m";
connectAttr "asBlack.oc" "asBlackSG.ss";
connectAttr "asBlackSG.msg" "materialInfo8.sg";
connectAttr "asBlack.msg" "materialInfo8.m";
connectAttr "asBones.oc" "asBonesSG.ss";
connectAttr "asBonesSG.msg" "materialInfo9.sg";
connectAttr "asBones.msg" "materialInfo9.m";
connectAttr "Root.fat" "RootFat.i1y";
connectAttr "Root.fat" "RootFat.i1z";
connectAttr "Root.fatFront" "RootFat.i2y";
connectAttr "Root.fatWidth" "RootFat.i2z";
connectAttr "Hip.fat" "HipFat.i1y";
connectAttr "Hip.fat" "HipFat.i1z";
connectAttr "Hip.fatFront" "HipFat.i2y";
connectAttr "Hip.fatWidth" "HipFat.i2z";
connectAttr "Knee.fat" "KneeFat.i1y";
connectAttr "Knee.fat" "KneeFat.i1z";
connectAttr "Knee.fatFront" "KneeFat.i2y";
connectAttr "Knee.fatWidth" "KneeFat.i2z";
connectAttr "Ankle.fat" "AnkleFat.i1y";
connectAttr "Ankle.fat" "AnkleFat.i1z";
connectAttr "Ankle.fatFront" "AnkleFat.i2y";
connectAttr "Ankle.fatWidth" "AnkleFat.i2z";
connectAttr "Toes.fat" "ToesFat.i1y";
connectAttr "Toes.fat" "ToesFat.i1z";
connectAttr "Toes.fatFront" "ToesFat.i2y";
connectAttr "Toes.fatWidth" "ToesFat.i2z";
connectAttr "ToesEnd.fat" "ToesEndFat.i1y";
connectAttr "ToesEnd.fat" "ToesEndFat.i1z";
connectAttr "ToesEnd.fatFront" "ToesEndFat.i2y";
connectAttr "ToesEnd.fatWidth" "ToesEndFat.i2z";
connectAttr "FootSideOuter.fat" "FootSideOuterFat.i1y";
connectAttr "FootSideOuter.fat" "FootSideOuterFat.i1z";
connectAttr "FootSideOuter.fatFront" "FootSideOuterFat.i2y";
connectAttr "FootSideOuter.fatWidth" "FootSideOuterFat.i2z";
connectAttr "FootSideInner.fat" "FootSideInnerFat.i1y";
connectAttr "FootSideInner.fat" "FootSideInnerFat.i1z";
connectAttr "FootSideInner.fatFront" "FootSideInnerFat.i2y";
connectAttr "FootSideInner.fatWidth" "FootSideInnerFat.i2z";
connectAttr "Heel.fat" "HeelFat.i1y";
connectAttr "Heel.fat" "HeelFat.i1z";
connectAttr "Heel.fatFront" "HeelFat.i2y";
connectAttr "Heel.fatWidth" "HeelFat.i2z";
connectAttr "Chest.fat" "ChestFat.i1y";
connectAttr "Chest.fat" "ChestFat.i1z";
connectAttr "Chest.fatFront" "ChestFat.i2y";
connectAttr "Chest.fatWidth" "ChestFat.i2z";
connectAttr "Scapula.fat" "ScapulaFat.i1y";
connectAttr "Scapula.fat" "ScapulaFat.i1z";
connectAttr "Scapula.fatFront" "ScapulaFat.i2y";
connectAttr "Scapula.fatWidth" "ScapulaFat.i2z";
connectAttr "Shoulder.fat" "ShoulderFat.i1y";
connectAttr "Shoulder.fat" "ShoulderFat.i1z";
connectAttr "Shoulder.fatFront" "ShoulderFat.i2y";
connectAttr "Shoulder.fatWidth" "ShoulderFat.i2z";
connectAttr "Elbow.fat" "ElbowFat.i1y";
connectAttr "Elbow.fat" "ElbowFat.i1z";
connectAttr "Elbow.fatFront" "ElbowFat.i2y";
connectAttr "Elbow.fatWidth" "ElbowFat.i2z";
connectAttr "Wrist.fat" "WristFat.i1y";
connectAttr "Wrist.fat" "WristFat.i1z";
connectAttr "Wrist.fatFront" "WristFat.i2y";
connectAttr "Wrist.fatWidth" "WristFat.i2z";
connectAttr "PinkyFinger0.fat" "CupFat.i1y";
connectAttr "PinkyFinger0.fat" "CupFat.i1z";
connectAttr "PinkyFinger0.fatFront" "CupFat.i2y";
connectAttr "PinkyFinger0.fatWidth" "CupFat.i2z";
connectAttr "RingFinger0.fat" "RingFinger1Fat.i1y";
connectAttr "RingFinger0.fat" "RingFinger1Fat.i1z";
connectAttr "RingFinger0.fatFront" "RingFinger1Fat.i2y";
connectAttr "RingFinger0.fatWidth" "RingFinger1Fat.i2z";
connectAttr "IndexFinger1.fat" "IndexFinger1Fat.i1y";
connectAttr "IndexFinger1.fat" "IndexFinger1Fat.i1z";
connectAttr "IndexFinger1.fatFront" "IndexFinger1Fat.i2y";
connectAttr "IndexFinger1.fatWidth" "IndexFinger1Fat.i2z";
connectAttr "IndexFinger2.fat" "IndexFinger2Fat.i1y";
connectAttr "IndexFinger2.fat" "IndexFinger2Fat.i1z";
connectAttr "IndexFinger2.fatFront" "IndexFinger2Fat.i2y";
connectAttr "IndexFinger2.fatWidth" "IndexFinger2Fat.i2z";
connectAttr "IndexFinger3.fat" "IndexFinger3Fat.i1y";
connectAttr "IndexFinger3.fat" "IndexFinger3Fat.i1z";
connectAttr "IndexFinger3.fatFront" "IndexFinger3Fat.i2y";
connectAttr "IndexFinger3.fatWidth" "IndexFinger3Fat.i2z";
connectAttr "ThumbFinger1.fat" "ThumbFinger1Fat.i1y";
connectAttr "ThumbFinger1.fat" "ThumbFinger1Fat.i1z";
connectAttr "ThumbFinger1.fatFront" "ThumbFinger1Fat.i2y";
connectAttr "ThumbFinger1.fatWidth" "ThumbFinger1Fat.i2z";
connectAttr "ThumbFinger2.fat" "ThumbFinger2Fat.i1y";
connectAttr "ThumbFinger2.fat" "ThumbFinger2Fat.i1z";
connectAttr "ThumbFinger2.fatFront" "ThumbFinger2Fat.i2y";
connectAttr "ThumbFinger2.fatWidth" "ThumbFinger2Fat.i2z";
connectAttr "ThumbFinger3.fat" "ThumbFinger3Fat.i1y";
connectAttr "ThumbFinger3.fat" "ThumbFinger3Fat.i1z";
connectAttr "ThumbFinger3.fatFront" "ThumbFinger3Fat.i2y";
connectAttr "ThumbFinger3.fatWidth" "ThumbFinger3Fat.i2z";
connectAttr "MiddleFinger1.fat" "MiddleFinger1Fat.i1y";
connectAttr "MiddleFinger1.fat" "MiddleFinger1Fat.i1z";
connectAttr "MiddleFinger1.fatFront" "MiddleFinger1Fat.i2y";
connectAttr "MiddleFinger1.fatWidth" "MiddleFinger1Fat.i2z";
connectAttr "MiddleFinger2.fat" "MiddleFinger2Fat.i1y";
connectAttr "MiddleFinger2.fat" "MiddleFinger2Fat.i1z";
connectAttr "MiddleFinger2.fatFront" "MiddleFinger2Fat.i2y";
connectAttr "MiddleFinger2.fatWidth" "MiddleFinger2Fat.i2z";
connectAttr "MiddleFinger3.fat" "MiddleFinger3Fat.i1y";
connectAttr "MiddleFinger3.fat" "MiddleFinger3Fat.i1z";
connectAttr "MiddleFinger3.fatFront" "MiddleFinger3Fat.i2y";
connectAttr "MiddleFinger3.fatWidth" "MiddleFinger3Fat.i2z";
connectAttr "Neck.fat" "NeckFat.i1y";
connectAttr "Neck.fat" "NeckFat.i1z";
connectAttr "Neck.fatFront" "NeckFat.i2y";
connectAttr "Neck.fatWidth" "NeckFat.i2z";
connectAttr "Head.fat" "HeadFat.i1y";
connectAttr "Head.fat" "HeadFat.i1z";
connectAttr "Head.fatFront" "HeadFat.i2y";
connectAttr "Head.fatWidth" "HeadFat.i2z";
connectAttr "HeadEnd.fat" "HeadEndFat.i1y";
connectAttr "HeadEnd.fat" "HeadEndFat.i1z";
connectAttr "HeadEnd.fatFront" "HeadEndFat.i2y";
connectAttr "HeadEnd.fatWidth" "HeadEndFat.i2z";
connectAttr "Spine1.fat" "Spine1Fat.i1y";
connectAttr "Spine1.fat" "Spine1Fat.i1z";
connectAttr "Spine1.fatFront" "Spine1Fat.i2y";
connectAttr "Spine1.fatWidth" "Spine1Fat.i2z";
connectAttr "Spine2.fat" "Spine2Fat.i1y";
connectAttr "Spine2.fat" "Spine2Fat.i1z";
connectAttr "Spine2.fatFront" "Spine2Fat.i2y";
connectAttr "Spine2.fatWidth" "Spine2Fat.i2z";
connectAttr "Spine3.fat" "Spine3Fat.i1y";
connectAttr "Spine3.fat" "Spine3Fat.i1z";
connectAttr "Spine3.fatFront" "Spine3Fat.i2y";
connectAttr "Spine3.fatWidth" "Spine3Fat.i2z";
connectAttr "Spine4.fat" "Spine4Fat.i1y";
connectAttr "Spine4.fat" "Spine4Fat.i1z";
connectAttr "Spine4.fatFront" "Spine4Fat.i2y";
connectAttr "Spine4.fatWidth" "Spine4Fat.i2z";
connectAttr "ThumbFinger4.fat" "ThumbFinger4Fat.i1y";
connectAttr "ThumbFinger4.fat" "ThumbFinger4Fat.i1z";
connectAttr "ThumbFinger4.fatFront" "ThumbFinger4Fat.i2y";
connectAttr "ThumbFinger4.fatWidth" "ThumbFinger4Fat.i2z";
connectAttr "IndexFinger4.fat" "IndexFinger4Fat.i1y";
connectAttr "IndexFinger4.fat" "IndexFinger4Fat.i1z";
connectAttr "IndexFinger4.fatFront" "IndexFinger4Fat.i2y";
connectAttr "IndexFinger4.fatWidth" "IndexFinger4Fat.i2z";
connectAttr "MiddleFinger4.fat" "MiddleFinger4Fat.i1y";
connectAttr "MiddleFinger4.fat" "MiddleFinger4Fat.i1z";
connectAttr "MiddleFinger4.fatFront" "MiddleFinger4Fat.i2y";
connectAttr "MiddleFinger4.fatWidth" "MiddleFinger4Fat.i2z";
connectAttr "asRedSG.pa" ":renderPartition.st" -na;
connectAttr "asRed2SG.pa" ":renderPartition.st" -na;
connectAttr "asGreenSG.pa" ":renderPartition.st" -na;
connectAttr "asGreen2SG.pa" ":renderPartition.st" -na;
connectAttr "asBlueSG.pa" ":renderPartition.st" -na;
connectAttr "asBlue2SG.pa" ":renderPartition.st" -na;
connectAttr "asWhiteSG.pa" ":renderPartition.st" -na;
connectAttr "asBlackSG.pa" ":renderPartition.st" -na;
connectAttr "asBonesSG.pa" ":renderPartition.st" -na;
connectAttr "asRed.msg" ":defaultShaderList1.s" -na;
connectAttr "asRed2.msg" ":defaultShaderList1.s" -na;
connectAttr "asGreen.msg" ":defaultShaderList1.s" -na;
connectAttr "asGreen2.msg" ":defaultShaderList1.s" -na;
connectAttr "asBlue.msg" ":defaultShaderList1.s" -na;
connectAttr "asBlue2.msg" ":defaultShaderList1.s" -na;
connectAttr "asWhite.msg" ":defaultShaderList1.s" -na;
connectAttr "asBlack.msg" ":defaultShaderList1.s" -na;
connectAttr "asBones.msg" ":defaultShaderList1.s" -na;

// End of MetaHuman.ma
