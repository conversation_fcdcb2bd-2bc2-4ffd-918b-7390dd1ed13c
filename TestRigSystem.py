"""
Test script for the Comprehensive Rig Tool
This script tests the rigging system functionality
"""

import maya.cmds as mc

def test_rig_utilities():
    """Test the RigUtilities class functions"""
    print("Testing RigUtilities...")
    
    try:
        from ComprehensiveRigTool import RigUtilities
        
        # Test controller creation
        test_ctrl = RigUtilities.create_controller_shape("test_ctrl", "circle", 2.0)
        if mc.objExists(test_ctrl):
            print("✓ Controller creation successful")
            mc.delete(test_ctrl)
        else:
            print("✗ Controller creation failed")
        
        # Test joint validation with no joints
        is_valid, message = RigUtilities.validate_joint_chain([])
        if not is_valid:
            print("✓ Empty joint validation working")
        else:
            print("✗ Empty joint validation failed")
        
        print("RigUtilities tests completed")
        
    except Exception as e:
        print(f"✗ RigUtilities test failed: {str(e)}")

def test_basic_rig_creation():
    """Test basic rig creation with simple joints"""
    print("\nTesting basic rig creation...")
    
    try:
        # Clear scene
        mc.file(new=True, force=True)
        
        # Create simple test joints
        mc.select(clear=True)
        joint1 = mc.joint(p=(0, 0, 0), n="test_shoulder_jnt")
        joint2 = mc.joint(p=(5, 0, 0), n="test_elbow_jnt") 
        joint3 = mc.joint(p=(10, 0, 0), n="test_wrist_jnt")
        
        test_joints = [joint1, joint2, joint3]
        
        # Test the rigging system
        from ComprehensiveRigTool import LimbRigger
        
        rigger = LimbRigger("test_arm", test_joints, "arm")
        success = rigger.create_fk_ik_system()
        
        if success:
            print("✓ Basic rig creation successful")
            
            # Check if expected objects were created
            expected_objects = [
                "test_arm_ikfkBlend_ctrl",
                "test_shoulder_jnt_drv_fk",
                "test_elbow_jnt_drv_fk", 
                "test_wrist_jnt_drv_fk"
            ]
            
            missing_objects = []
            for obj in expected_objects:
                if not mc.objExists(obj):
                    missing_objects.append(obj)
            
            if not missing_objects:
                print("✓ All expected rig objects created")
            else:
                print(f"✗ Missing objects: {missing_objects}")
        else:
            print("✗ Basic rig creation failed")
        
    except Exception as e:
        print(f"✗ Basic rig test failed: {str(e)}")

def test_ui_creation():
    """Test UI creation"""
    print("\nTesting UI creation...")
    
    try:
        from ComprehensiveRigTool import ComprehensiveRigUI
        
        ui = ComprehensiveRigUI()
        ui.create_ui()
        
        if mc.window(ui.window_id, q=True, exists=True):
            print("✓ UI creation successful")
            mc.deleteUI(ui.window_id)
        else:
            print("✗ UI creation failed")
            
    except Exception as e:
        print(f"✗ UI test failed: {str(e)}")

def test_launcher():
    """Test the launcher functionality"""
    print("\nTesting launcher...")
    
    try:
        import RigToolLauncher
        
        # Test launcher UI creation
        RigToolLauncher.create_launcher_ui()
        
        if mc.window("RigToolLauncher", q=True, exists=True):
            print("✓ Launcher UI creation successful")
            mc.deleteUI("RigToolLauncher")
        else:
            print("✗ Launcher UI creation failed")
            
    except Exception as e:
        print(f"✗ Launcher test failed: {str(e)}")

def run_all_tests():
    """Run all tests"""
    print("=" * 50)
    print("COMPREHENSIVE RIG TOOL - TEST SUITE")
    print("=" * 50)
    
    test_rig_utilities()
    test_basic_rig_creation()
    test_ui_creation()
    test_launcher()
    
    print("\n" + "=" * 50)
    print("TEST SUITE COMPLETED")
    print("=" * 50)
    
    # Show results dialog
    mc.confirmDialog(
        title="Test Results",
        message="Test suite completed. Check the Script Editor for detailed results.",
        button="OK"
    )

def quick_demo():
    """Create a quick demo of the rig system"""
    print("\nCreating quick demo...")
    
    try:
        # Clear scene
        mc.file(new=True, force=True)
        
        # Create test skeleton using launcher
        import RigToolLauncher
        RigToolLauncher.create_test_skeleton()
        
        # Launch the rig tool
        RigToolLauncher.launch_rig_tool()
        
        print("✓ Demo setup complete!")
        print("Now you can:")
        print("1. Select joints for each limb")
        print("2. Click 'Set' buttons to store selections")
        print("3. Click 'BUILD COMPLETE RIG' to create the rig")
        
    except Exception as e:
        print(f"✗ Demo setup failed: {str(e)}")

# Main execution
if __name__ == "__main__":
    # Ask user what they want to do
    result = mc.confirmDialog(
        title="Rig Tool Test",
        message="What would you like to do?",
        button=["Run Tests", "Quick Demo", "Cancel"],
        defaultButton="Quick Demo"
    )
    
    if result == "Run Tests":
        run_all_tests()
    elif result == "Quick Demo":
        quick_demo()
    else:
        print("Test cancelled by user")
