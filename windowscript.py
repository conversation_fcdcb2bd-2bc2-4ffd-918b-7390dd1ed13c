import maya.cmds as mc

##############################################
#           Methods                          #
##############################################

# create the fuction ChangeSelectedCrvWidth
def ChangeSelectedCrvWidth():

#get all the things selected
    selection = mc.ls(sl=True)
    lineWidth = mc.floatSliderGrp(LineWidthFSG, q=True, v=True)
    #loop through all the curves in the selection:
    for crv in selection:
        #find the shape of the curve
        shape = mc.listRelatives(crv, s=True)[0]
        #set the line width to 2
        mc.setAttr(shape+".lineWidth", lineWidth)

##############################################
#                 UI                         #
##############################################

windowID = "changeLineLenght1262023"

if mc.window(windowID, q=True, exists=True):
   mc.deleteUI(windowID)
    
mc.window(windowID, title = "Change Line Width")
mc.showWindow(windowID)
mc.columnLayout()
mc.text("please select your curves:")
LineWidthFSG = mc.floatSliderGrp(label="width", field=True)
mc.button(label="set",width=400, command="ChangeSelectedCrvWidth()")