/* XPM */
static char *biped_64_99_OffK0[] = {
/* columns rows colors chars-per-pixel */
"10 13 52 1",
"  c #ABAAAB",
". c #B9B8B8",
"X c gray75",
"o c #C8C7C7",
"O c gray79",
"+ c #D4D5D5",
"@ c #D5D5D5",
"# c #DAD9DA",
"$ c #DCDDDC",
"% c gray87",
"& c #E3E3E2",
"* c #E5E7E5",
"= c #E6E4E5",
"- c gray92",
"; c #EDEEEE",
": c gray94",
"> c #F1F1F1",
", c #F3F3F2",
"< c #F5F6F6",
"1 c #F8F9F8",
"2 c #F8F9F9",
"3 c #FAF9F9",
"4 c #FBFBFA",
"5 c #FCFDFC",
"6 c #FDFCFC",
"7 c #FDFCFD",
"8 c #FDFDFC",
"9 c #FDFDFD",
"0 c #FDFDFE",
"q c #FDFEFC",
"w c #FDFEFD",
"e c #FDFFFD",
"r c #FDFEFE",
"t c #FDFFFE",
"y c #FDFFFF",
"u c #FEFCFD",
"i c #FEFDFD",
"p c #FFFCFC",
"a c #FFFDFD",
"s c #FEFCFE",
"d c #FEFDFE",
"f c #FEFDFF",
"g c #FFFCFE",
"h c #FFFDFE",
"j c #FEFEFC",
"k c #FEFEFD",
"l c #FEFFFD",
"z c #FFFEFC",
"x c #FFFEFD",
"c c #FFFFFD",
"v c #FEFFFF",
"b c #FFFEFE",
/* pixels */
"XXXXXXXXXX",
"XO,555555X",
"X+1555555X",
"X$555555gX",
"X&555555<X",
"X;555555;X",
"X,555555*X",
"X3555555%X",
"Xg5g5551@X",
"X555555,OX",
"X555555=.X",
"X555555$ X",
"XXXXXXXXXX"
};
