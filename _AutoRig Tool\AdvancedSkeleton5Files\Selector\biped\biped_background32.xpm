/* XPM */
static char *biped_background32[] = {
/* columns rows colors chars-per-pixel */
"32 32 110 2",
"   c #1208EF",
".  c #3F3DD8",
"X  c #5B45BF",
"o  c #B97B33",
"O  c #BC6F6F",
"+  c red",
"@  c #E72828",
"#  c #E42C2C",
"$  c #CF4F4F",
"%  c #CD5252",
"&  c #CC5555",
"*  c #C85B5B",
"=  c #C95A5A",
"-  c #8E7FA3",
";  c #B68042",
":  c #8E8E8E",
">  c #939393",
",  c #939493",
"<  c gray58",
"1  c #959494",
"2  c #959595",
"3  c #989898",
"4  c gray60",
"5  c #999A99",
"6  c #999A9A",
"7  c #9A9A9A",
"8  c #9A9B9B",
"9  c #9B9B9B",
"0  c gray61",
"q  c #9D9D9D",
"w  c #9F9F9F",
"e  c #9692A4",
"r  c #84ABB8",
"t  c #89AAB4",
"y  c #9EA0A1",
"u  c #AF8787",
"i  c #AC8A8A",
"p  c #A59595",
"a  c #A49797",
"s  c #A39999",
"d  c #A19D9D",
"f  c #A49898",
"g  c #A0A0A0",
"h  c #A1A1A2",
"j  c gray64",
"k  c #A4A3A3",
"l  c #A5A5A5",
"z  c gray65",
"x  c #A7A7A7",
"c  c gray66",
"v  c #AAAAAA",
"b  c #AAABAA",
"n  c #ACACAC",
"m  c #B7B7B7",
"M  c #B7B7B8",
"N  c #BBBBBA",
"B  c #BCBCBC",
"V  c #BCBDBD",
"C  c #C2C1C2",
"Z  c gray76",
"A  c #C3C3C3",
"S  c #C4C3C3",
"D  c gray79",
"F  c #D6D6D5",
"G  c gray84",
"H  c #D7D7D7",
"J  c #D8D8D7",
"K  c #DAD9D9",
"L  c #DADAD9",
"P  c #DADADA",
"I  c #DDDDDD",
"U  c #DFDFDF",
"Y  c #DFDFE0",
"T  c #E0E0E1",
"R  c #E1E0E0",
"E  c #E4E3E3",
"W  c #E4E4E4",
"Q  c #E9E9E9",
"!  c #F3F3F3",
"~  c #F3F4F4",
"^  c gray97",
"/  c #F8F7F7",
"(  c #F8F8F8",
")  c #FAF9FA",
"_  c gray98",
"`  c #FBFAFA",
"'  c #FBFAFB",
"]  c #FBFAFC",
"[  c #FCFBFC",
"{  c gray99",
"}  c #FDFDFD",
"|  c #FDFEFE",
" . c #FDFEFF",
".. c #FDFFFE",
"X. c #FEFDFD",
"o. c #FEFDFE",
"O. c #FEFDFF",
"+. c #FFFDFE",
"@. c #FFFDFF",
"#. c #FEFEFD",
"$. c #FEFFFD",
"%. c #FFFFFD",
"&. c #FEFEFE",
"*. c #FEFEFF",
"=. c #FEFFFE",
"-. c #FEFFFF",
";. c #FFFEFE",
":. c #FFFEFF",
">. c #FFFFFE",
",. c gray100",
/* pixels */
"w w w w w w w w w w w w w w w 4 4 g g w w w w w w w w w w w w w ",
"w w w w w @ f w w O w w w w w =.=.4 w w w w O w w s @ w w w w w ",
"w w w w w + + w w + w w . w L =.=.K w . w w + e w + + w w w w w ",
"w w w w w + % w w + w w X w z =.=.z w X y j + y w % + w w w w w ",
"w w w w w w w w g d w w w w w =.=.w w w w w y y w w w w w w w w ",
"w w w w w 2 w w ; g o ; w w w I I w w w o o y o w w 2 w w w w w ",
"w w w w 1 ` =.w e d 3 7 h D =.@.=.=.D k e 4 0 w w =.{ 2 w w w w ",
"w w w w : ) =.=.=.=.@.=.=.=.@.=.=.=.=.=.=.=.=.=.=.=.{ : w w w w ",
"w w w w 9 g w w w w w w 4 B =.=.=.=.V 4 w w w w w w w 9 d w w w ",
"w w w w w w w w w w w w w w =.=.=.=.w s w w w w w w w w w w w w ",
"w w w w w w g k w w w w w w ` =.@.@.w + i w w w w w w w w w w w ",
"w w w F g %.d e w w w w w d =.=.=.=.w d w w w w w w =.w F w w w ",
"w w w ^ w =.w C w w w w w w =.=.=.=.w # w e w w C w =.w ) w w w ",
"w w 1 x w =.> 3 w w w w r w U =.@.I w + p - w w 4 > =.w z 2 w w ",
"w w =.1 > =.E w w w w w w w m =.=.M w w w w w w w W =.> 2 =.w w ",
"w w Q w K =.=.w w w w w w w ^ =.=.^ w + w w w w w =.=.K w Q w w ",
"w w 5 h =.=.=.d w w w t w w @.=.=.@.w + w w w w w @.@.@.h 3 w w ",
"w g d =.=.=.=.9 =.w w w w w =.@.=.=.w w w w w =.4 =.=.@.=.w w w ",
"w w g =.=.=.=.=.w w w w w w @.=.=.=.w w w w w w =.@.=.=.=.w w w ",
"w w e =.=.@.=.=.w w w w w w =.@.=.=.w w w w w w =.@.@.=.=.w w w ",
"w w w =.=.@.=.=.w w w   w w @.=.=.@.w w   w w w @.=.=.@.=.w w w ",
"w w w =.=.=.=.e w w w w w w =.@.@.=.w w w w w w w =.=.=.=.w w w ",
"w w w C =.=.M w w w w w w w @.=.=.@.w w w w w w w B =.=.C w w w ",
"w w w v T =.w w w w w $ w w =.=.@.=.w w $ w w w w w =.T v w w w ",
"w w w w w w w w w w w $ w w =.=.=.=.w w $ w w w w w w w w w w w ",
"w w w w w w w w w w w w w w =.=.=.=.w w w w w w w w w w w w w w ",
"w w w w w w w w w w w + w w =.=.=.=.w w + w w w w w w w w w w w ",
"w w w w w w w w w w * + f w =.=.=.=.w f + * w w w w w w w w w w ",
"w w w w w w w w w w w + w w ) ! ! @.w w + w w w w g g d w w w w ",
"w w w w w w w w w w w w w w n K H n w w w w w w w d g g w w w w ",
"w w w w w g & u w d d g w =.=.=.=.=.@.w w w d w u & e g w w w w ",
"w w w w w w w w w g g k e 4 4 4 4 4 3 w w w w w w w w w w w w w "
};
