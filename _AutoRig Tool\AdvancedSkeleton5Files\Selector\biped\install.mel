string $currentShelf = `tabLayout -query -selectTab $gShelfTopLevel`;
setParent $currentShelf;
string $asInstallScriptLocation=`asInstallScriptLocation`;
string $scriptName="biped";
string $sourceFile=$asInstallScriptLocation+"../"+$scriptName+".mel";
string $command="source \""+$sourceFile+"\"";
string $icon=$asInstallScriptLocation+"biped_background32.png";
if (!`file -q -ex $sourceFile`)
	error ("Something went wrong, can not find: \""+$sourceFile+"\"");
shelfButton
	-command $command
	-annotation $scriptName
	-label $scriptName
	-image $icon
	-image1 $icon
	-sourceType "mel"
;
print ("\n// "+$scriptName+" has been added to current shelf.\n");

global proc asInstallScriptLocator (){}

global proc string asInstallScriptLocation ()
{
string $whatIs=`whatIs asInstallScriptLocator`;
string $fullPath=`substring $whatIs 25 999`;
string $buffer[];
int $numTok=`tokenize $fullPath "/" $buffer`;
int $numLetters=size($fullPath);
int $numLettersLastFolder=size($buffer[$numTok-1]);
string $scriptLocation=`substring $fullPath 1 ($numLetters-$numLettersLastFolder)`;
return $scriptLocation;
}