/* XPM */
static char *biped_162_219_OffK0[] = {
/* columns rows colors chars-per-pixel */
"19 19 117 2",
"   c #8B8B8B",
".  c gray55",
"X  c #8C8C8D",
"o  c #8C8D8C",
"O  c #8C8D8D",
"+  c #8D8C8C",
"@  c #8D8C8D",
"#  c #8D8D8C",
"$  c #939393",
"%  c gray58",
"&  c gray59",
"*  c #979797",
"=  c #989898",
"-  c #989999",
";  c #9B9B9B",
":  c #9D9D9D",
">  c gray62",
",  c #A4A4A4",
"<  c #A5A5A5",
"1  c #A5A6A6",
"2  c gray65",
"3  c #ACACAC",
"4  c #AFAFAF",
"5  c #B2B2B2",
"6  c #B3B2B2",
"7  c #B7B7B7",
"8  c #<PERSON>BC<PERSON>",
"9  c gray75",
"0  c #C6C6C6",
"q  c gray80",
"w  c #D2D2D2",
"e  c #D6D5D5",
"r  c #D6D5D6",
"t  c #D6D6D5",
"y  c #D7D6D5",
"u  c gray84",
"i  c #D6D6D7",
"p  c #D6D7D6",
"a  c #D8D9D8",
"s  c #D8D9D9",
"d  c gray86",
"f  c #DCDDDC",
"g  c #DDDDDE",
"h  c gray87",
"j  c #E9E9E9",
"k  c #EAEAEA",
"l  c #EBECEC",
"z  c #ECECEC",
"x  c #EEEFEF",
"c  c #EFEEEE",
"v  c #F0EFEF",
"b  c #F1F0F0",
"n  c #F3F4F5",
"m  c #F4F5F5",
"M  c #F7F6F6",
"N  c #F7F8F6",
"B  c #F7F8F8",
"V  c #F8F9F8",
"C  c #F8F9F9",
"Z  c #F9F8F8",
"A  c #F9F9F8",
"S  c #F9F8FA",
"D  c #F8FAF9",
"F  c #F9FAF9",
"G  c #F9FAFA",
"H  c #FAF8F9",
"J  c #FAF9F8",
"K  c #FAF9F9",
"L  c #FAFAF8",
"P  c #FAFAF9",
"I  c gray98",
"U  c #FAFBFB",
"Y  c #FBFBFC",
"T  c #FCFDFB",
"R  c #FCFDFD",
"E  c #FDFCFD",
"W  c #FDFDFC",
"Q  c #FDFDFD",
"!  c #FCFDFE",
"~  c #FDFDFE",
"^  c #FCFEFC",
"/  c #FCFEFD",
"(  c #FCFFFD",
")  c #FDFEFD",
"_  c #FDFFFC",
"`  c #FDFFFD",
"'  c #FCFEFE",
"]  c #FCFFFE",
"[  c #FDFEFE",
"{  c #FDFEFF",
"}  c #FDFFFE",
"|  c #FDFFFF",
" . c #FEFDFC",
".. c #FEFDFD",
"X. c #FFFCFD",
"o. c #FFFDFC",
"O. c #FFFDFD",
"+. c #FEFDFE",
"@. c #FEFDFF",
"#. c #FFFCFF",
"$. c #FFFDFE",
"%. c #FFFDFF",
"&. c #FEFEFC",
"*. c #FEFEFD",
"=. c #FEFFFD",
"-. c #FFFEFC",
";. c #FFFEFD",
":. c #FFFFFC",
">. c #FFFFFD",
",. c #FEFEFE",
"<. c #FEFEFF",
"1. c #FEFFFE",
"2. c #FEFFFF",
"3. c #FFFEFE",
"4. c #FFFEFF",
"5. c #FFFFFE",
"6. c gray100",
/* pixels */
"9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 ",
"9 3 k O.O.( ( O.O.O.( O.f 1 $ > > > 9 ",
"9 5 l O.O.O.( ( O.( O.( s 1 $ > > > 9 ",
"9 8 x ( ( O.O.O.O.( ( O.g 1 $ > > > 9 ",
"9 q n O.O.( O.O.O.O.( ( c 7 & - > > 9 ",
"9 g B O.O.O.( O.( O.O.O.B s 4 * * : 9 ",
"9 k ( ( ( O.O.O.O.O.O.O.( B d 5 ; - 9 ",
"9 c O.( O.O.O.( O.O.( O.O.O.G k 0 1 9 ",
"9 x ( ( ( O.O.O.O.O.O.O.O.( O.O.B M 9 ",
"9 b O.O.O.O.( O.O.O.O.O.( ( ( ( O.O.9 ",
"9 M O.O.O.( ( ( ( ( ( ( O.( O.O.O.O.9 ",
"9 ! O.O.O.O.O.( O.( ( ( ( ( O.( O.! 9 ",
"9 ! ! O.O.O.O.( O.O.O.O.( ! ! O.O.O.9 ",
"9 (  .! O.O.O.O.O.O.O.O.! ! O.O.O.! 9 ",
"9 O.O.O.O.O.O.O.( O.O.O.O.O.( ( O.( 9 ",
"9 M M G G T B B B B S B G B B G  .G 9 ",
"9 w p p p p i p p p p p r p e e e e 9 ",
"9     o o o . . . . X X   X   X X X 9 ",
"9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 "
};
