/* XPM */
static char *biped_240_146_OnK0[] = {
/* columns rows colors chars-per-pixel */
"11 20 43 1",
"  c #6ED244",
". c #6ED344",
"X c #6FD444",
"o c #6FD544",
"O c #6FD644",
"+ c #70D744",
"@ c #70D844",
"# c #70D944",
"$ c #70DA44",
"% c #71DB44",
"& c #71DD44",
"* c #72DE44",
"= c #72DF44",
"- c #72E044",
"; c #72E144",
": c #73E244",
"> c #73E344",
", c #74E544",
"< c #75E944",
"1 c #75EB44",
"2 c #76EB44",
"3 c #77EF44",
"4 c #78F344",
"5 c #79F744",
"6 c #7BFC44",
"7 c #7BFE44",
"8 c #7CFF44",
"9 c #7DFF44",
"0 c #7EFF44",
"q c #80FF44",
"w c #82FF44",
"e c #83FF44",
"r c #85FF44",
"t c #86FF44",
"y c #87FF44",
"u c #88FF44",
"i c #89FF44",
"p c #8AFF44",
"a c #8CFF44",
"s c #8DFF44",
"d c #8EFF44",
"f c #8FFF44",
"g c gray75",
/* pixels */
"ggggggggggg",
"g$qdffffffg",
"gX0dffffffg",
"g 7iffffffg",
"g 5idfffffg",
"g 4tffffffg",
"gX3tffffffg",
"g$1qffffffg",
"g*:0dfffffg",
"g:X,qffdffg",
"g:*$>iddffg",
"g::*+1idffg",
"g>>:* 0iffg",
"g:>>>$*0dfg",
"g>>>::+1qdg",
"g>>>::*$4tg",
"g>>>>>>$*7g",
"g>>>>>>:#:g",
"g>>>>>>>:$g",
"ggggggggggg"
};
