# Comprehensive Maya Rigging Tool

A complete Maya rigging system inspired by the 4SchoolV1 rigging approach, featuring FK/IK switching capabilities for arms and legs, plus comprehensive body part controls.

## Features

### Core Rigging Components
- **FK/IK Switching**: Seamless switching between FK and IK for arms and legs
- **Limb Rigging**: Complete arm and leg rigs with proper controllers
- **Spine System**: FK spine controls for body animation
- **Body Parts**: Hip, chest, and clavicle controls
- **Master Control**: Overall rig control and organization

### Controller Types
- **FK Controllers**: Circle-shaped controllers for FK animation
- **IK Controllers**: Arrow-shaped controllers for IK end effectors
- **Pole Vector Controls**: Cube-shaped controllers for IK pole vectors
- **Switch Controls**: Square-shaped controllers with ikfkBlend attribute
- **Master Control**: Large square controller for overall rig control

## Files

- `ComprehensiveRigTool.py` - Main rigging system with classes and UI
- `RigToolLauncher.py` - Launcher script with utilities and test skeleton
- `README_RigTool.md` - This documentation file

## Installation

1. Copy the Python files to your Maya scripts directory
2. In Maya, run the following Python code:

```python
import RigToolLauncher
RigToolLauncher.create_launcher_ui()
```

## Quick Start

### Method 1: Using Test Skeleton
1. Run the launcher UI
2. Click "Create Test Skeleton" to generate a test character
3. Click "Launch Rig Tool" to open the main rigging interface
4. Follow the on-screen instructions to select joints and build the rig

### Method 2: Using Your Own Skeleton
1. Load your character skeleton in Maya
2. Run `RigToolLauncher.create_launcher_ui()`
3. Click "Launch Rig Tool"
4. Select appropriate joints for each body part
5. Click "Set" to store selections
6. Click "BUILD COMPLETE RIG"

## Detailed Workflow

### 1. Joint Selection
For each body part category, select the appropriate joints:

- **Left/Right Arms**: Select from shoulder to wrist (minimum 2 joints)
- **Left/Right Legs**: Select from hip to ankle (minimum 2 joints)  
- **Spine**: Select spine joints from pelvis to chest
- **Hips**: Select hip/pelvis joints
- **Chest**: Select chest/upper torso joints
- **Left/Right Clavicles**: Select clavicle joints

### 2. Building the Rig
1. Use "Validate Selections" to check your joint selections
2. Click "BUILD COMPLETE RIG" to create the complete rigging system
3. The tool will create all controllers, constraints, and switching mechanisms

### 3. Using FK/IK Switching
- Locate the switch controllers (square shapes near limb ends)
- Use the `ikfkBlend` attribute:
  - `0.0` = Full FK mode
  - `1.0` = Full IK mode
  - Intermediate values for blending
- Animate this attribute for smooth FK/IK transitions

## Technical Details

### Naming Conventions
The tool follows consistent naming patterns:
- Original joints: `*_jnt`
- FK joints: `*_jnt_drv_fk`
- IK joints: `*_jnt_drv_ik`
- FK controllers: `*_ac_fk_*`
- IK controllers: `*_ik_ctrl`
- Switch controllers: `*_ikfkBlend_ctrl`

### Rig Structure
```
RIG_GRP
├── master_ctrl
├── left_arm_ikfkBlend_ctrl
├── right_arm_ikfkBlend_ctrl
├── left_leg_ikfkBlend_ctrl
├── right_leg_ikfkBlend_ctrl
└── [other controllers and groups]
```

### FK/IK Implementation
- Uses Maya's `blendColors` nodes for smooth switching
- IK handles use `ikRPsolver` for realistic limb behavior
- Pole vector constraints for proper IK control
- All switching is keyable and animatable

## Troubleshooting

### Common Issues
1. **"Need at least 2 joints" error**: Ensure you select enough joints for each limb
2. **"Joint does not exist" error**: Check that selected objects are valid joints
3. **Build fails**: Use "Validate Selections" to identify missing selections

### Best Practices
- Use consistent joint naming conventions
- Select joints in hierarchical order (root to tip)
- Test the rig on a simple skeleton first
- Save your scene before building the rig

## Customization

The tool is designed to be extensible. Key areas for customization:

- **Controller Shapes**: Modify `RigUtilities.create_controller_shape()`
- **Naming Conventions**: Update naming patterns in respective classes
- **Additional Body Parts**: Extend the UI and rigging classes
- **Custom Constraints**: Add specialized constraint types

## Support

For issues or questions:
1. Check the Maya Script Editor for detailed error messages
2. Use the "Show Help" button in the launcher for quick reference
3. Validate your joint selections before building
4. Test with the provided test skeleton first

## Version History

- **v1.0**: Initial release with complete FK/IK rigging system
- Based on 4SchoolV1 rigging methodology
- Comprehensive UI with validation and error handling
