/* XPM */
static char *biped_162_141_OnK1[] = {
/* columns rows colors chars-per-pixel */
"20 41 11 1",
"  c #FFF066",
". c #FFF166",
"X c #FFF266",
"o c #FFF366",
"O c #FFF466",
"+ c #FFF566",
"@ c #FFF666",
"# c #FFF966",
"$ c #FFFD66",
"% c #FFFF66",
"& c gray75",
/* pixels */
"&&&&&&&&&&&&&&&&&&&&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%%&",
"&%%%%%%%%%%%%%%%%%#&",
"&%%%%%%%%%%%%%%%%%+&",
"&%%%%%%%%%%%%%%%%%.&",
"&%%%%%%%%%%%%%%%%%.&",
"&%%%%%%%%%%%%%%%%%.&",
"&%%%%%%%%%%%%%%%%%+&",
"&%%%%%%%%%%%%%%%%%+&",
"&%%%%%%%%%%%%%%%%%.&",
"&%%%%%%%%%%%%%%%%%.&",
"&%%%%%%%%%%%%%%%%%.&",
"&%%%%%%%%%%%%%%%%%.&",
"&%%%%%%%%%%%%%%%%%.&",
"&%%%%%%%%%%%%%%%%%+&",
"&%%%%%%%%%%%%%%%%%#&",
"&&&&&&&&&&&&&&&&&&&&"
};
