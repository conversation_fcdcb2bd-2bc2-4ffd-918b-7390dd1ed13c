//Maya ASCII 2012 scene
//Name: headTopologyEyeBall.ma
//Last modified: Wed, Jun 21, 2017 05:32:34 PM
//Codeset: 1252
requires maya "2012";
requires "stereoCamera" "10.0";
currentUnit -l centimeter -a degree -t pal;
fileInfo "application" "maya";
fileInfo "product" "Maya 2012";
fileInfo "version" "2012 x64";
fileInfo "cutIdentifier" "201201172029-821146";
fileInfo "osv" "Microsoft Business Edition, 64-bit  (Build 9200)\n";
createNode transform -s -n "persp";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 0.79381505788543727 0.87001595247577057 1.5737467644105467 ;
	setAttr ".r" -type "double3" -26.138352574666989 26.599999999999831 8.8926343756588581e-016 ;
createNode camera -s -n "perspShape" -p "persp";
	setAttr -k off ".v" no;
	setAttr ".fl" 34.999999999999986;
	setAttr ".ncp" 0.001;
	setAttr ".coi" 1.9747804907050808;
	setAttr ".imn" -type "string" "persp";
	setAttr ".den" -type "string" "persp_depth";
	setAttr ".man" -type "string" "persp_mask";
	setAttr ".tp" -type "double3" 1.6376376152038574e-005 4.57763671875e-005 -0.011432573199272156 ;
	setAttr ".hc" -type "string" "viewSet -p %camera";
createNode transform -s -n "top";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 3.7252902984619141e-009 1000.1014840899691 -1.4900939126708737e-008 ;
	setAttr ".r" -type "double3" -89.999999999999986 0 0 ;
createNode camera -s -n "topShape" -p "top";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 999.708760133434;
	setAttr ".ow" 2.8104493081727129;
	setAttr ".imn" -type "string" "top";
	setAttr ".den" -type "string" "top_depth";
	setAttr ".man" -type "string" "top_mask";
	setAttr ".tp" -type "double3" 3.5017728805541992e-006 0.39124151319265366 0.057378515601158142 ;
	setAttr ".hc" -type "string" "viewSet -t %camera";
	setAttr ".o" yes;
createNode transform -s -n "front";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 1.6376376152038574e-005 4.57763671875e-005 1000.3291111972184 ;
createNode camera -s -n "frontShape" -p "front";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 1000.0426980160948;
	setAttr ".ow" 2.3878580063660335;
	setAttr ".imn" -type "string" "front";
	setAttr ".den" -type "string" "front_depth";
	setAttr ".man" -type "string" "front_mask";
	setAttr ".tp" -type "double3" 3.5017728805541992e-006 0.39124151319265366 0.057378515601158142 ;
	setAttr ".hc" -type "string" "viewSet -f %camera";
	setAttr ".o" yes;
createNode transform -s -n "side";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 1000.3281039589198 4.57763671875e-005 -0.011432573198827921 ;
	setAttr ".r" -type "double3" -6.209983444176606e-021 89.999999999999972 0 ;
	setAttr ".rp" -type "double3" -1.3552527156068805e-020 0 0 ;
	setAttr ".rpt" -type "double3" 1.3552527156142764e-020 0 1.3552527156068808e-020 ;
createNode camera -s -n "sideShape" -p "side";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 1000.3280875182904;
	setAttr ".ow" 2.3849245198717264;
	setAttr ".imn" -type "string" "side";
	setAttr ".den" -type "string" "side_depth";
	setAttr ".man" -type "string" "side_mask";
	setAttr ".tp" -type "double3" 1.637637637941225e-005 -0.00046191104329646783 -0.00010603994489975526 ;
	setAttr ".hc" -type "string" "viewSet -s %camera";
	setAttr ".o" yes;
createNode transform -n "eyeball";
	setAttr -l on ".tx";
	setAttr -l on ".ty";
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr -l on ".rz";
	setAttr -l on ".sx";
	setAttr -l on ".sy";
	setAttr -l on ".sz";
createNode mesh -n "eyeballShape" -p "eyeball";
	setAttr -k off ".v";
	setAttr -s 6 ".iog[0].og";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
createNode mesh -n "eyeballShapeOrig" -p "eyeball";
	setAttr -k off ".v";
	setAttr ".io" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr -s 544 ".uvst[0].uvsp";
	setAttr ".uvst[0].uvsp[0:249]" -type "float2" 0 0.050000001 0.050000001 0.050000001
		 0.1 0.050000001 0.15000001 0.050000001 0.2 0.050000001 0.25 0.050000001 0.30000001
		 0.050000001 0.35000002 0.050000001 0.40000004 0.050000001 0.45000005 0.050000001
		 0.50000006 0.050000001 0.55000007 0.050000001 0.60000008 0.050000001 0.6500001 0.050000001
		 0.70000011 0.050000001 0.75000012 0.050000001 0.80000013 0.050000001 0.85000014 0.050000001
		 0.90000015 0.050000001 0.95000017 0.050000001 1.000000119209 0.050000001 0 0.1 0.050000001
		 0.1 0.1 0.1 0.15000001 0.1 0.2 0.1 0.25 0.1 0.30000001 0.1 0.35000002 0.1 0.40000004
		 0.1 0.45000005 0.1 0.50000006 0.1 0.55000007 0.1 0.60000008 0.1 0.6500001 0.1 0.70000011
		 0.1 0.75000012 0.1 0.80000013 0.1 0.85000014 0.1 0.90000015 0.1 0.95000017 0.1 1.000000119209
		 0.1 0 0.15000001 0.050000001 0.15000001 0.1 0.15000001 0.15000001 0.15000001 0.2
		 0.15000001 0.25 0.15000001 0.30000001 0.15000001 0.35000002 0.15000001 0.40000004
		 0.15000001 0.45000005 0.15000001 0.50000006 0.15000001 0.55000007 0.15000001 0.60000008
		 0.15000001 0.6500001 0.15000001 0.70000011 0.15000001 0.75000012 0.15000001 0.80000013
		 0.15000001 0.85000014 0.15000001 0.90000015 0.15000001 0.95000017 0.15000001 1.000000119209
		 0.15000001 0 0.2 0.050000001 0.2 0.1 0.2 0.15000001 0.2 0.2 0.2 0.25 0.2 0.30000001
		 0.2 0.35000002 0.2 0.40000004 0.2 0.45000005 0.2 0.50000006 0.2 0.55000007 0.2 0.60000008
		 0.2 0.6500001 0.2 0.70000011 0.2 0.75000012 0.2 0.80000013 0.2 0.85000014 0.2 0.90000015
		 0.2 0.95000017 0.2 1.000000119209 0.2 0 0.25 0.050000001 0.25 0.1 0.25 0.15000001
		 0.25 0.2 0.25 0.25 0.25 0.30000001 0.25 0.35000002 0.25 0.40000004 0.25 0.45000005
		 0.25 0.50000006 0.25 0.55000007 0.25 0.60000008 0.25 0.6500001 0.25 0.70000011 0.25
		 0.75000012 0.25 0.80000013 0.25 0.85000014 0.25 0.90000015 0.25 0.95000017 0.25 1.000000119209
		 0.25 0 0.30000001 0.050000001 0.30000001 0.1 0.30000001 0.15000001 0.30000001 0.2
		 0.30000001 0.25 0.30000001 0.30000001 0.30000001 0.35000002 0.30000001 0.40000004
		 0.30000001 0.45000005 0.30000001 0.50000006 0.30000001 0.55000007 0.30000001 0.60000008
		 0.30000001 0.6500001 0.30000001 0.70000011 0.30000001 0.75000012 0.30000001 0.80000013
		 0.30000001 0.85000014 0.30000001 0.90000015 0.30000001 0.95000017 0.30000001 1.000000119209
		 0.30000001 0 0.35000002 0.050000001 0.35000002 0.1 0.35000002 0.15000001 0.35000002
		 0.2 0.35000002 0.25 0.35000002 0.30000001 0.35000002 0.35000002 0.35000002 0.40000004
		 0.35000002 0.45000005 0.35000002 0.50000006 0.35000002 0.55000007 0.35000002 0.60000008
		 0.35000002 0.6500001 0.35000002 0.70000011 0.35000002 0.75000012 0.35000002 0.80000013
		 0.35000002 0.85000014 0.35000002 0.90000015 0.35000002 0.95000017 0.35000002 1.000000119209
		 0.35000002 0 0.40000004 0.050000001 0.40000004 0.1 0.40000004 0.15000001 0.40000004
		 0.2 0.40000004 0.25 0.40000004 0.30000001 0.40000004 0.35000002 0.40000004 0.40000004
		 0.40000004 0.45000005 0.40000004 0.50000006 0.40000004 0.55000007 0.40000004 0.60000008
		 0.40000004 0.6500001 0.40000004 0.70000011 0.40000004 0.75000012 0.40000004 0.80000013
		 0.40000004 0.85000014 0.40000004 0.90000015 0.40000004 0.95000017 0.40000004 1.000000119209
		 0.40000004 0 0.45000005 0.050000001 0.45000005 0.1 0.45000005 0.15000001 0.45000005
		 0.2 0.45000005 0.25 0.45000005 0.30000001 0.45000005 0.35000002 0.45000005 0.40000004
		 0.45000005 0.45000005 0.45000005 0.50000006 0.45000005 0.55000007 0.45000005 0.60000008
		 0.45000005 0.6500001 0.45000005 0.70000011 0.45000005 0.75000012 0.45000005 0.80000013
		 0.45000005 0.85000014 0.45000005 0.90000015 0.45000005 0.95000017 0.45000005 1.000000119209
		 0.45000005 0 0.50000006 0.050000001 0.50000006 0.1 0.50000006 0.15000001 0.50000006
		 0.2 0.50000006 0.25 0.50000006 0.30000001 0.50000006 0.35000002 0.50000006 0.40000004
		 0.50000006 0.45000005 0.50000006 0.50000006 0.50000006 0.55000007 0.50000006 0.60000008
		 0.50000006 0.6500001 0.50000006 0.70000011 0.50000006 0.75000012 0.50000006 0.80000013
		 0.50000006 0.85000014 0.50000006 0.90000015 0.50000006 0.95000017 0.50000006 1.000000119209
		 0.50000006 0 0.55000007 0.050000001 0.55000007 0.1 0.55000007 0.15000001 0.55000007
		 0.2 0.55000007 0.25 0.55000007 0.30000001 0.55000007 0.35000002 0.55000007 0.40000004
		 0.55000007 0.45000005 0.55000007 0.50000006 0.55000007 0.55000007 0.55000007 0.60000008
		 0.55000007 0.6500001 0.55000007 0.70000011 0.55000007 0.75000012 0.55000007 0.80000013
		 0.55000007 0.85000014 0.55000007 0.90000015 0.55000007 0.95000017 0.55000007 1.000000119209
		 0.55000007 0 0.60000008 0.050000001 0.60000008 0.1 0.60000008 0.15000001 0.60000008
		 0.2 0.60000008 0.25 0.60000008 0.30000001 0.60000008 0.35000002 0.60000008 0.40000004
		 0.60000008 0.45000005 0.60000008 0.50000006 0.60000008 0.55000007 0.60000008 0.60000008
		 0.60000008 0.6500001 0.60000008 0.70000011 0.60000008 0.75000012 0.60000008 0.80000013
		 0.60000008 0.85000014 0.60000008 0.90000015 0.60000008;
	setAttr ".uvst[0].uvsp[250:499]" 0.95000017 0.60000008 1.000000119209 0.60000008
		 0 0.6500001 0.050000001 0.6500001 0.1 0.6500001 0.15000001 0.6500001 0.2 0.6500001
		 0.25 0.6500001 0.30000001 0.6500001 0.35000002 0.6500001 0.40000004 0.6500001 0.45000005
		 0.6500001 0.50000006 0.6500001 0.55000007 0.6500001 0.60000008 0.6500001 0.6500001
		 0.6500001 0.70000011 0.6500001 0.75000012 0.6500001 0.80000013 0.6500001 0.85000014
		 0.6500001 0.90000015 0.6500001 0.95000017 0.6500001 1.000000119209 0.6500001 0 0.70000011
		 0.050000001 0.70000011 0.1 0.70000011 0.15000001 0.70000011 0.2 0.70000011 0.25 0.70000011
		 0.30000001 0.70000011 0.35000002 0.70000011 0.40000004 0.70000011 0.45000005 0.70000011
		 0.50000006 0.70000011 0.55000007 0.70000011 0.60000008 0.70000011 0.6500001 0.70000011
		 0.70000011 0.70000011 0.75000012 0.70000011 0.80000013 0.70000011 0.85000014 0.70000011
		 0.90000015 0.70000011 0.95000017 0.70000011 1.000000119209 0.70000011 0 0.75000012
		 0.050000001 0.75000012 0.1 0.75000012 0.15000001 0.75000012 0.2 0.75000012 0.25 0.75000012
		 0.30000001 0.75000012 0.35000002 0.75000012 0.40000004 0.75000012 0.45000005 0.75000012
		 0.50000006 0.75000012 0.55000007 0.75000012 0.60000008 0.75000012 0.6500001 0.75000012
		 0.70000011 0.75000012 0.75000012 0.75000012 0.80000013 0.75000012 0.85000014 0.75000012
		 0.90000015 0.75000012 0.95000017 0.75000012 1.000000119209 0.75000012 0 0.80000013
		 0.050000001 0.80000013 0.1 0.80000013 0.15000001 0.80000013 0.2 0.80000013 0.25 0.80000013
		 0.30000001 0.80000013 0.35000002 0.80000013 0.40000004 0.80000013 0.45000005 0.80000013
		 0.50000006 0.80000013 0.55000007 0.80000013 0.60000008 0.80000013 0.6500001 0.80000013
		 0.70000011 0.80000013 0.75000012 0.80000013 0.80000013 0.80000013 0.85000014 0.80000013
		 0.90000015 0.80000013 0.95000017 0.80000013 1.000000119209 0.80000013 0 0.85000014
		 0.050000001 0.85000014 0.1 0.85000014 0.15000001 0.85000014 0.2 0.85000014 0.25 0.85000014
		 0.30000001 0.85000014 0.35000002 0.85000014 0.40000004 0.85000014 0.45000005 0.85000014
		 0.50000006 0.85000014 0.55000007 0.85000014 0.60000008 0.85000014 0.6500001 0.85000014
		 0.70000011 0.85000014 0.75000012 0.85000014 0.80000013 0.85000014 0.85000014 0.85000014
		 0.90000015 0.85000014 0.95000017 0.85000014 1.000000119209 0.85000014 0 0.90000015
		 0.050000001 0.90000015 0.1 0.90000015 0.15000001 0.90000015 0.2 0.90000015 0.25 0.90000015
		 0.30000001 0.90000015 0.35000002 0.90000015 0.40000004 0.90000015 0.45000005 0.90000015
		 0.50000006 0.90000015 0.55000007 0.90000015 0.60000008 0.90000015 0.6500001 0.90000015
		 0.70000011 0.90000015 0.75000012 0.90000015 0.80000013 0.90000015 0.85000014 0.90000015
		 0.90000015 0.90000015 0.95000017 0.90000015 1.000000119209 0.90000015 0 0.95000017
		 0.050000001 0.95000017 0.1 0.95000017 0.15000001 0.95000017 0.2 0.95000017 0.25 0.95000017
		 0.30000001 0.95000017 0.35000002 0.95000017 0.40000004 0.95000017 0.45000005 0.95000017
		 0.50000006 0.95000017 0.55000007 0.95000017 0.60000008 0.95000017 0.6500001 0.95000017
		 0.70000011 0.95000017 0.75000012 0.95000017 0.80000013 0.95000017 0.85000014 0.95000017
		 0.90000015 0.95000017 0.95000017 0.95000017 1.000000119209 0.95000017 0.025 0 0.075000003
		 0 0.125 0 0.175 0 0.22500001 0 0.27500001 0 0.32500002 0 0.375 0 0.42500001 0 0.47499999
		 0 0.52500004 0 0.57499999 0 0.625 0 0.67500001 0 0.72500002 0 0.77500004 0 0.82499999
		 0 0.875 0 0.92500001 0 0.97500002 0 0.025 1 0.075000003 1 0.125 1 0.175 1 0.22500001
		 1 0.27500001 1 0.32500002 1 0.375 1 0.42500001 1 0.47499999 1 0.52500004 1 0.57499999
		 1 0.625 1 0.67500001 1 0.72500002 1 0.77500004 1 0.82499999 1 0.875 1 0.92500001
		 1 0.97500002 1 0.30000001 0.94439614 0.25 0.94439614 0.2 0.94439614 0.15000001 0.94439614
		 0.1 0.94439614 0.050000001 0.94439614 1.000000119209 0.94439614 0 0.94439614 0.95000017
		 0.94439614 0.90000015 0.94439614 0.85000014 0.94439614 0.80000019 0.94439614 0.75000012
		 0.94439614 0.70000011 0.94439614 0.6500001 0.94439614 0.60000002 0.94439614 0.55000007
		 0.94439614 0.50000006 0.94439614 0.45000005 0.94439614 0.40000004 0.94439614 0.35000002
		 0.94439614 0.30000001 0.92804581 0.25 0.92804581 0.20000002 0.92804581 0.15000001
		 0.92804581 0.10000001 0.92804581 0.050000004 0.92804581 1.000000119209 0.92804581
		 0 0.92804581 0.95000017 0.92804581 0.90000015 0.92804581 0.85000014 0.92804581 0.80000019
		 0.92804581 0.75000012 0.92804581 0.70000011 0.92804581 0.6500001 0.92804581 0.60000002
		 0.92804581 0.55000007 0.92804581 0.50000006 0.92804581 0.45000005 0.92804581 0.40000004
		 0.92804581 0.35000002 0.92804581 0.30000001 0.91036844 0.25 0.91036844 0.20000002
		 0.91036844 0.15000001 0.91036844 0.10000001 0.91036844 0.050000004 0.91036844 1.000000119209
		 0.91036844 0 0.91036844 0.95000017 0.91036844 0.90000021 0.91036844 0.85000014 0.91036844
		 0.80000019 0.91036844 0.75000012 0.91036844 0.70000017 0.91036844 0.6500001 0.91036844
		 0.60000008 0.91036844 0.55000007 0.91036844 0.50000006 0.91036844 0.45000005 0.91036844;
	setAttr ".uvst[0].uvsp[500:543]" 0.40000007 0.91036844 0.35000002 0.91036844
		 0.30000001 0.90617871 0.25 0.90617871 0.20000002 0.90617871 0.15000001 0.90617871
		 0.10000001 0.90617871 0.050000004 0.90617871 1.000000119209 0.90617871 0 0.90617871
		 0.95000017 0.90617871 0.90000021 0.90617871 0.85000014 0.90617871 0.80000019 0.90617871
		 0.75000012 0.90617871 0.70000017 0.90617871 0.6500001 0.90617871 0.60000008 0.90617871
		 0.55000007 0.90617871 0.50000006 0.90617871 0.45000005 0.90617871 0.40000004 0.90617871
		 0.35000002 0.90617871 0.80000019 0.94492835 0.75000012 0.94492835 0.70000011 0.94492835
		 0.6500001 0.94492835 0.60000002 0.94492835 0.55000007 0.94492835 0.50000006 0.94492835
		 0.45000005 0.94492835 0.40000004 0.94492835 0.35000002 0.94492835 0.30000001 0.94492835
		 0.25 0.94492835 0.2 0.94492835 0.15000001 0.94492835 0.1 0.94492835 0.050000001 0.94492835
		 1.000000119209 0.94492835 0 0.94492835 0.95000017 0.94492835 0.90000015 0.94492835
		 0.85000014 0.94492835;
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr -s 482 ".pt";
	setAttr ".pt[0:165]" -type "float3"  -0.074389264 1.0118575 -0.4455038 -0.063279092 
		1.0336659 -0.40189463 -0.045975015 1.0509686 -0.36728638 -0.024170557 1.0620753 -0.34506646 
		4.6185278e-014 1.0659046 -0.33741003 0.024170479 1.0620753 -0.34506643 0.045975 1.0509686 
		-0.36728635 0.063278943 1.0336659 -0.40189466 0.074389368 1.0118575 -0.4455038 0.078217171 
		0.98768538 -0.49384475 0.074389368 0.96351701 -0.54218566 0.063278943 0.94171101 
		-0.58579475 0.045975011 0.92440873 -0.62040311 0.024170473 0.91330016 -0.64262313 
		2.3310101e-009 0.90946972 -0.65027964 -0.024170538 0.91330016 -0.64262307 -0.045974992 
		0.92440873 -0.62040317 -0.063279018 0.94171101 -0.58579457 -0.074389204 0.96351701 
		-0.54218531 -0.078217141 0.98768538 -0.49384475 -0.14694673 0.99880105 -0.38003686 
		-0.12499963 1.0418739 -0.29389229 -0.090817742 1.0760567 -0.22552773 -0.047745544 
		1.0980016 -0.1816348 4.6185278e-014 1.1055659 -0.16651033 0.047745686 1.0980016 -0.18163483 
		0.09081772 1.0760567 -0.22552779 0.12499953 1.0418739 -0.29389232 0.1469465 0.99880105 
		-0.38003686 0.15450846 0.95105356 -0.4755289 0.1469465 0.90330958 -0.57102048 0.12499952 
		0.86023647 -0.65716463 0.09081775 0.82605809 -0.72552896 0.047745727 0.80411059 -0.7694217 
		4.6046758e-009 0.7965495 -0.78454602 -0.047745511 0.80411071 -0.76942158 -0.090817645 
		0.82605821 -0.72552884 -0.12499948 0.86023647 -0.65716451 -0.14694655 0.90330958 
		-0.57102048 -0.15450847 0.95105356 -0.4755289 -0.21588595 0.96115303 -0.3052128 -0.18364319 
		1.0244327 -0.17865452 -0.13342443 1.0746511 -0.078216583 -0.070145413 1.1068902 -0.013732255 
		4.6185278e-014 1.1180031 0.008487612 0.070145354 1.1068902 -0.013732314 0.13342428 
		1.0746511 -0.078216672 0.18364313 1.0244325 -0.17865452 0.2158857 0.96115303 -0.30521286 
		0.22699453 0.89100355 -0.44550306 0.2158857 0.82086086 -0.58579421 0.18364309 0.757581 
		-0.71235257 0.13342425 0.70736301 -0.81279063 0.070145316 0.67512208 -0.87727463 
		6.765049e-009 0.6640116 -0.89949441 -0.070145354 0.67512208 -0.87727457 -0.13342425 
		0.70736301 -0.81279051 -0.1836431 0.757581 -0.71235263 -0.2158857 0.82086086 -0.58579421 
		-0.22699451 0.89100355 -0.44550306 -0.27950814 0.89983445 -0.22287309 -0.23776434 
		0.98176104 -0.059016675 -0.17274605 1.0467795 0.071020693 -0.090817727 1.0885247 
		0.15450948 4.6185278e-014 1.1029066 0.18327793 0.090817697 1.0885247 0.15450945 0.17274587 
		1.0467794 0.071020573 0.23776414 0.98176104 -0.059016794 0.27950799 0.89983428 -0.22287315 
		0.29389301 0.8090142 -0.40450868 0.27950799 0.71819705 -0.58614373 0.23776412 0.6362713 
		-0.75000101 0.17274578 0.57125455 -0.88003707 0.090817757 0.52950931 -0.96352559 
		8.758672e-009 0.51512468 -0.99229407 -0.090817638 0.52950931 -0.96352559 -0.17274584 
		0.57125455 -0.88003719 -0.23776411 0.6362713 -0.75000089 -0.27950799 0.71819705 -0.58614397 
		-0.29389298 0.8090142 -0.40450868 -0.33624998 0.81636238 -0.13504541 -0.28603041 
		0.91491824 0.062073529 -0.20781285 0.99313712 0.21850803 -0.10925424 1.0433558 0.31894502 
		4.6185278e-014 1.0606612 0.35355306 0.10925432 1.0433558 0.31894514 0.20781271 0.99313712 
		0.21850792 0.28603014 0.91491807 0.06207341 0.3362498 0.81636238 -0.13504545 0.35355327 
		0.70710385 -0.35355353 0.3362498 0.59785038 -0.57206231 0.28603014 0.49929422 -0.76918209 
		0.20781267 0.42107517 -0.92561626 0.10925429 0.37085822 -1.0260539 1.0536727e-008 
		0.35355338 -1.0606623 -0.10925419 0.37085822 -1.0260539 -0.20781264 0.42107517 -0.92561632 
		-0.28603011 0.49929422 -0.76918209 -0.33624974 0.59785038 -0.57206225 -0.35355321 
		0.70710385 -0.35355353 -0.38471025 0.71278208 -0.043892816 -0.32725406 0.8255477 
		0.18163547 -0.23776434 0.91503966 0.36061811 -0.12499958 0.97249734 0.47553092 4.6185278e-014 
		0.99229264 0.5151273 0.12499952 0.97249734 0.47553071 0.23776416 0.91503966 0.360618 
		0.32725379 0.8255477 0.18163523 0.38470998 0.71278208 -0.043892831 0.4045094 0.58778226 
		-0.29389313 0.38470998 0.46278647 -0.54389262 0.32725394 0.35002226 -0.76941937 0.23776412 
		0.26053119 -0.9483999 0.12499952 0.20307517 -1.0633134 1.2055294e-008 0.18327734 
		-1.1029096 -0.12499947 0.20307517 -1.0633134 -0.23776411 0.26053119 -0.94839984 -0.32725385 
		0.35002229 -0.76941937 -0.38470984 0.46278647 -0.54389244 -0.40450934 0.58778226 
		-0.29389313 -0.42370024 0.5916602 0.048341885 -0.36042151 0.71584916 0.29672605 -0.26186073 
		0.81440794 0.49384421 -0.13766854 0.87768775 0.62040234 4.6185278e-014 0.8994934 
		0.66401136 0.13766843 0.87768775 0.62040234 0.26186061 0.81440794 0.49384403 0.36042148 
		0.71584862 0.29672587 0.42370021 0.5916602 0.048341766 0.44550389 0.45399126 -0.22699548 
		0.42370021 0.31632182 -0.50233108 0.36042148 0.19213109 -0.75071687 0.26186055 0.093572795 
		-0.94783545 0.13766834 0.030293167 -1.0743945 1.3277008e-008 0.0084877014 -1.1180037 
		-0.1376684 0.030293196 -1.0743945 -0.26186049 0.093572855 -0.94783539 -0.36042139 
		0.19213109 -0.75071687 -0.42370009 0.31632182 -0.50233102 -0.44550377 0.45399126 
		-0.22699548 -0.45225373 0.45596403 0.13938513 -0.38471025 0.58852547 0.40450901 -0.27950814 
		0.69372928 0.61491221 -0.14694667 0.76127315 0.7499994 4.6185278e-014 0.78454602 
		0.79654771 0.14694655 0.76127315 0.74999958 0.27950811 0.69372928 0.61491209 0.38470995 
		0.58852518 0.40450883 0.45225334 0.45596403 0.13938512 0.4755283 0.30901837 -0.15450855 
		0.45225334 0.16207188 -0.44840091 0.38470998 0.029509321 -0.71352714 0.27950799 -0.075692952 
		-0.92393017 0.1469465 -0.1432364 -1.0590185 1.4171867e-008 -0.16651005 -1.1055667 
		-0.14694652 -0.1432364 -1.0590185 -0.27950799 -0.075692952 -0.92393011 -0.38470984 
		0.029509321 -0.71352714 -0.45225322 0.16207191 -0.44840086 -0.47552818 0.30901837 
		-0.15450855 -0.46967623 0.30904031 0.22699623 -0.39952874 0.44671062 0.50233382 -0.29027414 
		0.55596405 0.72084194 -0.15260661 0.6261071 0.86113161 4.6185278e-014 0.65027881 
		0.90947229 0.15260649 0.6261071 0.86113149;
	setAttr ".pt[166:331]" 0.29027423 0.55596393 0.72084188 0.3995285 0.44671059 
		0.50233364 0.46967646 0.30903998 0.22699611 0.49384379 0.15643565 -0.078217223 0.46967646 
		0.0038291365 -0.38342941 0.39952844 -0.13383883 -0.65876454 0.29027411 -0.24309364 
		-0.87727195 0.15260635 -0.31323853 -1.017562 1.4717719e-008 -0.3374095 -1.0659045 
		-0.15260643 -0.31323853 -1.017562 -0.29027411 -0.24309364 -0.87727189 -0.39952838 
		-0.13383883 -0.65876448 -0.46967646 0.0038291812 -0.38342935 -0.49384367 0.15643565 
		-0.078217223 -0.47552872 0.15450931 0.30901703 -0.40450966 0.29389256 0.58778793 
		-0.29389334 0.40450963 0.8090198 -0.15450859 0.47552902 0.95105916 4.6185278e-014 
		0.50000191 1.0000023 0.15450853 0.47552866 0.9510591 0.2938931 0.40450954 0.80901963 
		0.40450945 0.29389256 0.58778775 0.4755283 0.15450929 0.30901691 0.49999803 5.7736429e-007 
		5.3085387e-008 0.4755283 -0.15450849 -0.30901754 0.4045094 -0.29389206 -0.58778328 
		0.29389301 -0.40450889 -0.8090148 0.15450846 -0.47552815 -0.95105433 1.4901161e-008 
		-0.50000072 -0.99999613 -0.15450847 -0.47552782 -0.95105428 -0.29389298 -0.40450889 
		-0.80901474 -0.40450934 -0.29389203 -0.58778322 -0.47552818 -0.15450849 -0.30901748 
		-0.49999791 5.7736429e-007 5.3085387e-008 -0.46967623 -0.0038280636 0.38343 -0.39952874 
		0.13383982 0.65876716 -0.29027414 0.24309462 0.87727529 -0.15260661 0.31323972 1.0175666 
		4.6185278e-014 0.33741042 1.0659066 0.15260649 0.31323972 1.0175666 0.29027423 0.24309435 
		0.87727511 0.3995285 0.1338397 0.65876698 0.46967646 -0.0038280934 0.38342988 0.49384379 
		-0.15643504 0.078217089 0.46967646 -0.30904031 -0.226996 0.39952844 -0.44670954 -0.50233078 
		0.29027411 -0.55596328 -0.72083861 0.15260635 -0.6261068 -0.8611294 1.4717719e-008 
		-0.65027809 -0.90947032 -0.15260643 -0.6261068 -0.8611294 -0.29027411 -0.55596328 
		-0.72083855 -0.39952838 -0.44670954 -0.50233072 -0.46967646 -0.30904025 -0.22699594 
		-0.49384367 -0.15643504 0.078217089 -0.45225373 -0.16207063 0.44840109 -0.38471025 
		-0.029507846 0.71352673 -0.27950814 0.075693905 0.92393088 -0.14694667 0.14323765 
		1.059019 4.6185278e-014 0.16651173 1.105567 0.14694655 0.14323762 1.0590189 0.27950811 
		0.075693816 0.92393076 0.38470995 -0.029507905 0.71352655 0.45225334 -0.16207069 
		0.44840103 0.4755283 -0.30901816 0.15450835 0.45225334 -0.45596343 -0.1393844 0.38470998 
		-0.5885247 -0.40450919 0.27950799 -0.69372821 -0.61491263 0.1469465 -0.76127195 -0.74999928 
		1.4171867e-008 -0.78454494 -0.79654729 -0.14694652 -0.76127189 -0.74999923 -0.27950799 
		-0.69372821 -0.61491257 -0.38470984 -0.5885247 -0.40450913 -0.45225322 -0.45596343 
		-0.13938434 -0.47552818 -0.30901816 0.15450835 -0.42370024 -0.3163217 0.50233126 
		-0.36042151 -0.19213039 0.75071514 -0.26186073 -0.093571305 0.94783384 -0.13766854 
		-0.030291885 1.0743946 4.6185278e-014 -0.0084867179 1.1180032 0.13766843 -0.030291885 
		1.0743946 0.26186061 -0.093571335 0.94783384 0.36042148 -0.19213052 0.75071478 0.42370021 
		-0.31632176 0.50233114 0.44550389 -0.45399168 0.22699526 0.42370021 -0.59165901 -0.048341006 
		0.36042148 -0.71584904 -0.29672614 0.26186055 -0.81441224 -0.49384567 0.13766834 
		-0.87768865 -0.62040335 1.3277008e-008 -0.89949244 -0.66401231 -0.1376684 -0.87768865 
		-0.62040329 -0.26186049 -0.81441176 -0.49384561 -0.36042139 -0.71584904 -0.29672608 
		-0.42370009 -0.59165901 -0.048340976 -0.44550377 -0.45399168 0.22699526 -0.38471025 
		-0.46278626 0.54389417 -0.32725406 -0.3500213 0.76942158 -0.23776434 -0.26053116 
		0.94840193 -0.12499958 -0.20307532 1.0633135 4.6185278e-014 -0.18327647 1.1029096 
		0.12499952 -0.20307535 1.0633135 0.23776416 -0.26053116 0.94840181 0.32725379 -0.35002136 
		0.76942134 0.38470998 -0.46278626 0.54389399 0.4045094 -0.58778721 0.29389256 0.38470998 
		-0.71278626 0.043892667 0.32725394 -0.82554841 -0.18163508 0.23776412 -0.91504025 
		-0.36061609 0.12499952 -0.97249639 -0.47552922 1.2055294e-008 -0.99229336 -0.51512408 
		-0.12499947 -0.97249639 -0.47552922 -0.23776411 -0.91504025 -0.36061603 -0.32725385 
		-0.82554841 -0.18163502 -0.38470984 -0.71278626 0.043892726 -0.40450934 -0.58778721 
		0.29389256 -0.33624998 -0.59785277 0.57206178 -0.28603041 -0.49929464 0.76918054 
		-0.20781285 -0.42107445 0.9256146 -0.10925424 -0.37085789 1.0260528 4.6185278e-014 
		-0.35355303 1.0606622 0.10925432 -0.37085789 1.0260526 0.20781271 -0.42107445 0.92561448 
		0.28603014 -0.49929464 0.76918042 0.3362498 -0.59785277 0.57206166 0.35355327 -0.70710796 
		0.35355353 0.3362498 -0.81636214 0.13504516 0.28603014 -0.91492093 -0.062073559 0.20781267 
		-0.99313796 -0.21850806 0.10925429 -1.0433552 -0.31894588 1.0536727e-008 -1.0606604 
		-0.35355413 -0.10925419 -1.0433552 -0.31894588 -0.20781264 -0.99313796 -0.21850801 
		-0.28603011 -0.91492093 -0.062073529 -0.33624974 -0.81636214 0.13504519 -0.35355321 
		-0.70710796 0.35355353 -0.27950814 -0.71819746 0.58614314 -0.23776434 -0.63627219 
		0.74999887 -0.17274605 -0.57125318 0.88003623 -0.090817727 -0.52950913 0.96352482 
		4.6185278e-014 -0.51512372 0.99229324 0.090817697 -0.52950913 0.96352494 0.17274587 
		-0.57125318 0.88003612 0.23776414 -0.63627219 0.74999875 0.27950799 -0.71819764 0.58614308 
		0.29389301 -0.80901867 0.40450799 0.27950799 -0.89983684 0.22287282 0.23776412 -0.98176128 
		0.059016585 0.17274578 -1.0467802 -0.071020305 0.090817757 -1.0885243 -0.15450922 
		8.758672e-009 -1.102909 -0.18327749 -0.090817638 -1.0885243 -0.15450922 -0.17274584 
		-1.0467802 -0.071020275 -0.23776411 -0.98176128 0.059016615 -0.27950799 -0.89983684 
		0.22287285 -0.29389298 -0.80901867 0.40450799 -0.21588595 -0.82086045 0.58579397 
		-0.18364319 -0.7575804 0.71235228 -0.13342443 -0.70736462 0.81279129 -0.070145413 
		-0.67512202 0.87727588 4.6185278e-014 -0.66401166 0.89949548 0.070145354 -0.67512202 
		0.87727559 0.13342428 -0.70736474 0.81279099 0.18364313 -0.75758046 0.71235216 0.2158857 
		-0.82086045 0.58579391 0.22699453 -0.8910085 0.44550329 0.2158857 -0.96115202 0.30521253 
		0.18364309 -1.0244321 0.17865436;
	setAttr ".pt[332:481]" 0.13342425 -1.0746508 0.07821703 0.070145316 -1.1068902 
		0.013732225 6.765049e-009 -1.1180036 -0.0084876716 -0.070145354 -1.1068902 0.013732255 
		-0.13342425 -1.0746508 0.078217059 -0.1836431 -1.0244321 0.17865439 -0.2158857 -0.96115202 
		0.30521256 -0.22699451 -0.8910085 0.44550329 -0.14892118 -0.90395319 0.57101953 -0.12667896 
		-0.86145991 0.65716374 -0.092038073 -0.82773656 0.72552848 -0.048387062 -0.80608636 
		0.7694211 -2.0015278e-010 -0.79862225 0.78454542 0.048387036 -0.80608636 0.7694211 
		0.09203802 -0.82773656 0.72552836 0.12667881 -0.86145991 0.65716368 0.14892098 -0.90395319 
		0.57101953 0.15658434 -0.95105821 0.47552791 0.14892098 -0.9981606 0.38003641 0.12667878 
		-1.040657 0.29389241 0.092037991 -1.0743773 0.22552828 0.048387013 -1.0960255 0.18163572 
		4.4664574e-009 -1.10349 0.16651137 -0.048387058 -1.0960255 0.18163575 -0.09203802 
		-1.0743773 0.22552831 -0.12667881 -1.0406567 0.29389244 -0.14892104 -0.9981606 0.38003641 
		-0.15658435 -0.95105821 0.47552791 -0.037006572 -0.89542377 0.47777259 -0.031479787 
		-0.88457626 0.49946696 -0.022871338 -0.87596917 0.5166837 -0.012024172 -0.87044078 
		0.52773762 5.6161973e-009 -0.86853766 0.53154659 0.012024187 -0.87044078 0.52773762 
		0.022871308 -0.87596917 0.5166837 0.031479716 -0.88457626 0.4994669 0.037006609 -0.89542377 
		0.47777256 0.038911108 -0.90745026 0.45372435 0.037006609 -0.91947234 0.429676 0.031479716 
		-0.93031967 0.40798157 0.022871295 -0.93892783 0.39076483 0.012024182 -0.94445682 
		0.37971106 6.7757924e-009 -0.94636011 0.37590209 -0.012024173 -0.94445682 0.37971103 
		-0.022871317 -0.93892783 0.39076489 -0.031479754 -0.93031967 0.40798163 -0.037006535 
		-0.91947234 0.429676 -0.03891103 -0.90745026 0.45372435 4.6185278e-014 0.99999702 
		-0.49999973 4.6185278e-014 -0.90744978 0.45372379 0.046998613 -0.84275818 0.58310175 
		0.024708744 -0.83140355 0.60581666 1.169397e-009 -0.82748777 0.61364353 -0.024708716 
		-0.83140355 0.60581666 -0.046998691 -0.84275812 0.58310163 -0.064688347 -0.86044747 
		0.54772222 -0.076045573 -0.88273954 0.50314176 -0.079959065 -0.90745026 0.45372429 
		-0.076045483 -0.9321565 0.40430692 -0.064688392 -0.95444953 0.35972658 -0.046998665 
		-0.97213829 0.32434753 -0.024708714 -0.98349255 0.30163264 3.5523335e-009 -0.9874081 
		0.29380572 0.024708744 -0.98349255 0.30163264 0.046998609 -0.97213829 0.32434753 
		0.064688511 -0.95444953 0.35972655 0.076045409 -0.9321565 0.40430674 0.07995908 -0.90745026 
		0.45372429 0.076045409 -0.88273954 0.50314176 0.064688511 -0.86044747 0.54772216 
		0.065664932 -0.84383553 0.64786762 0.034522176 -0.82797003 0.67960405 4.6185278e-014 
		-0.82250267 0.6905396 -0.034522228 -0.82797003 0.67960405 -0.065664798 -0.84383553 
		0.64786768 -0.090379938 -0.86855137 0.59843755 -0.10624801 -0.89969492 0.53615236 
		-0.11171592 -0.9342193 0.46710879 -0.106248 -0.96873921 0.39806467 -0.090379849 -0.99988282 
		0.33577907 -0.065664768 -1.0245987 0.286349 -0.03452222 -1.0404654 0.25461286 3.329415e-009 
		-1.0459313 0.2436773 0.034522161 -1.0404654 0.2546128 0.065664962 -1.0245987 0.28634888 
		0.090379775 -0.99988282 0.33577907 0.10624795 -0.96873921 0.39806461 0.11171586 -0.9342193 
		0.46710879 0.10624795 -0.89969492 0.53615236 0.090379797 -0.86855137 0.59843749 0.079401903 
		-0.84353882 0.70127624 0.041744348 -0.82332802 0.74067426 3.869971e-010 -0.81636399 
		0.75425011 -0.041744262 -0.82332802 0.74067414 -0.079402015 -0.84353882 0.7012763 
		-0.10928748 -0.87501687 0.63991135 -0.12847526 -0.91468316 0.56258762 -0.13508758 
		-0.95865375 0.47687373 -0.12847528 -1.0026214 0.39115939 -0.1092875 -1.0422881 0.31383529 
		-0.07940191 -1.0737662 0.25247055 -0.04174421 -1.0939773 0.21307205 4.4128647e-009 
		-1.1009443 0.19949621 0.04174434 -1.0939773 0.21307202 0.079401888 -1.0737662 0.25247049 
		0.10928752 -1.0422881 0.31383526 0.12847541 -1.0026214 0.39115939 0.13508755 -0.95865375 
		0.47687373 0.12847541 -0.91468316 0.56258768 0.10928754 -0.87501687 0.63991129 0.084067874 
		-0.83654612 0.71253765 0.044197325 -0.81564569 0.75375235 2.1110935e-010 -0.80844712 
		0.76795375 -0.044197179 -0.81564569 0.75375235 -0.084067948 -0.83654612 0.71253777 
		-0.11570975 -0.8690961 0.64834404 -0.13602468 -0.91011477 0.56745607 -0.14302501 
		-0.95558465 0.47779095 -0.13602459 -1.0010505 0.38812587 -0.11570971 -1.0420667 0.3072384 
		-0.084067911 -1.0746208 0.24304534 -0.044197239 -1.095518 0.20183089 4.4736375e-009 
		-1.1027178 0.18762936 0.044197313 -1.095518 0.20183083 0.084067859 -1.0746208 0.24304532 
		0.11570983 -1.0420667 0.30723825 0.13602458 -1.0010505 0.38812581 0.14302498 -0.95558465 
		0.47779095 0.13602458 -0.91011477 0.56745607 0.11570986 -0.8690961 0.64834392 -0.044707213 
		-0.96898276 0.33065516 -0.023504084 -0.9797855 0.30904776 3.8584922e-009 -0.98350978 
		0.30160236 0.02350403 -0.9797855 0.30904776 0.044707149 -0.96898276 0.3306551 0.061534569 
		-0.95215416 0.36430937 0.072337911 -0.93095285 0.40671617 0.076060534 -0.90745026 
		0.45372429 0.072337911 -0.8839432 0.50073242 0.061534569 -0.86274183 0.5431394 0.044707157 
		-0.84591329 0.57679397 0.02350403 -0.83510989 0.59840155 1.5917259e-009 -0.83138734 
		0.60584688 -0.023504093 -0.83510989 0.59840155 -0.04470728 -0.84591317 0.57679397 
		-0.061534632 -0.86274183 0.5431394 -0.072337836 -0.8839432 0.50073242 -0.076060563 
		-0.90745026 0.45372429 -0.072337799 -0.93095285 0.40671617 -0.061534569 -0.95215416 
		0.36430934;
	setAttr -s 482 ".vt";
	setAttr ".vt[0:165]"  0.14877813 -0.98768836 -0.048340943 0.12655823 -0.98768836 -0.091949932
		 0.091949932 -0.98768836 -0.12655823 0.048340935 -0.98768836 -0.14877811 0 -0.98768836 -0.15643455
		 -0.048340935 -0.98768836 -0.1487781 -0.091949917 -0.98768836 -0.1265582 -0.12655818 -0.98768836 -0.091949902
		 -0.14877807 -0.98768836 -0.048340924 -0.15643452 -0.98768836 0 -0.14877807 -0.98768836 0.048340924
		 -0.12655818 -0.98768836 0.091949895 -0.091949895 -0.98768836 0.12655817 -0.048340924 -0.98768836 0.14877805
		 -4.6621107e-009 -0.98768836 0.15643449 0.048340909 -0.98768836 0.14877804 0.09194988 -0.98768836 0.12655815
		 0.12655815 -0.98768836 0.091949888 0.14877804 -0.98768836 0.048340913 0.15643448 -0.98768836 0
		 0.29389283 -0.95105654 -0.095491566 0.25000018 -0.95105654 -0.18163574 0.18163574 -0.95105654 -0.25000015
		 0.095491551 -0.95105654 -0.2938928 0 -0.95105654 -0.30901715 -0.095491551 -0.95105654 -0.29389277
		 -0.18163571 -0.95105654 -0.25000009 -0.25000009 -0.95105654 -0.18163569 -0.29389271 -0.95105654 -0.095491529
		 -0.30901706 -0.95105654 0 -0.29389271 -0.95105654 0.095491529 -0.25000006 -0.95105654 0.18163568
		 -0.18163568 -0.95105654 0.25000006 -0.095491529 -0.95105654 0.29389268 -9.2094243e-009 -0.95105654 0.30901703
		 0.095491499 -0.95105654 0.29389265 0.18163563 -0.95105654 0.25000003 0.25 -0.95105654 0.18163565
		 0.29389265 -0.95105654 0.095491506 0.309017 -0.95105654 0 0.43177092 -0.89100653 -0.14029087
		 0.36728629 -0.89100653 -0.2668491 0.2668491 -0.89100653 -0.36728626 0.14029086 -0.89100653 -0.43177086
		 0 -0.89100653 -0.45399073 -0.14029086 -0.89100653 -0.43177083 -0.26684904 -0.89100653 -0.36728618
		 -0.36728615 -0.89100653 -0.26684901 -0.43177077 -0.89100653 -0.14029081 -0.45399064 -0.89100653 0
		 -0.43177077 -0.89100653 0.14029081 -0.36728612 -0.89100653 0.26684898 -0.26684898 -0.89100653 0.36728612
		 -0.14029081 -0.89100653 0.43177071 -1.3529972e-008 -0.89100653 0.45399058 0.14029078 -0.89100653 0.43177068
		 0.26684892 -0.89100653 0.36728609 0.36728606 -0.89100653 0.26684895 0.43177065 -0.89100653 0.1402908
		 0.45399052 -0.89100653 0 0.55901736 -0.809017 -0.18163574 0.47552857 -0.809017 -0.34549171
		 0.34549171 -0.809017 -0.47552854 0.18163572 -0.809017 -0.5590173 0 -0.809017 -0.58778554
		 -0.18163572 -0.809017 -0.55901724 -0.34549165 -0.809017 -0.47552842 -0.47552839 -0.809017 -0.34549159
		 -0.55901712 -0.809017 -0.18163566 -0.58778536 -0.809017 0 -0.55901712 -0.809017 0.18163566
		 -0.47552836 -0.809017 0.34549156 -0.34549156 -0.809017 0.47552833 -0.18163566 -0.809017 0.55901706
		 -1.7517365e-008 -0.809017 0.5877853 0.18163562 -0.809017 0.55901706 0.3454915 -0.809017 0.4755283
		 0.47552827 -0.809017 0.34549153 0.559017 -0.809017 0.18163563 0.58778524 -0.809017 0
		 0.67249894 -0.70710677 -0.21850814 0.57206178 -0.70710677 -0.41562718 0.41562718 -0.70710677 -0.57206172
		 0.21850812 -0.70710677 -0.67249888 0 -0.70710677 -0.70710713 -0.21850812 -0.70710677 -0.67249882
		 -0.41562709 -0.70710677 -0.5720616 -0.57206154 -0.70710677 -0.41562706 -0.6724987 -0.70710677 -0.21850805
		 -0.70710695 -0.70710677 0 -0.6724987 -0.70710677 0.21850805 -0.57206154 -0.70710677 0.415627
		 -0.415627 -0.70710677 0.57206148 -0.21850805 -0.70710677 0.67249858 -2.1073424e-008 -0.70710677 0.70710683
		 0.21850799 -0.70710677 0.67249858 0.41562691 -0.70710677 0.57206142 0.57206142 -0.70710677 0.41562697
		 0.67249852 -0.70710677 0.21850802 0.70710677 -0.70710677 0 0.7694214 -0.58778524 -0.25000015
		 0.65450895 -0.58778524 -0.47552854 0.47552854 -0.58778524 -0.65450889 0.25000012 -0.58778524 -0.76942128
		 0 -0.58778524 -0.80901736 -0.25000012 -0.58778524 -0.76942122 -0.47552845 -0.58778524 -0.65450877
		 -0.65450871 -0.58778524 -0.47552839 -0.7694211 -0.58778524 -0.25000006 -0.80901718 -0.58778524 0
		 -0.7694211 -0.58778524 0.25000006 -0.65450865 -0.58778524 0.47552836 -0.47552836 -0.58778524 0.65450859
		 -0.25000006 -0.58778524 0.76942098 -2.4110586e-008 -0.58778524 0.80901712 0.24999999 -0.58778524 0.76942098
		 0.47552827 -0.58778524 0.65450853 0.65450853 -0.58778524 0.4755283 0.76942092 -0.58778524 0.25
		 0.809017 -0.58778524 0 0.8473981 -0.45399052 -0.27533633 0.72083992 -0.45399052 -0.5237208
		 0.5237208 -0.45399052 -0.72083986 0.2753363 -0.45399052 -0.84739798 0 -0.45399052 -0.89100695
		 -0.2753363 -0.45399052 -0.84739798 -0.52372068 -0.45399052 -0.72083968 -0.72083962 -0.45399052 -0.52372062
		 -0.8473978 -0.45399052 -0.27533621 -0.89100677 -0.45399052 0 -0.8473978 -0.45399052 0.27533621
		 -0.72083962 -0.45399052 0.52372062 -0.52372062 -0.45399052 0.72083956 -0.27533621 -0.45399052 0.84739769
		 -2.6554064e-008 -0.45399052 0.89100665 0.27533615 -0.45399052 0.84739763 0.5237205 -0.45399052 0.7208395
		 0.72083944 -0.45399052 0.52372056 0.84739757 -0.45399052 0.27533618 0.89100653 -0.45399052 0
		 0.90450913 -0.30901697 -0.2938928 0.7694214 -0.30901697 -0.55901736 0.55901736 -0.30901697 -0.76942134
		 0.29389277 -0.30901697 -0.90450901 0 -0.30901697 -0.95105702 -0.29389277 -0.30901697 -0.90450895
		 -0.55901724 -0.30901697 -0.76942122 -0.76942116 -0.30901697 -0.55901718 -0.90450877 -0.30901697 -0.29389271
		 -0.95105678 -0.30901697 0 -0.90450877 -0.30901697 0.29389271 -0.7694211 -0.30901697 0.55901712
		 -0.55901712 -0.30901697 0.76942104 -0.29389271 -0.30901697 0.90450865 -2.8343694e-008 -0.30901697 0.95105666
		 0.29389262 -0.30901697 0.90450859 0.559017 -0.30901697 0.76942098 0.76942092 -0.30901697 0.55901706
		 0.90450853 -0.30901697 0.29389265 0.95105654 -0.30901697 0 0.93934804 -0.15643437 -0.30521268
		 0.79905719 -0.15643437 -0.580549 0.580549 -0.15643437 -0.79905713 0.30521265 -0.15643437 -0.93934792
		 0 -0.15643437 -0.98768884 -0.30521265 -0.15643437 -0.93934786;
	setAttr ".vt[166:331]" -0.58054888 -0.15643437 -0.79905695 -0.79905689 -0.15643437 -0.58054882
		 -0.93934768 -0.15643437 -0.30521256 -0.9876886 -0.15643437 0 -0.93934768 -0.15643437 0.30521256
		 -0.79905683 -0.15643437 0.58054876 -0.58054876 -0.15643437 0.79905677 -0.30521256 -0.15643437 0.93934757
		 -2.9435407e-008 -0.15643437 0.98768848 0.30521247 -0.15643437 0.93934757 0.58054864 -0.15643437 0.79905671
		 0.79905665 -0.15643437 0.5805487 0.93934751 -0.15643437 0.3052125 0.98768836 -0.15643437 0
		 0.95105714 0 -0.30901718 0.80901754 0 -0.5877856 0.5877856 0 -0.80901748 0.30901715 0 -0.95105702
		 0 0 -1.000000476837 -0.30901715 0 -0.95105696 -0.58778548 0 -0.8090173 -0.80901724 0 -0.58778542
		 -0.95105678 0 -0.30901706 -1.000000238419 0 0 -0.95105678 0 0.30901706 -0.80901718 0 0.58778536
		 -0.58778536 0 0.80901712 -0.30901706 0 0.95105666 -2.9802322e-008 0 1.000000119209
		 0.30901697 0 0.9510566 0.58778524 0 0.80901706 0.809017 0 0.5877853 0.95105654 0 0.309017
		 1 0 0 0.93934804 0.15643437 -0.30521268 0.79905719 0.15643437 -0.580549 0.580549 0.15643437 -0.79905713
		 0.30521265 0.15643437 -0.93934792 0 0.15643437 -0.98768884 -0.30521265 0.15643437 -0.93934786
		 -0.58054888 0.15643437 -0.79905695 -0.79905689 0.15643437 -0.58054882 -0.93934768 0.15643437 -0.30521256
		 -0.9876886 0.15643437 0 -0.93934768 0.15643437 0.30521256 -0.79905683 0.15643437 0.58054876
		 -0.58054876 0.15643437 0.79905677 -0.30521256 0.15643437 0.93934757 -2.9435407e-008 0.15643437 0.98768848
		 0.30521247 0.15643437 0.93934757 0.58054864 0.15643437 0.79905671 0.79905665 0.15643437 0.5805487
		 0.93934751 0.15643437 0.3052125 0.98768836 0.15643437 0 0.90450913 0.30901697 -0.2938928
		 0.7694214 0.30901697 -0.55901736 0.55901736 0.30901697 -0.76942134 0.29389277 0.30901697 -0.90450901
		 0 0.30901697 -0.95105702 -0.29389277 0.30901697 -0.90450895 -0.55901724 0.30901697 -0.76942122
		 -0.76942116 0.30901697 -0.55901718 -0.90450877 0.30901697 -0.29389271 -0.95105678 0.30901697 0
		 -0.90450877 0.30901697 0.29389271 -0.7694211 0.30901697 0.55901712 -0.55901712 0.30901697 0.76942104
		 -0.29389271 0.30901697 0.90450865 -2.8343694e-008 0.30901697 0.95105666 0.29389262 0.30901697 0.90450859
		 0.559017 0.30901697 0.76942098 0.76942092 0.30901697 0.55901706 0.90450853 0.30901697 0.29389265
		 0.95105654 0.30901697 0 0.8473981 0.45399052 -0.27533633 0.72083992 0.45399052 -0.5237208
		 0.5237208 0.45399052 -0.72083986 0.2753363 0.45399052 -0.84739798 0 0.45399052 -0.89100695
		 -0.2753363 0.45399052 -0.84739798 -0.52372068 0.45399052 -0.72083968 -0.72083962 0.45399052 -0.52372062
		 -0.8473978 0.45399052 -0.27533621 -0.89100677 0.45399052 0 -0.8473978 0.45399052 0.27533621
		 -0.72083962 0.45399052 0.52372062 -0.52372062 0.45399052 0.72083956 -0.27533621 0.45399052 0.84739769
		 -2.6554064e-008 0.45399052 0.89100665 0.27533615 0.45399052 0.84739763 0.5237205 0.45399052 0.7208395
		 0.72083944 0.45399052 0.52372056 0.84739757 0.45399052 0.27533618 0.89100653 0.45399052 0
		 0.7694214 0.58778524 -0.25000015 0.65450895 0.58778524 -0.47552854 0.47552854 0.58778524 -0.65450889
		 0.25000012 0.58778524 -0.76942128 0 0.58778524 -0.80901736 -0.25000012 0.58778524 -0.76942122
		 -0.47552845 0.58778524 -0.65450877 -0.65450871 0.58778524 -0.47552839 -0.7694211 0.58778524 -0.25000006
		 -0.80901718 0.58778524 0 -0.7694211 0.58778524 0.25000006 -0.65450865 0.58778524 0.47552836
		 -0.47552836 0.58778524 0.65450859 -0.25000006 0.58778524 0.76942098 -2.4110586e-008 0.58778524 0.80901712
		 0.24999999 0.58778524 0.76942098 0.47552827 0.58778524 0.65450853 0.65450853 0.58778524 0.4755283
		 0.76942092 0.58778524 0.25 0.809017 0.58778524 0 0.67249894 0.70710677 -0.21850814
		 0.57206178 0.70710677 -0.41562718 0.41562718 0.70710677 -0.57206172 0.21850812 0.70710677 -0.67249888
		 0 0.70710677 -0.70710713 -0.21850812 0.70710677 -0.67249882 -0.41562709 0.70710677 -0.5720616
		 -0.57206154 0.70710677 -0.41562706 -0.6724987 0.70710677 -0.21850805 -0.70710695 0.70710677 0
		 -0.6724987 0.70710677 0.21850805 -0.57206154 0.70710677 0.415627 -0.415627 0.70710677 0.57206148
		 -0.21850805 0.70710677 0.67249858 -2.1073424e-008 0.70710677 0.70710683 0.21850799 0.70710677 0.67249858
		 0.41562691 0.70710677 0.57206142 0.57206142 0.70710677 0.41562697 0.67249852 0.70710677 0.21850802
		 0.70710677 0.70710677 0 0.55901736 0.809017 -0.18163574 0.47552857 0.809017 -0.34549171
		 0.34549171 0.809017 -0.47552854 0.18163572 0.809017 -0.5590173 0 0.809017 -0.58778554
		 -0.18163572 0.809017 -0.55901724 -0.34549165 0.809017 -0.47552842 -0.47552839 0.809017 -0.34549159
		 -0.55901712 0.809017 -0.18163566 -0.58778536 0.809017 0 -0.55901712 0.809017 0.18163566
		 -0.47552836 0.809017 0.34549156 -0.34549156 0.809017 0.47552833 -0.18163566 0.809017 0.55901706
		 -1.7517365e-008 0.809017 0.5877853 0.18163562 0.809017 0.55901706 0.3454915 0.809017 0.4755283
		 0.47552827 0.809017 0.34549153 0.559017 0.809017 0.18163563 0.58778524 0.809017 0
		 0.43177092 0.89100653 -0.14029087 0.36728629 0.89100653 -0.2668491 0.2668491 0.89100653 -0.36728626
		 0.14029086 0.89100653 -0.43177086 0 0.89100653 -0.45399073 -0.14029086 0.89100653 -0.43177083
		 -0.26684904 0.89100653 -0.36728618 -0.36728615 0.89100653 -0.26684901 -0.43177077 0.89100653 -0.14029081
		 -0.45399064 0.89100653 0 -0.43177077 0.89100653 0.14029081 -0.36728612 0.89100653 0.26684898;
	setAttr ".vt[332:481]" -0.26684898 0.89100653 0.36728612 -0.14029081 0.89100653 0.43177071
		 -1.3529972e-008 0.89100653 0.45399058 0.14029078 0.89100653 0.43177068 0.26684892 0.89100653 0.36728609
		 0.36728606 0.89100653 0.26684895 0.43177065 0.89100653 0.1402908 0.45399052 0.89100653 0
		 0.29389283 0.95105654 -0.095491566 0.25000018 0.95105654 -0.18163574 0.18163574 0.95105654 -0.25000015
		 0.095491551 0.95105654 -0.2938928 0 0.95105654 -0.30901715 -0.095491551 0.95105654 -0.29389277
		 -0.18163571 0.95105654 -0.25000009 -0.25000009 0.95105654 -0.18163569 -0.29389271 0.95105654 -0.095491529
		 -0.30901706 0.95105654 0 -0.29389271 0.95105654 0.095491529 -0.25000006 0.95105654 0.18163568
		 -0.18163568 0.95105654 0.25000006 -0.095491529 0.95105654 0.29389268 -9.2094243e-009 0.95105654 0.30901703
		 0.095491499 0.95105654 0.29389265 0.18163563 0.95105654 0.25000003 0.25 0.95105654 0.18163565
		 0.29389265 0.95105654 0.095491506 0.309017 0.95105654 0 0.074013181 0.90744829 -0.024048347
		 0.062959358 0.90744829 -0.045742657 0.045742642 0.90744829 -0.062959366 0.02404831 0.90744829 -0.074013181
		 -1.1232345e-008 0.90744829 -0.077822074 -0.02404834 0.90744829 -0.074013166 -0.045742642 0.90744829 -0.062959351
		 -0.062959343 0.90744829 -0.045742631 -0.074013151 0.90744829 -0.024048338 -0.077822059 0.90744829 -1.4976463e-008
		 -0.074013151 0.90744829 0.024048302 -0.062959343 0.90744829 0.045742616 -0.045742624 0.90744829 0.062959306
		 -0.02404833 0.90744829 0.074013121 -1.3551624e-008 0.90744829 0.077822015 0.024048302 0.90744829 0.074013107
		 0.045742605 0.90744829 0.062959298 0.062959298 0.90744829 0.045742609 0.074013114 0.90744829 0.024048302
		 0.077822015 0.90744829 -1.4976463e-008 0 -1 0 0 0.90744781 1.880618e-016 -0.09399756 0.90744829 -0.12937658
		 -0.049417417 0.90744829 -0.15209126 -2.338699e-009 0.90744829 -0.15991819 0.049417414 0.90744829 -0.15209126
		 0.093997605 0.90744829 -0.12937661 0.12937665 0.90744829 -0.093997583 0.15209131 0.90744829 -0.049417417
		 0.15991813 0.90744829 -2.338699e-009 0.15209121 0.90744829 0.049417406 0.12937652 0.90744829 0.093997531
		 0.093997523 0.90744829 0.12937652 0.049417403 0.90744829 0.15209121 -7.1046058e-009 0.90744829 0.15991813
		 -0.049417417 0.90744829 0.15209121 -0.093997546 0.90744829 0.12937655 -0.12937656 0.90744829 0.093997538
		 -0.15209123 0.90744829 0.04941741 -0.15991819 0.90744829 -2.338699e-009 -0.15209123 0.90744829 -0.049417417
		 -0.12937656 0.90744829 -0.093997546 -0.13132975 0.93421751 -0.18075988 -0.069044143 0.93421751 -0.21249601
		 -1.6653345e-016 0.93421751 -0.22343153 0.069044143 0.93421751 -0.21249604 0.13132977 0.93421751 -0.18075994
		 0.18075994 0.93421751 -0.13132977 0.21249604 0.93421751 -0.06904415 0.22343142 0.93421751 -3.6056975e-017
		 0.21249592 0.93421751 0.069044113 0.18075982 0.93421751 0.13132972 0.1313297 0.93421751 0.18075982
		 0.069044098 0.93421751 0.21249592 -6.6587753e-009 0.93421751 0.22343144 -0.069044128 0.93421751 0.21249595
		 -0.13132972 0.93421751 0.18075986 -0.18075986 0.93421751 0.13132972 -0.21249597 0.93421751 0.069044128
		 -0.22343148 0.93421751 -3.6056975e-017 -0.21249597 0.93421751 -0.069044128 -0.18075988 0.93421751 -0.13132975
		 -0.163038 0.95865273 -0.22440255 -0.085714161 0.95865273 -0.26380107 0 0.95865273 -0.27737686
		 0.085714161 0.95865273 -0.2638011 0.16303805 0.95865273 -0.22440261 0.22440264 0.95865273 -0.16303805
		 0.2638011 0.95865273 -0.085714176 0.27737671 0.95865273 0 0.26380095 0.95865273 0.085714124
		 0.22440247 0.95865273 0.16303796 0.16303794 0.95865273 0.22440249 0.085714109 0.95865273 0.26380095
		 -8.2664702e-009 0.95865273 0.27737674 -0.085714139 0.95865273 0.26380098 -0.16303799 0.95865273 0.22440253
		 -0.22440253 0.95865273 0.16303799 -0.26380101 0.95865273 0.085714139 -0.27737677 0.95865273 0
		 -0.26380101 0.95865273 -0.085714139 -0.22440255 0.95865273 -0.163038 -0.17055327 0.95558316 -0.23474643
		 -0.089665174 0.95558316 -0.27596104 2.7755576e-017 0.95558316 -0.29016259 0.089665174 0.95558316 -0.27596104
		 0.17055331 0.95558316 -0.23474649 0.23474652 0.95558316 -0.17055331 0.27596107 0.95558316 -0.089665189
		 0.29016244 0.95558316 5.5511151e-017 0.27596089 0.95558316 0.08966513 0.23474635 0.95558316 0.17055322
		 0.17055321 0.95558316 0.23474637 0.089665115 0.95558316 0.27596089 -8.6475147e-009 0.95558316 0.29016247
		 -0.089665145 0.95558316 0.27596095 -0.17055325 0.95558316 0.2347464 -0.2347464 0.95558316 0.17055325
		 -0.27596098 0.95558316 0.089665145 -0.2901625 0.95558316 5.5511151e-017 -0.27596098 0.95558316 -0.089665145
		 -0.23474643 0.95558316 -0.17055327 0.08941479 0.90744829 0.12306891 0.047008116 0.90744829 0.14467618
		 -7.7168743e-009 0.90744829 0.15212151 -0.047008134 0.90744829 0.14467618 -0.089414813 0.90744829 0.12306895
		 -0.12306897 0.90744829 0.089414805 -0.14467622 0.90744829 0.047008123 -0.15212159 0.90744829 -3.5388981e-009
		 -0.14467622 0.90744829 -0.047008134 -0.12306897 0.90744829 -0.089414813 -0.08941482 0.90744829 -0.12306898
		 -0.047008134 0.90744829 -0.14467625 -3.1833221e-009 0.90744829 -0.15212159 0.047008127 0.90744829 -0.14467625
		 0.089414865 0.90744829 -0.123069 0.12306905 0.90744829 -0.08941485 0.14467628 0.90744829 -0.047008134
		 0.15212151 0.90744829 -3.5388981e-009 0.14467618 0.90744829 0.04700812 0.12306891 0.90744829 0.089414798;
	setAttr -s 980 ".ed";
	setAttr ".ed[0:165]"  0 1 1 1 2 1 2 3 1 3 4 1 4 5 1 5 6 1 6 7 1 7 8 1 8 9 1
		 9 10 1 10 11 1 11 12 1 12 13 1 13 14 1 14 15 1 15 16 1 16 17 1 17 18 1 18 19 1 19 0 1
		 20 21 1 21 22 1 22 23 1 23 24 1 24 25 1 25 26 1 26 27 1 27 28 1 28 29 1 29 30 1 30 31 1
		 31 32 1 32 33 1 33 34 1 34 35 1 35 36 1 36 37 1 37 38 1 38 39 1 39 20 1 40 41 1 41 42 1
		 42 43 1 43 44 1 44 45 1 45 46 1 46 47 1 47 48 1 48 49 1 49 50 1 50 51 1 51 52 1 52 53 1
		 53 54 1 54 55 1 55 56 1 56 57 1 57 58 1 58 59 1 59 40 1 60 61 1 61 62 1 62 63 1 63 64 1
		 64 65 1 65 66 1 66 67 1 67 68 1 68 69 1 69 70 1 70 71 1 71 72 1 72 73 1 73 74 1 74 75 1
		 75 76 1 76 77 1 77 78 1 78 79 1 79 60 1 80 81 1 81 82 1 82 83 1 83 84 1 84 85 1 85 86 1
		 86 87 1 87 88 1 88 89 1 89 90 1 90 91 1 91 92 1 92 93 1 93 94 1 94 95 1 95 96 1 96 97 1
		 97 98 1 98 99 1 99 80 1 100 101 1 101 102 1 102 103 1 103 104 1 104 105 1 105 106 1
		 106 107 1 107 108 1 108 109 1 109 110 1 110 111 1 111 112 1 112 113 1 113 114 1 114 115 1
		 115 116 1 116 117 1 117 118 1 118 119 1 119 100 1 120 121 1 121 122 1 122 123 1 123 124 1
		 124 125 1 125 126 1 126 127 1 127 128 1 128 129 1 129 130 1 130 131 1 131 132 1 132 133 1
		 133 134 1 134 135 1 135 136 1 136 137 1 137 138 1 138 139 1 139 120 1 140 141 1 141 142 1
		 142 143 1 143 144 1 144 145 1 145 146 1 146 147 1 147 148 1 148 149 1 149 150 1 150 151 1
		 151 152 1 152 153 1 153 154 1 154 155 1 155 156 1 156 157 1 157 158 1 158 159 1 159 140 1
		 160 161 1 161 162 1 162 163 1 163 164 1 164 165 1 165 166 1;
	setAttr ".ed[166:331]" 166 167 1 167 168 1 168 169 1 169 170 1 170 171 1 171 172 1
		 172 173 1 173 174 1 174 175 1 175 176 1 176 177 1 177 178 1 178 179 1 179 160 1 180 181 1
		 181 182 1 182 183 1 183 184 1 184 185 1 185 186 1 186 187 1 187 188 1 188 189 1 189 190 1
		 190 191 1 191 192 1 192 193 1 193 194 1 194 195 1 195 196 1 196 197 1 197 198 1 198 199 1
		 199 180 1 200 201 1 201 202 1 202 203 1 203 204 1 204 205 1 205 206 1 206 207 1 207 208 1
		 208 209 1 209 210 1 210 211 1 211 212 1 212 213 1 213 214 1 214 215 1 215 216 1 216 217 1
		 217 218 1 218 219 1 219 200 1 220 221 1 221 222 1 222 223 1 223 224 1 224 225 1 225 226 1
		 226 227 1 227 228 1 228 229 1 229 230 1 230 231 1 231 232 1 232 233 1 233 234 1 234 235 1
		 235 236 1 236 237 1 237 238 1 238 239 1 239 220 1 240 241 1 241 242 1 242 243 1 243 244 1
		 244 245 1 245 246 1 246 247 1 247 248 1 248 249 1 249 250 1 250 251 1 251 252 1 252 253 1
		 253 254 1 254 255 1 255 256 1 256 257 1 257 258 1 258 259 1 259 240 1 260 261 1 261 262 1
		 262 263 1 263 264 1 264 265 1 265 266 1 266 267 1 267 268 1 268 269 1 269 270 1 270 271 1
		 271 272 1 272 273 1 273 274 1 274 275 1 275 276 1 276 277 1 277 278 1 278 279 1 279 260 1
		 280 281 1 281 282 1 282 283 1 283 284 1 284 285 1 285 286 1 286 287 1 287 288 1 288 289 1
		 289 290 1 290 291 1 291 292 1 292 293 1 293 294 1 294 295 1 295 296 1 296 297 1 297 298 1
		 298 299 1 299 280 1 300 301 1 301 302 1 302 303 1 303 304 1 304 305 1 305 306 1 306 307 1
		 307 308 1 308 309 1 309 310 1 310 311 1 311 312 1 312 313 1 313 314 1 314 315 1 315 316 1
		 316 317 1 317 318 1 318 319 1 319 300 1 320 321 1 321 322 1 322 323 1 323 324 1 324 325 1
		 325 326 1 326 327 1 327 328 1 328 329 1 329 330 1 330 331 1 331 332 1;
	setAttr ".ed[332:497]" 332 333 1 333 334 1 334 335 1 335 336 1 336 337 1 337 338 1
		 338 339 1 339 320 1 340 341 1 341 342 1 342 343 1 343 344 1 344 345 1 345 346 1 346 347 1
		 347 348 1 348 349 1 349 350 1 350 351 1 351 352 1 352 353 1 353 354 1 354 355 1 355 356 1
		 356 357 1 357 358 1 358 359 1 359 340 1 360 361 1 361 362 1 362 363 1 363 364 1 364 365 1
		 365 366 1 366 367 1 367 368 1 368 369 1 369 370 1 370 371 1 371 372 1 372 373 1 373 374 1
		 374 375 1 375 376 1 376 377 1 377 378 1 378 379 1 379 360 1 0 20 1 1 21 1 2 22 1
		 3 23 1 4 24 1 5 25 1 6 26 1 7 27 1 8 28 1 9 29 1 10 30 1 11 31 1 12 32 1 13 33 1
		 14 34 1 15 35 1 16 36 1 17 37 1 18 38 1 19 39 1 20 40 1 21 41 1 22 42 1 23 43 1 24 44 1
		 25 45 1 26 46 1 27 47 1 28 48 1 29 49 1 30 50 1 31 51 1 32 52 1 33 53 1 34 54 1 35 55 1
		 36 56 1 37 57 1 38 58 1 39 59 1 40 60 1 41 61 1 42 62 1 43 63 1 44 64 1 45 65 1 46 66 1
		 47 67 1 48 68 1 49 69 1 50 70 1 51 71 1 52 72 1 53 73 1 54 74 1 55 75 1 56 76 1 57 77 1
		 58 78 1 59 79 1 60 80 1 61 81 1 62 82 1 63 83 1 64 84 1 65 85 1 66 86 1 67 87 1 68 88 1
		 69 89 1 70 90 1 71 91 1 72 92 1 73 93 1 74 94 1 75 95 1 76 96 1 77 97 1 78 98 1 79 99 1
		 80 100 1 81 101 1 82 102 1 83 103 1 84 104 1 85 105 1 86 106 1 87 107 1 88 108 1
		 89 109 1 90 110 1 91 111 1 92 112 1 93 113 1 94 114 1 95 115 1 96 116 1 97 117 1
		 98 118 1 99 119 1 100 120 1 101 121 1 102 122 1 103 123 1 104 124 1 105 125 1 106 126 1
		 107 127 1 108 128 1 109 129 1 110 130 1 111 131 1 112 132 1 113 133 1 114 134 1 115 135 1
		 116 136 1 117 137 1;
	setAttr ".ed[498:663]" 118 138 1 119 139 1 120 140 1 121 141 1 122 142 1 123 143 1
		 124 144 1 125 145 1 126 146 1 127 147 1 128 148 1 129 149 1 130 150 1 131 151 1 132 152 1
		 133 153 1 134 154 1 135 155 1 136 156 1 137 157 1 138 158 1 139 159 1 140 160 1 141 161 1
		 142 162 1 143 163 1 144 164 1 145 165 1 146 166 1 147 167 1 148 168 1 149 169 1 150 170 1
		 151 171 1 152 172 1 153 173 1 154 174 1 155 175 1 156 176 1 157 177 1 158 178 1 159 179 1
		 160 180 1 161 181 1 162 182 1 163 183 1 164 184 1 165 185 1 166 186 1 167 187 1 168 188 1
		 169 189 1 170 190 1 171 191 1 172 192 1 173 193 1 174 194 1 175 195 1 176 196 1 177 197 1
		 178 198 1 179 199 1 180 200 1 181 201 1 182 202 1 183 203 1 184 204 1 185 205 1 186 206 1
		 187 207 1 188 208 1 189 209 1 190 210 1 191 211 1 192 212 1 193 213 1 194 214 1 195 215 1
		 196 216 1 197 217 1 198 218 1 199 219 1 200 220 1 201 221 1 202 222 1 203 223 1 204 224 1
		 205 225 1 206 226 1 207 227 1 208 228 1 209 229 1 210 230 1 211 231 1 212 232 1 213 233 1
		 214 234 1 215 235 1 216 236 1 217 237 1 218 238 1 219 239 1 220 240 1 221 241 1 222 242 1
		 223 243 1 224 244 1 225 245 1 226 246 1 227 247 1 228 248 1 229 249 1 230 250 1 231 251 1
		 232 252 1 233 253 1 234 254 1 235 255 1 236 256 1 237 257 1 238 258 1 239 259 1 240 260 1
		 241 261 1 242 262 1 243 263 1 244 264 1 245 265 1 246 266 1 247 267 1 248 268 1 249 269 1
		 250 270 1 251 271 1 252 272 1 253 273 1 254 274 1 255 275 1 256 276 1 257 277 1 258 278 1
		 259 279 1 260 280 1 261 281 1 262 282 1 263 283 1 264 284 1 265 285 1 266 286 1 267 287 1
		 268 288 1 269 289 1 270 290 1 271 291 1 272 292 1 273 293 1 274 294 1 275 295 1 276 296 1
		 277 297 1 278 298 1 279 299 1 280 300 1 281 301 1 282 302 1 283 303 1;
	setAttr ".ed[664:829]" 284 304 1 285 305 1 286 306 1 287 307 1 288 308 1 289 309 1
		 290 310 1 291 311 1 292 312 1 293 313 1 294 314 1 295 315 1 296 316 1 297 317 1 298 318 1
		 299 319 1 300 320 1 301 321 1 302 322 1 303 323 1 304 324 1 305 325 1 306 326 1 307 327 1
		 308 328 1 309 329 1 310 330 1 311 331 1 312 332 1 313 333 1 314 334 1 315 335 1 316 336 1
		 317 337 1 318 338 1 319 339 1 320 340 1 321 341 1 322 342 1 323 343 1 324 344 1 325 345 1
		 326 346 1 327 347 1 328 348 1 329 349 1 330 350 1 331 351 1 332 352 1 333 353 1 334 354 1
		 335 355 1 336 356 1 337 357 1 338 358 1 339 359 1 340 448 1 341 447 1 342 446 1 343 445 1
		 344 444 1 345 443 1 346 442 1 347 461 1 348 460 1 349 459 1 350 458 1 351 457 1 352 456 1
		 353 455 1 354 454 1 355 453 1 356 452 1 357 451 1 358 450 1 359 449 1 380 0 1 380 1 1
		 380 2 1 380 3 1 380 4 1 380 5 1 380 6 1 380 7 1 380 8 1 380 9 1 380 10 1 380 11 1
		 380 12 1 380 13 1 380 14 1 380 15 1 380 16 1 380 17 1 380 18 1 380 19 1 360 381 1
		 361 381 1 362 381 1 363 381 1 364 381 1 365 381 1 366 381 1 367 381 1 368 381 1 369 381 1
		 370 381 1 371 381 1 372 381 1 373 381 1 374 381 1 375 381 1 376 381 1 377 381 1 378 381 1
		 379 381 1 382 472 1 383 473 1 382 383 1 384 474 1 383 384 1 385 475 1 384 385 1 386 476 1
		 385 386 1 387 477 1 386 387 1 388 478 1 387 388 1 389 479 1 388 389 1 390 480 1 389 390 1
		 391 481 1 390 391 1 392 462 1 391 392 1 393 463 1 392 393 1 394 464 1 393 394 1 395 465 1
		 394 395 1 396 466 1 395 396 1 397 467 1 396 397 1 398 468 1 397 398 1 399 469 1 398 399 1
		 400 470 1 399 400 1 401 471 1 400 401 1 401 382 1 402 382 1 403 383 1 402 403 1 404 384 1
		 403 404 1 405 385 1 404 405 1 406 386 1 405 406 1 407 387 1;
	setAttr ".ed[830:979]" 406 407 1 408 388 1 407 408 1 409 389 1 408 409 1 410 390 1
		 409 410 1 411 391 1 410 411 1 412 392 1 411 412 1 413 393 1 412 413 1 414 394 1 413 414 1
		 415 395 1 414 415 1 416 396 1 415 416 1 417 397 1 416 417 1 418 398 1 417 418 1 419 399 1
		 418 419 1 420 400 1 419 420 1 421 401 1 420 421 1 421 402 1 422 402 1 423 403 1 422 423 1
		 424 404 1 423 424 1 425 405 1 424 425 1 426 406 1 425 426 1 427 407 1 426 427 1 428 408 1
		 427 428 1 429 409 1 428 429 1 430 410 1 429 430 1 431 411 1 430 431 1 432 412 1 431 432 1
		 433 413 1 432 433 1 434 414 1 433 434 1 435 415 1 434 435 1 436 416 1 435 436 1 437 417 1
		 436 437 1 438 418 1 437 438 1 439 419 1 438 439 1 440 420 1 439 440 1 441 421 1 440 441 1
		 441 422 1 442 422 1 443 423 1 442 443 1 444 424 1 443 444 1 445 425 1 444 445 1 446 426 1
		 445 446 1 447 427 1 446 447 1 448 428 1 447 448 1 449 429 1 448 449 1 450 430 1 449 450 1
		 451 431 1 450 451 1 452 432 1 451 452 1 453 433 1 452 453 1 454 434 1 453 454 1 455 435 1
		 454 455 1 456 436 1 455 456 1 457 437 1 456 457 1 458 438 1 457 458 1 459 439 1 458 459 1
		 460 440 1 459 460 1 461 441 1 460 461 1 461 442 1 462 376 1 463 375 1 462 463 1 464 374 1
		 463 464 1 465 373 1 464 465 1 466 372 1 465 466 1 467 371 1 466 467 1 468 370 1 467 468 1
		 469 369 1 468 469 1 470 368 1 469 470 1 471 367 1 470 471 1 472 366 1 471 472 1 473 365 1
		 472 473 1 474 364 1 473 474 1 475 363 1 474 475 1 476 362 1 475 476 1 477 361 1 476 477 1
		 478 360 1 477 478 1 479 379 1 478 479 1 480 378 1 479 480 1 481 377 1 480 481 1 481 462 1;
	setAttr -s 500 ".fc[0:499]" -type "polyFaces" 
		f 4 0 381 -21 -381
		mu 0 4 0 1 22 21
		f 4 1 382 -22 -382
		mu 0 4 1 2 23 22
		f 4 2 383 -23 -383
		mu 0 4 2 3 24 23
		f 4 3 384 -24 -384
		mu 0 4 3 4 25 24
		f 4 4 385 -25 -385
		mu 0 4 4 5 26 25
		f 4 5 386 -26 -386
		mu 0 4 5 6 27 26
		f 4 6 387 -27 -387
		mu 0 4 6 7 28 27
		f 4 7 388 -28 -388
		mu 0 4 7 8 29 28
		f 4 8 389 -29 -389
		mu 0 4 8 9 30 29
		f 4 9 390 -30 -390
		mu 0 4 9 10 31 30
		f 4 10 391 -31 -391
		mu 0 4 10 11 32 31
		f 4 11 392 -32 -392
		mu 0 4 11 12 33 32
		f 4 12 393 -33 -393
		mu 0 4 12 13 34 33
		f 4 13 394 -34 -394
		mu 0 4 13 14 35 34
		f 4 14 395 -35 -395
		mu 0 4 14 15 36 35
		f 4 15 396 -36 -396
		mu 0 4 15 16 37 36
		f 4 16 397 -37 -397
		mu 0 4 16 17 38 37
		f 4 17 398 -38 -398
		mu 0 4 17 18 39 38
		f 4 18 399 -39 -399
		mu 0 4 18 19 40 39
		f 4 19 380 -40 -400
		mu 0 4 19 20 41 40
		f 4 20 401 -41 -401
		mu 0 4 21 22 43 42
		f 4 21 402 -42 -402
		mu 0 4 22 23 44 43
		f 4 22 403 -43 -403
		mu 0 4 23 24 45 44
		f 4 23 404 -44 -404
		mu 0 4 24 25 46 45
		f 4 24 405 -45 -405
		mu 0 4 25 26 47 46
		f 4 25 406 -46 -406
		mu 0 4 26 27 48 47
		f 4 26 407 -47 -407
		mu 0 4 27 28 49 48
		f 4 27 408 -48 -408
		mu 0 4 28 29 50 49
		f 4 28 409 -49 -409
		mu 0 4 29 30 51 50
		f 4 29 410 -50 -410
		mu 0 4 30 31 52 51
		f 4 30 411 -51 -411
		mu 0 4 31 32 53 52
		f 4 31 412 -52 -412
		mu 0 4 32 33 54 53
		f 4 32 413 -53 -413
		mu 0 4 33 34 55 54
		f 4 33 414 -54 -414
		mu 0 4 34 35 56 55
		f 4 34 415 -55 -415
		mu 0 4 35 36 57 56
		f 4 35 416 -56 -416
		mu 0 4 36 37 58 57
		f 4 36 417 -57 -417
		mu 0 4 37 38 59 58
		f 4 37 418 -58 -418
		mu 0 4 38 39 60 59
		f 4 38 419 -59 -419
		mu 0 4 39 40 61 60
		f 4 39 400 -60 -420
		mu 0 4 40 41 62 61
		f 4 40 421 -61 -421
		mu 0 4 42 43 64 63
		f 4 41 422 -62 -422
		mu 0 4 43 44 65 64
		f 4 42 423 -63 -423
		mu 0 4 44 45 66 65
		f 4 43 424 -64 -424
		mu 0 4 45 46 67 66
		f 4 44 425 -65 -425
		mu 0 4 46 47 68 67
		f 4 45 426 -66 -426
		mu 0 4 47 48 69 68
		f 4 46 427 -67 -427
		mu 0 4 48 49 70 69
		f 4 47 428 -68 -428
		mu 0 4 49 50 71 70
		f 4 48 429 -69 -429
		mu 0 4 50 51 72 71
		f 4 49 430 -70 -430
		mu 0 4 51 52 73 72
		f 4 50 431 -71 -431
		mu 0 4 52 53 74 73
		f 4 51 432 -72 -432
		mu 0 4 53 54 75 74
		f 4 52 433 -73 -433
		mu 0 4 54 55 76 75
		f 4 53 434 -74 -434
		mu 0 4 55 56 77 76
		f 4 54 435 -75 -435
		mu 0 4 56 57 78 77
		f 4 55 436 -76 -436
		mu 0 4 57 58 79 78
		f 4 56 437 -77 -437
		mu 0 4 58 59 80 79
		f 4 57 438 -78 -438
		mu 0 4 59 60 81 80
		f 4 58 439 -79 -439
		mu 0 4 60 61 82 81
		f 4 59 420 -80 -440
		mu 0 4 61 62 83 82
		f 4 60 441 -81 -441
		mu 0 4 63 64 85 84
		f 4 61 442 -82 -442
		mu 0 4 64 65 86 85
		f 4 62 443 -83 -443
		mu 0 4 65 66 87 86
		f 4 63 444 -84 -444
		mu 0 4 66 67 88 87
		f 4 64 445 -85 -445
		mu 0 4 67 68 89 88
		f 4 65 446 -86 -446
		mu 0 4 68 69 90 89
		f 4 66 447 -87 -447
		mu 0 4 69 70 91 90
		f 4 67 448 -88 -448
		mu 0 4 70 71 92 91
		f 4 68 449 -89 -449
		mu 0 4 71 72 93 92
		f 4 69 450 -90 -450
		mu 0 4 72 73 94 93
		f 4 70 451 -91 -451
		mu 0 4 73 74 95 94
		f 4 71 452 -92 -452
		mu 0 4 74 75 96 95
		f 4 72 453 -93 -453
		mu 0 4 75 76 97 96
		f 4 73 454 -94 -454
		mu 0 4 76 77 98 97
		f 4 74 455 -95 -455
		mu 0 4 77 78 99 98
		f 4 75 456 -96 -456
		mu 0 4 78 79 100 99
		f 4 76 457 -97 -457
		mu 0 4 79 80 101 100
		f 4 77 458 -98 -458
		mu 0 4 80 81 102 101
		f 4 78 459 -99 -459
		mu 0 4 81 82 103 102
		f 4 79 440 -100 -460
		mu 0 4 82 83 104 103
		f 4 80 461 -101 -461
		mu 0 4 84 85 106 105
		f 4 81 462 -102 -462
		mu 0 4 85 86 107 106
		f 4 82 463 -103 -463
		mu 0 4 86 87 108 107
		f 4 83 464 -104 -464
		mu 0 4 87 88 109 108
		f 4 84 465 -105 -465
		mu 0 4 88 89 110 109
		f 4 85 466 -106 -466
		mu 0 4 89 90 111 110
		f 4 86 467 -107 -467
		mu 0 4 90 91 112 111
		f 4 87 468 -108 -468
		mu 0 4 91 92 113 112
		f 4 88 469 -109 -469
		mu 0 4 92 93 114 113
		f 4 89 470 -110 -470
		mu 0 4 93 94 115 114
		f 4 90 471 -111 -471
		mu 0 4 94 95 116 115
		f 4 91 472 -112 -472
		mu 0 4 95 96 117 116
		f 4 92 473 -113 -473
		mu 0 4 96 97 118 117
		f 4 93 474 -114 -474
		mu 0 4 97 98 119 118
		f 4 94 475 -115 -475
		mu 0 4 98 99 120 119
		f 4 95 476 -116 -476
		mu 0 4 99 100 121 120
		f 4 96 477 -117 -477
		mu 0 4 100 101 122 121
		f 4 97 478 -118 -478
		mu 0 4 101 102 123 122
		f 4 98 479 -119 -479
		mu 0 4 102 103 124 123
		f 4 99 460 -120 -480
		mu 0 4 103 104 125 124
		f 4 100 481 -121 -481
		mu 0 4 105 106 127 126
		f 4 101 482 -122 -482
		mu 0 4 106 107 128 127
		f 4 102 483 -123 -483
		mu 0 4 107 108 129 128
		f 4 103 484 -124 -484
		mu 0 4 108 109 130 129
		f 4 104 485 -125 -485
		mu 0 4 109 110 131 130
		f 4 105 486 -126 -486
		mu 0 4 110 111 132 131
		f 4 106 487 -127 -487
		mu 0 4 111 112 133 132
		f 4 107 488 -128 -488
		mu 0 4 112 113 134 133
		f 4 108 489 -129 -489
		mu 0 4 113 114 135 134
		f 4 109 490 -130 -490
		mu 0 4 114 115 136 135
		f 4 110 491 -131 -491
		mu 0 4 115 116 137 136
		f 4 111 492 -132 -492
		mu 0 4 116 117 138 137
		f 4 112 493 -133 -493
		mu 0 4 117 118 139 138
		f 4 113 494 -134 -494
		mu 0 4 118 119 140 139
		f 4 114 495 -135 -495
		mu 0 4 119 120 141 140
		f 4 115 496 -136 -496
		mu 0 4 120 121 142 141
		f 4 116 497 -137 -497
		mu 0 4 121 122 143 142
		f 4 117 498 -138 -498
		mu 0 4 122 123 144 143
		f 4 118 499 -139 -499
		mu 0 4 123 124 145 144
		f 4 119 480 -140 -500
		mu 0 4 124 125 146 145
		f 4 120 501 -141 -501
		mu 0 4 126 127 148 147
		f 4 121 502 -142 -502
		mu 0 4 127 128 149 148
		f 4 122 503 -143 -503
		mu 0 4 128 129 150 149
		f 4 123 504 -144 -504
		mu 0 4 129 130 151 150
		f 4 124 505 -145 -505
		mu 0 4 130 131 152 151
		f 4 125 506 -146 -506
		mu 0 4 131 132 153 152
		f 4 126 507 -147 -507
		mu 0 4 132 133 154 153
		f 4 127 508 -148 -508
		mu 0 4 133 134 155 154
		f 4 128 509 -149 -509
		mu 0 4 134 135 156 155
		f 4 129 510 -150 -510
		mu 0 4 135 136 157 156
		f 4 130 511 -151 -511
		mu 0 4 136 137 158 157
		f 4 131 512 -152 -512
		mu 0 4 137 138 159 158
		f 4 132 513 -153 -513
		mu 0 4 138 139 160 159
		f 4 133 514 -154 -514
		mu 0 4 139 140 161 160
		f 4 134 515 -155 -515
		mu 0 4 140 141 162 161
		f 4 135 516 -156 -516
		mu 0 4 141 142 163 162
		f 4 136 517 -157 -517
		mu 0 4 142 143 164 163
		f 4 137 518 -158 -518
		mu 0 4 143 144 165 164
		f 4 138 519 -159 -519
		mu 0 4 144 145 166 165
		f 4 139 500 -160 -520
		mu 0 4 145 146 167 166
		f 4 140 521 -161 -521
		mu 0 4 147 148 169 168
		f 4 141 522 -162 -522
		mu 0 4 148 149 170 169
		f 4 142 523 -163 -523
		mu 0 4 149 150 171 170
		f 4 143 524 -164 -524
		mu 0 4 150 151 172 171
		f 4 144 525 -165 -525
		mu 0 4 151 152 173 172
		f 4 145 526 -166 -526
		mu 0 4 152 153 174 173
		f 4 146 527 -167 -527
		mu 0 4 153 154 175 174
		f 4 147 528 -168 -528
		mu 0 4 154 155 176 175
		f 4 148 529 -169 -529
		mu 0 4 155 156 177 176
		f 4 149 530 -170 -530
		mu 0 4 156 157 178 177
		f 4 150 531 -171 -531
		mu 0 4 157 158 179 178
		f 4 151 532 -172 -532
		mu 0 4 158 159 180 179
		f 4 152 533 -173 -533
		mu 0 4 159 160 181 180
		f 4 153 534 -174 -534
		mu 0 4 160 161 182 181
		f 4 154 535 -175 -535
		mu 0 4 161 162 183 182
		f 4 155 536 -176 -536
		mu 0 4 162 163 184 183
		f 4 156 537 -177 -537
		mu 0 4 163 164 185 184
		f 4 157 538 -178 -538
		mu 0 4 164 165 186 185
		f 4 158 539 -179 -539
		mu 0 4 165 166 187 186
		f 4 159 520 -180 -540
		mu 0 4 166 167 188 187
		f 4 160 541 -181 -541
		mu 0 4 168 169 190 189
		f 4 161 542 -182 -542
		mu 0 4 169 170 191 190
		f 4 162 543 -183 -543
		mu 0 4 170 171 192 191
		f 4 163 544 -184 -544
		mu 0 4 171 172 193 192
		f 4 164 545 -185 -545
		mu 0 4 172 173 194 193
		f 4 165 546 -186 -546
		mu 0 4 173 174 195 194
		f 4 166 547 -187 -547
		mu 0 4 174 175 196 195
		f 4 167 548 -188 -548
		mu 0 4 175 176 197 196
		f 4 168 549 -189 -549
		mu 0 4 176 177 198 197
		f 4 169 550 -190 -550
		mu 0 4 177 178 199 198
		f 4 170 551 -191 -551
		mu 0 4 178 179 200 199
		f 4 171 552 -192 -552
		mu 0 4 179 180 201 200
		f 4 172 553 -193 -553
		mu 0 4 180 181 202 201
		f 4 173 554 -194 -554
		mu 0 4 181 182 203 202
		f 4 174 555 -195 -555
		mu 0 4 182 183 204 203
		f 4 175 556 -196 -556
		mu 0 4 183 184 205 204
		f 4 176 557 -197 -557
		mu 0 4 184 185 206 205
		f 4 177 558 -198 -558
		mu 0 4 185 186 207 206
		f 4 178 559 -199 -559
		mu 0 4 186 187 208 207
		f 4 179 540 -200 -560
		mu 0 4 187 188 209 208
		f 4 180 561 -201 -561
		mu 0 4 189 190 211 210
		f 4 181 562 -202 -562
		mu 0 4 190 191 212 211
		f 4 182 563 -203 -563
		mu 0 4 191 192 213 212
		f 4 183 564 -204 -564
		mu 0 4 192 193 214 213
		f 4 184 565 -205 -565
		mu 0 4 193 194 215 214
		f 4 185 566 -206 -566
		mu 0 4 194 195 216 215
		f 4 186 567 -207 -567
		mu 0 4 195 196 217 216
		f 4 187 568 -208 -568
		mu 0 4 196 197 218 217
		f 4 188 569 -209 -569
		mu 0 4 197 198 219 218
		f 4 189 570 -210 -570
		mu 0 4 198 199 220 219
		f 4 190 571 -211 -571
		mu 0 4 199 200 221 220
		f 4 191 572 -212 -572
		mu 0 4 200 201 222 221
		f 4 192 573 -213 -573
		mu 0 4 201 202 223 222
		f 4 193 574 -214 -574
		mu 0 4 202 203 224 223
		f 4 194 575 -215 -575
		mu 0 4 203 204 225 224
		f 4 195 576 -216 -576
		mu 0 4 204 205 226 225
		f 4 196 577 -217 -577
		mu 0 4 205 206 227 226
		f 4 197 578 -218 -578
		mu 0 4 206 207 228 227
		f 4 198 579 -219 -579
		mu 0 4 207 208 229 228
		f 4 199 560 -220 -580
		mu 0 4 208 209 230 229
		f 4 200 581 -221 -581
		mu 0 4 210 211 232 231
		f 4 201 582 -222 -582
		mu 0 4 211 212 233 232
		f 4 202 583 -223 -583
		mu 0 4 212 213 234 233
		f 4 203 584 -224 -584
		mu 0 4 213 214 235 234
		f 4 204 585 -225 -585
		mu 0 4 214 215 236 235
		f 4 205 586 -226 -586
		mu 0 4 215 216 237 236
		f 4 206 587 -227 -587
		mu 0 4 216 217 238 237
		f 4 207 588 -228 -588
		mu 0 4 217 218 239 238
		f 4 208 589 -229 -589
		mu 0 4 218 219 240 239
		f 4 209 590 -230 -590
		mu 0 4 219 220 241 240
		f 4 210 591 -231 -591
		mu 0 4 220 221 242 241
		f 4 211 592 -232 -592
		mu 0 4 221 222 243 242
		f 4 212 593 -233 -593
		mu 0 4 222 223 244 243
		f 4 213 594 -234 -594
		mu 0 4 223 224 245 244
		f 4 214 595 -235 -595
		mu 0 4 224 225 246 245
		f 4 215 596 -236 -596
		mu 0 4 225 226 247 246
		f 4 216 597 -237 -597
		mu 0 4 226 227 248 247
		f 4 217 598 -238 -598
		mu 0 4 227 228 249 248
		f 4 218 599 -239 -599
		mu 0 4 228 229 250 249
		f 4 219 580 -240 -600
		mu 0 4 229 230 251 250
		f 4 220 601 -241 -601
		mu 0 4 231 232 253 252
		f 4 221 602 -242 -602
		mu 0 4 232 233 254 253
		f 4 222 603 -243 -603
		mu 0 4 233 234 255 254
		f 4 223 604 -244 -604
		mu 0 4 234 235 256 255
		f 4 224 605 -245 -605
		mu 0 4 235 236 257 256
		f 4 225 606 -246 -606
		mu 0 4 236 237 258 257
		f 4 226 607 -247 -607
		mu 0 4 237 238 259 258
		f 4 227 608 -248 -608
		mu 0 4 238 239 260 259
		f 4 228 609 -249 -609
		mu 0 4 239 240 261 260
		f 4 229 610 -250 -610
		mu 0 4 240 241 262 261
		f 4 230 611 -251 -611
		mu 0 4 241 242 263 262
		f 4 231 612 -252 -612
		mu 0 4 242 243 264 263
		f 4 232 613 -253 -613
		mu 0 4 243 244 265 264
		f 4 233 614 -254 -614
		mu 0 4 244 245 266 265
		f 4 234 615 -255 -615
		mu 0 4 245 246 267 266
		f 4 235 616 -256 -616
		mu 0 4 246 247 268 267
		f 4 236 617 -257 -617
		mu 0 4 247 248 269 268
		f 4 237 618 -258 -618
		mu 0 4 248 249 270 269
		f 4 238 619 -259 -619
		mu 0 4 249 250 271 270
		f 4 239 600 -260 -620
		mu 0 4 250 251 272 271
		f 4 240 621 -261 -621
		mu 0 4 252 253 274 273
		f 4 241 622 -262 -622
		mu 0 4 253 254 275 274
		f 4 242 623 -263 -623
		mu 0 4 254 255 276 275
		f 4 243 624 -264 -624
		mu 0 4 255 256 277 276
		f 4 244 625 -265 -625
		mu 0 4 256 257 278 277
		f 4 245 626 -266 -626
		mu 0 4 257 258 279 278
		f 4 246 627 -267 -627
		mu 0 4 258 259 280 279
		f 4 247 628 -268 -628
		mu 0 4 259 260 281 280
		f 4 248 629 -269 -629
		mu 0 4 260 261 282 281
		f 4 249 630 -270 -630
		mu 0 4 261 262 283 282
		f 4 250 631 -271 -631
		mu 0 4 262 263 284 283
		f 4 251 632 -272 -632
		mu 0 4 263 264 285 284
		f 4 252 633 -273 -633
		mu 0 4 264 265 286 285
		f 4 253 634 -274 -634
		mu 0 4 265 266 287 286
		f 4 254 635 -275 -635
		mu 0 4 266 267 288 287
		f 4 255 636 -276 -636
		mu 0 4 267 268 289 288
		f 4 256 637 -277 -637
		mu 0 4 268 269 290 289
		f 4 257 638 -278 -638
		mu 0 4 269 270 291 290
		f 4 258 639 -279 -639
		mu 0 4 270 271 292 291
		f 4 259 620 -280 -640
		mu 0 4 271 272 293 292
		f 4 260 641 -281 -641
		mu 0 4 273 274 295 294
		f 4 261 642 -282 -642
		mu 0 4 274 275 296 295
		f 4 262 643 -283 -643
		mu 0 4 275 276 297 296
		f 4 263 644 -284 -644
		mu 0 4 276 277 298 297
		f 4 264 645 -285 -645
		mu 0 4 277 278 299 298
		f 4 265 646 -286 -646
		mu 0 4 278 279 300 299
		f 4 266 647 -287 -647
		mu 0 4 279 280 301 300
		f 4 267 648 -288 -648
		mu 0 4 280 281 302 301
		f 4 268 649 -289 -649
		mu 0 4 281 282 303 302
		f 4 269 650 -290 -650
		mu 0 4 282 283 304 303
		f 4 270 651 -291 -651
		mu 0 4 283 284 305 304
		f 4 271 652 -292 -652
		mu 0 4 284 285 306 305
		f 4 272 653 -293 -653
		mu 0 4 285 286 307 306
		f 4 273 654 -294 -654
		mu 0 4 286 287 308 307
		f 4 274 655 -295 -655
		mu 0 4 287 288 309 308
		f 4 275 656 -296 -656
		mu 0 4 288 289 310 309
		f 4 276 657 -297 -657
		mu 0 4 289 290 311 310
		f 4 277 658 -298 -658
		mu 0 4 290 291 312 311
		f 4 278 659 -299 -659
		mu 0 4 291 292 313 312
		f 4 279 640 -300 -660
		mu 0 4 292 293 314 313
		f 4 280 661 -301 -661
		mu 0 4 294 295 316 315
		f 4 281 662 -302 -662
		mu 0 4 295 296 317 316
		f 4 282 663 -303 -663
		mu 0 4 296 297 318 317
		f 4 283 664 -304 -664
		mu 0 4 297 298 319 318
		f 4 284 665 -305 -665
		mu 0 4 298 299 320 319
		f 4 285 666 -306 -666
		mu 0 4 299 300 321 320
		f 4 286 667 -307 -667
		mu 0 4 300 301 322 321
		f 4 287 668 -308 -668
		mu 0 4 301 302 323 322
		f 4 288 669 -309 -669
		mu 0 4 302 303 324 323
		f 4 289 670 -310 -670
		mu 0 4 303 304 325 324
		f 4 290 671 -311 -671
		mu 0 4 304 305 326 325
		f 4 291 672 -312 -672
		mu 0 4 305 306 327 326
		f 4 292 673 -313 -673
		mu 0 4 306 307 328 327
		f 4 293 674 -314 -674
		mu 0 4 307 308 329 328
		f 4 294 675 -315 -675
		mu 0 4 308 309 330 329
		f 4 295 676 -316 -676
		mu 0 4 309 310 331 330
		f 4 296 677 -317 -677
		mu 0 4 310 311 332 331
		f 4 297 678 -318 -678
		mu 0 4 311 312 333 332
		f 4 298 679 -319 -679
		mu 0 4 312 313 334 333
		f 4 299 660 -320 -680
		mu 0 4 313 314 335 334
		f 4 300 681 -321 -681
		mu 0 4 315 316 337 336
		f 4 301 682 -322 -682
		mu 0 4 316 317 338 337
		f 4 302 683 -323 -683
		mu 0 4 317 318 339 338
		f 4 303 684 -324 -684
		mu 0 4 318 319 340 339
		f 4 304 685 -325 -685
		mu 0 4 319 320 341 340
		f 4 305 686 -326 -686
		mu 0 4 320 321 342 341
		f 4 306 687 -327 -687
		mu 0 4 321 322 343 342
		f 4 307 688 -328 -688
		mu 0 4 322 323 344 343
		f 4 308 689 -329 -689
		mu 0 4 323 324 345 344
		f 4 309 690 -330 -690
		mu 0 4 324 325 346 345
		f 4 310 691 -331 -691
		mu 0 4 325 326 347 346
		f 4 311 692 -332 -692
		mu 0 4 326 327 348 347
		f 4 312 693 -333 -693
		mu 0 4 327 328 349 348
		f 4 313 694 -334 -694
		mu 0 4 328 329 350 349
		f 4 314 695 -335 -695
		mu 0 4 329 330 351 350
		f 4 315 696 -336 -696
		mu 0 4 330 331 352 351
		f 4 316 697 -337 -697
		mu 0 4 331 332 353 352
		f 4 317 698 -338 -698
		mu 0 4 332 333 354 353
		f 4 318 699 -339 -699
		mu 0 4 333 334 355 354
		f 4 319 680 -340 -700
		mu 0 4 334 335 356 355
		f 4 320 701 -341 -701
		mu 0 4 336 337 358 357
		f 4 321 702 -342 -702
		mu 0 4 337 338 359 358
		f 4 322 703 -343 -703
		mu 0 4 338 339 360 359
		f 4 323 704 -344 -704
		mu 0 4 339 340 361 360
		f 4 324 705 -345 -705
		mu 0 4 340 341 362 361
		f 4 325 706 -346 -706
		mu 0 4 341 342 363 362
		f 4 326 707 -347 -707
		mu 0 4 342 343 364 363
		f 4 327 708 -348 -708
		mu 0 4 343 344 365 364
		f 4 328 709 -349 -709
		mu 0 4 344 345 366 365
		f 4 329 710 -350 -710
		mu 0 4 345 346 367 366
		f 4 330 711 -351 -711
		mu 0 4 346 347 368 367
		f 4 331 712 -352 -712
		mu 0 4 347 348 369 368
		f 4 332 713 -353 -713
		mu 0 4 348 349 370 369
		f 4 333 714 -354 -714
		mu 0 4 349 350 371 370
		f 4 334 715 -355 -715
		mu 0 4 350 351 372 371
		f 4 335 716 -356 -716
		mu 0 4 351 352 373 372
		f 4 336 717 -357 -717
		mu 0 4 352 353 374 373
		f 4 337 718 -358 -718
		mu 0 4 353 354 375 374
		f 4 338 719 -359 -719
		mu 0 4 354 355 376 375
		f 4 339 700 -360 -720
		mu 0 4 355 356 377 376
		f 4 340 721 912 -721
		mu 0 4 357 358 507 509
		f 4 341 722 910 -722
		mu 0 4 358 359 506 507
		f 4 342 723 908 -723
		mu 0 4 359 360 505 506
		f 4 343 724 906 -724
		mu 0 4 360 361 504 505
		f 4 344 725 904 -725
		mu 0 4 361 362 503 504
		f 4 345 726 902 -726
		mu 0 4 362 363 502 503
		f 4 346 727 939 -727
		mu 0 4 363 364 522 502
		f 4 347 728 938 -728
		mu 0 4 364 365 521 522
		f 4 348 729 936 -729
		mu 0 4 365 366 520 521
		f 4 349 730 934 -730
		mu 0 4 366 367 519 520
		f 4 350 731 932 -731
		mu 0 4 367 368 518 519
		f 4 351 732 930 -732
		mu 0 4 368 369 517 518
		f 4 352 733 928 -733
		mu 0 4 369 370 516 517
		f 4 353 734 926 -734
		mu 0 4 370 371 515 516
		f 4 354 735 924 -735
		mu 0 4 371 372 514 515
		f 4 355 736 922 -736
		mu 0 4 372 373 513 514
		f 4 356 737 920 -737
		mu 0 4 373 374 512 513
		f 4 357 738 918 -738
		mu 0 4 374 375 511 512
		f 4 358 739 916 -739
		mu 0 4 375 376 510 511
		f 4 359 720 914 -740
		mu 0 4 376 377 508 510
		f 3 -1 -741 741
		mu 0 3 1 0 399
		f 3 -2 -742 742
		mu 0 3 2 1 400
		f 3 -3 -743 743
		mu 0 3 3 2 401
		f 3 -4 -744 744
		mu 0 3 4 3 402
		f 3 -5 -745 745
		mu 0 3 5 4 403
		f 3 -6 -746 746
		mu 0 3 6 5 404
		f 3 -7 -747 747
		mu 0 3 7 6 405
		f 3 -8 -748 748
		mu 0 3 8 7 406
		f 3 -9 -749 749
		mu 0 3 9 8 407
		f 3 -10 -750 750
		mu 0 3 10 9 408
		f 3 -11 -751 751
		mu 0 3 11 10 409
		f 3 -12 -752 752
		mu 0 3 12 11 410
		f 3 -13 -753 753
		mu 0 3 13 12 411
		f 3 -14 -754 754
		mu 0 3 14 13 412
		f 3 -15 -755 755
		mu 0 3 15 14 413
		f 3 -16 -756 756
		mu 0 3 16 15 414
		f 3 -17 -757 757
		mu 0 3 17 16 415
		f 3 -18 -758 758
		mu 0 3 18 17 416
		f 3 -19 -759 759
		mu 0 3 19 18 417
		f 3 -20 -760 740
		mu 0 3 20 19 418
		f 3 360 761 -761
		mu 0 3 378 379 419
		f 3 361 762 -762
		mu 0 3 379 380 420
		f 3 362 763 -763
		mu 0 3 380 381 421
		f 3 363 764 -764
		mu 0 3 381 382 422
		f 3 364 765 -765
		mu 0 3 382 383 423
		f 3 365 766 -766
		mu 0 3 383 384 424
		f 3 366 767 -767
		mu 0 3 384 385 425
		f 3 367 768 -768
		mu 0 3 385 386 426
		f 3 368 769 -769
		mu 0 3 386 387 427
		f 3 369 770 -770
		mu 0 3 387 388 428
		f 3 370 771 -771
		mu 0 3 388 389 429
		f 3 371 772 -772
		mu 0 3 389 390 430
		f 3 372 773 -773
		mu 0 3 390 391 431
		f 3 373 774 -774
		mu 0 3 391 392 432
		f 3 374 775 -775
		mu 0 3 392 393 433
		f 3 375 776 -776
		mu 0 3 393 394 434
		f 3 376 777 -777
		mu 0 3 394 395 435
		f 3 377 778 -778
		mu 0 3 395 396 436
		f 3 378 779 -779
		mu 0 3 396 397 437
		f 3 379 760 -780
		mu 0 3 397 398 438
		f 4 -783 780 962 -782
		mu 0 4 440 439 533 534
		f 4 -785 781 964 -784
		mu 0 4 441 440 534 535
		f 4 -787 783 966 -786
		mu 0 4 442 441 535 536
		f 4 -789 785 968 -788
		mu 0 4 443 442 536 537
		f 4 -791 787 970 -790
		mu 0 4 444 443 537 538
		f 4 -793 789 972 -792
		mu 0 4 446 444 538 540
		f 4 -795 791 974 -794
		mu 0 4 447 445 539 541
		f 4 -797 793 976 -796
		mu 0 4 448 447 541 542
		f 4 -799 795 978 -798
		mu 0 4 449 448 542 543
		f 4 -801 797 979 -800
		mu 0 4 450 449 543 523
		f 4 -803 799 942 -802
		mu 0 4 451 450 523 524
		f 4 -805 801 944 -804
		mu 0 4 452 451 524 525
		f 4 -807 803 946 -806
		mu 0 4 453 452 525 526
		f 4 -809 805 948 -808
		mu 0 4 454 453 526 527
		f 4 -811 807 950 -810
		mu 0 4 455 454 527 528
		f 4 -813 809 952 -812
		mu 0 4 456 455 528 529
		f 4 -815 811 954 -814
		mu 0 4 457 456 529 530
		f 4 -817 813 956 -816
		mu 0 4 458 457 530 531
		f 4 -819 815 958 -818
		mu 0 4 459 458 531 532
		f 4 -820 817 960 -781
		mu 0 4 439 459 532 533
		f 4 -823 820 782 -822
		mu 0 4 461 460 439 440
		f 4 -825 821 784 -824
		mu 0 4 462 461 440 441
		f 4 -827 823 786 -826
		mu 0 4 463 462 441 442
		f 4 -829 825 788 -828
		mu 0 4 464 463 442 443
		f 4 -831 827 790 -830
		mu 0 4 465 464 443 444
		f 4 -833 829 792 -832
		mu 0 4 467 465 444 446
		f 4 -835 831 794 -834
		mu 0 4 468 466 445 447
		f 4 -837 833 796 -836
		mu 0 4 469 468 447 448
		f 4 -839 835 798 -838
		mu 0 4 470 469 448 449
		f 4 -841 837 800 -840
		mu 0 4 471 470 449 450
		f 4 -843 839 802 -842
		mu 0 4 472 471 450 451
		f 4 -845 841 804 -844
		mu 0 4 473 472 451 452
		f 4 -847 843 806 -846
		mu 0 4 474 473 452 453
		f 4 -849 845 808 -848
		mu 0 4 475 474 453 454
		f 4 -851 847 810 -850
		mu 0 4 476 475 454 455
		f 4 -853 849 812 -852
		mu 0 4 477 476 455 456
		f 4 -855 851 814 -854
		mu 0 4 478 477 456 457
		f 4 -857 853 816 -856
		mu 0 4 479 478 457 458
		f 4 -859 855 818 -858
		mu 0 4 480 479 458 459
		f 4 -860 857 819 -821
		mu 0 4 460 480 459 439
		f 4 -863 860 822 -862
		mu 0 4 482 481 460 461
		f 4 -865 861 824 -864
		mu 0 4 483 482 461 462
		f 4 -867 863 826 -866
		mu 0 4 484 483 462 463
		f 4 -869 865 828 -868
		mu 0 4 485 484 463 464
		f 4 -871 867 830 -870
		mu 0 4 486 485 464 465
		f 4 -873 869 832 -872
		mu 0 4 488 486 465 467
		f 4 -875 871 834 -874
		mu 0 4 489 487 466 468
		f 4 -877 873 836 -876
		mu 0 4 490 489 468 469
		f 4 -879 875 838 -878
		mu 0 4 491 490 469 470
		f 4 -881 877 840 -880
		mu 0 4 492 491 470 471
		f 4 -883 879 842 -882
		mu 0 4 493 492 471 472
		f 4 -885 881 844 -884
		mu 0 4 494 493 472 473
		f 4 -887 883 846 -886
		mu 0 4 495 494 473 474
		f 4 -889 885 848 -888
		mu 0 4 496 495 474 475
		f 4 -891 887 850 -890
		mu 0 4 497 496 475 476
		f 4 -893 889 852 -892
		mu 0 4 498 497 476 477
		f 4 -895 891 854 -894
		mu 0 4 499 498 477 478
		f 4 -897 893 856 -896
		mu 0 4 500 499 478 479
		f 4 -899 895 858 -898
		mu 0 4 501 500 479 480
		f 4 -900 897 859 -861
		mu 0 4 481 501 480 460
		f 4 -903 900 862 -902
		mu 0 4 503 502 481 482
		f 4 -905 901 864 -904
		mu 0 4 504 503 482 483
		f 4 -907 903 866 -906
		mu 0 4 505 504 483 484
		f 4 -909 905 868 -908
		mu 0 4 506 505 484 485
		f 4 -911 907 870 -910
		mu 0 4 507 506 485 486
		f 4 -913 909 872 -912
		mu 0 4 509 507 486 488
		f 4 -915 911 874 -914
		mu 0 4 510 508 487 489
		f 4 -917 913 876 -916
		mu 0 4 511 510 489 490
		f 4 -919 915 878 -918
		mu 0 4 512 511 490 491
		f 4 -921 917 880 -920
		mu 0 4 513 512 491 492
		f 4 -923 919 882 -922
		mu 0 4 514 513 492 493
		f 4 -925 921 884 -924
		mu 0 4 515 514 493 494
		f 4 -927 923 886 -926
		mu 0 4 516 515 494 495
		f 4 -929 925 888 -928
		mu 0 4 517 516 495 496
		f 4 -931 927 890 -930
		mu 0 4 518 517 496 497
		f 4 -933 929 892 -932
		mu 0 4 519 518 497 498
		f 4 -935 931 894 -934
		mu 0 4 520 519 498 499
		f 4 -937 933 896 -936
		mu 0 4 521 520 499 500
		f 4 -939 935 898 -938
		mu 0 4 522 521 500 501
		f 4 -940 937 899 -901
		mu 0 4 502 522 501 481
		f 4 -943 940 -376 -942
		mu 0 4 524 523 394 393
		f 4 -945 941 -375 -944
		mu 0 4 525 524 393 392
		f 4 -947 943 -374 -946
		mu 0 4 526 525 392 391
		f 4 -949 945 -373 -948
		mu 0 4 527 526 391 390
		f 4 -951 947 -372 -950
		mu 0 4 528 527 390 389
		f 4 -953 949 -371 -952
		mu 0 4 529 528 389 388
		f 4 -955 951 -370 -954
		mu 0 4 530 529 388 387
		f 4 -957 953 -369 -956
		mu 0 4 531 530 387 386
		f 4 -959 955 -368 -958
		mu 0 4 532 531 386 385
		f 4 -961 957 -367 -960
		mu 0 4 533 532 385 384
		f 4 -963 959 -366 -962
		mu 0 4 534 533 384 383
		f 4 -965 961 -365 -964
		mu 0 4 535 534 383 382
		f 4 -967 963 -364 -966
		mu 0 4 536 535 382 381
		f 4 -969 965 -363 -968
		mu 0 4 537 536 381 380
		f 4 -971 967 -362 -970
		mu 0 4 538 537 380 379
		f 4 -973 969 -361 -972
		mu 0 4 540 538 379 378
		f 4 -975 971 -380 -974
		mu 0 4 541 539 398 397
		f 4 -977 973 -379 -976
		mu 0 4 542 541 397 396
		f 4 -979 975 -378 -978
		mu 0 4 543 542 396 395
		f 4 -980 977 -377 -941
		mu 0 4 523 543 395 394;
	setAttr ".cd" -type "dataPolyComponent" Index_Data Edge 0 ;
	setAttr ".cvd" -type "dataPolyComponent" Index_Data Vertex 0 ;
	setAttr ".hfd" -type "dataPolyComponent" Index_Data Face 0 ;
createNode lightLinker -s -n "lightLinker1";
	setAttr -s 8 ".lnk";
	setAttr -s 8 ".slnk";
createNode displayLayerManager -n "layerManager";
createNode displayLayer -n "defaultLayer";
createNode renderLayerManager -n "renderLayerManager";
createNode renderLayer -n "defaultRenderLayer";
	setAttr ".g" yes;

createNode script -n "sceneConfigurationScriptNode";
	setAttr ".b" -type "string" "playbackOptions -min 1 -max 120 -ast 1 -aet 200 ";
	setAttr ".st" 6;
createNode groupId -n "groupId12";
	setAttr ".ihi" 0;
createNode shadingEngine -n "eyeWhitePhongSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo12";
createNode phong -n "eyeWhitePhong";
	setAttr ".dc" 1;
	setAttr ".c" -type "float3" 0.93387997 0.93387997 0.93387997 ;
	setAttr ".sc" -type "float3" 0.074380003 0.074380003 0.074380003 ;
	setAttr ".rfl" 0.0099999997764825821;
	setAttr ".cp" 5.2399997711181641;
createNode groupId -n "groupId13";
	setAttr ".ihi" 0;
createNode shadingEngine -n "pupilPhongSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo10";
createNode phong -n "pupilPhong";
	setAttr ".dc" 1;
	setAttr ".c" -type "float3" 0 0 0 ;
	setAttr ".sc" -type "float3" 0.090226598 0.090226598 0.090226598 ;
	setAttr ".rfl" 0;
	setAttr ".cp" 5.2399997711181641;
createNode groupId -n "groupId14";
	setAttr ".ihi" 0;
createNode shadingEngine -n "irisPhongSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo11";
createNode phong -n "irisPhong";
	setAttr ".c" -type "float3" 0.11451896 0.23926146 0.31706721 ;
	setAttr ".sc" -type "float3" 0.20661999 0.20661999 0.20661999 ;
	setAttr ".rfl" 0.019999999552965164;
	setAttr ".cp" 5.2399997711181641;
createNode groupParts -n "groupParts6";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "f[400:459]";
createNode groupParts -n "groupParts5";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 2 "f[380:399]" "f[480:499]";
createNode groupParts -n "groupParts4";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 2 "f[0:379]" "f[460:479]";
select -ne :time1;
	setAttr -av -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".o" 1;
	setAttr -av ".unw" 1;
	setAttr -k on ".etw";
	setAttr -k on ".tps";
	setAttr -k on ".tms";
lockNode -l 1 ;
select -ne :renderPartition;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 5 ".st";
	setAttr -cb on ".an";
	setAttr -cb on ".pt";
lockNode -l 1 ;
select -ne :initialShadingGroup;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".mwc";
	setAttr -cb on ".an";
	setAttr -cb on ".il";
	setAttr -cb on ".vo";
	setAttr -cb on ".eo";
	setAttr -cb on ".fo";
	setAttr -cb on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :initialParticleSE;
	setAttr -av -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".mwc";
	setAttr -cb on ".an";
	setAttr -cb on ".il";
	setAttr -cb on ".vo";
	setAttr -cb on ".eo";
	setAttr -cb on ".fo";
	setAttr -cb on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :defaultShaderList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 5 ".s";
lockNode -l 1 ;
select -ne :postProcessList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 2 ".p";
lockNode -l 1 ;
select -ne :defaultRenderingList1;
lockNode -l 1 ;
select -ne :renderGlobalsList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
lockNode -l 1 ;
select -ne :defaultRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".macc";
	setAttr -k on ".macd";
	setAttr -k on ".macq";
	setAttr -k on ".mcfr";
	setAttr -cb on ".ifg";
	setAttr -k on ".clip";
	setAttr -k on ".edm";
	setAttr -k on ".edl";
	setAttr -cb on ".ren";
	setAttr -av -k on ".esr";
	setAttr -k on ".ors";
	setAttr -cb on ".sdf";
	setAttr -av -k on ".outf";
	setAttr -cb on ".imfkey";
	setAttr -k on ".gama";
	setAttr -k on ".an";
	setAttr -cb on ".ar";
	setAttr -k on ".fs" 1;
	setAttr -k on ".ef" 10;
	setAttr -av -k on ".bfs";
	setAttr -cb on ".me";
	setAttr -cb on ".se";
	setAttr -k on ".be";
	setAttr -cb on ".ep";
	setAttr -k on ".fec";
	setAttr -av -k on ".ofc";
	setAttr -cb on ".ofe";
	setAttr -cb on ".efe";
	setAttr -cb on ".oft";
	setAttr -cb on ".umfn";
	setAttr -cb on ".ufe";
	setAttr -cb on ".pff";
	setAttr -cb on ".peie";
	setAttr -cb on ".ifp";
	setAttr -k on ".comp";
	setAttr -k on ".cth";
	setAttr -k on ".soll";
	setAttr -k on ".sosl";
	setAttr -k on ".rd";
	setAttr -k on ".lp";
	setAttr -av -k on ".sp";
	setAttr -k on ".shs";
	setAttr -av -k on ".lpr";
	setAttr -cb on ".gv";
	setAttr -cb on ".sv";
	setAttr -k on ".mm";
	setAttr -k on ".npu";
	setAttr -k on ".itf";
	setAttr -k on ".shp";
	setAttr -cb on ".isp";
	setAttr -k on ".uf";
	setAttr -k on ".oi";
	setAttr -k on ".rut";
	setAttr -cb on ".mb";
	setAttr -av -k on ".mbf";
	setAttr -k on ".afp";
	setAttr -k on ".pfb";
	setAttr -k on ".pram";
	setAttr -k on ".poam";
	setAttr -k on ".prlm";
	setAttr -k on ".polm";
	setAttr -cb on ".prm";
	setAttr -cb on ".pom";
	setAttr -cb on ".pfrm";
	setAttr -cb on ".pfom";
	setAttr -av -k on ".bll";
	setAttr -k on ".bls";
	setAttr -k on ".smv";
	setAttr -k on ".ubc";
	setAttr -k on ".mbc";
	setAttr -cb on ".mbt";
	setAttr -k on ".udbx";
	setAttr -k on ".smc";
	setAttr -k on ".kmv";
	setAttr -cb on ".isl";
	setAttr -cb on ".ism";
	setAttr -cb on ".imb";
	setAttr -k on ".rlen";
	setAttr -av -k on ".frts";
	setAttr -k on ".tlwd";
	setAttr -k on ".tlht";
	setAttr -k on ".jfc";
	setAttr -cb on ".rsb";
	setAttr -k on ".ope";
	setAttr -k on ".oppf";
	setAttr -cb on ".hbl";
select -ne :defaultResolution;
	setAttr -av -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -av -k on ".w";
	setAttr -av -k on ".h";
	setAttr -av -k on ".pa" 1;
	setAttr -av -k on ".al";
	setAttr -av -k on ".dar";
	setAttr -av -k on ".ldar";
	setAttr -k on ".dpi";
	setAttr -av -k on ".off";
	setAttr -av -k on ".fld";
	setAttr -av -k on ".zsl";
	setAttr -k on ".isu";
	setAttr -k on ".pdu";
select -ne :defaultLightSet;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -k on ".mwc";
	setAttr -k on ".an";
	setAttr -k on ".il";
	setAttr -k on ".vo";
	setAttr -k on ".eo";
	setAttr -k on ".fo";
	setAttr -k on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :defaultObjectSet;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -k on ".mwc";
	setAttr -k on ".an";
	setAttr -k on ".il";
	setAttr -k on ".vo";
	setAttr -k on ".eo";
	setAttr -k on ".fo";
	setAttr -k on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :hardwareRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k off ".ctrs" 256;
	setAttr -av -k off ".btrs" 512;
	setAttr -k off ".fbfm";
	setAttr -k off -cb on ".ehql";
	setAttr -k off -cb on ".eams";
	setAttr -k off -cb on ".eeaa";
	setAttr -k off -cb on ".engm";
	setAttr -k off -cb on ".mes";
	setAttr -k off -cb on ".emb";
	setAttr -av -k off -cb on ".mbbf";
	setAttr -k off -cb on ".mbs";
	setAttr -k off -cb on ".trm";
	setAttr -k off -cb on ".tshc";
	setAttr -k off ".enpt";
	setAttr -k off -cb on ".clmt";
	setAttr -k off -cb on ".tcov";
	setAttr -k off -cb on ".lith";
	setAttr -k off -cb on ".sobc";
	setAttr -k off -cb on ".cuth";
	setAttr -k off -cb on ".hgcd";
	setAttr -k off -cb on ".hgci";
	setAttr -k off -cb on ".mgcs";
	setAttr -k off -cb on ".twa";
	setAttr -k off -cb on ".twz";
	setAttr -k on ".hwcc";
	setAttr -k on ".hwdp";
	setAttr -k on ".hwql";
	setAttr -k on ".hwfr";
	setAttr -k on ".soll";
	setAttr -k on ".sosl";
	setAttr -k on ".bswa";
	setAttr -k on ".shml";
	setAttr -k on ".hwel";
lockNode -l 1 ;
select -ne :hardwareRenderingGlobals;
	setAttr ".otfna" -type "stringArray" 22 "NURBS Curves" "NURBS Surfaces" "Polygons" "Subdiv Surface" "Particles" "Particle Instance" "Fluids" "Strokes" "Image Planes" "UI" "Lights" "Cameras" "Locators" "Joints" "IK Handles" "Deformers" "Motion Trails" "Components" "Hair Systems" "Follicles" "Misc. UI" "Ornaments"  ;
	setAttr ".otfva" -type "Int32Array" 22 0 1 1 1 1 1
		 1 1 1 0 0 0 0 0 0 0 0 0
		 0 0 0 0 ;
	setAttr ".fprt" yes;
select -ne :defaultHardwareRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -av -k on ".rp";
	setAttr -k on ".cai";
	setAttr -k on ".coi";
	setAttr -cb on ".bc";
	setAttr -av -k on ".bcb";
	setAttr -av -k on ".bcg";
	setAttr -av -k on ".bcr";
	setAttr -k on ".ei";
	setAttr -av -k on ".ex";
	setAttr -av -k on ".es";
	setAttr -av -k on ".ef";
	setAttr -av -k on ".bf";
	setAttr -k on ".fii";
	setAttr -av -k on ".sf";
	setAttr -k on ".gr";
	setAttr -k on ".li";
	setAttr -k on ".ls";
	setAttr -av -k on ".mb";
	setAttr -k on ".ti";
	setAttr -k on ".txt";
	setAttr -k on ".mpr";
	setAttr -k on ".wzd";
	setAttr -k on ".fn" -type "string" "im";
	setAttr -k on ".if";
	setAttr -k on ".res" -type "string" "ntsc_4d 646 485 1.333";
	setAttr -k on ".as";
	setAttr -k on ".ds";
	setAttr -k on ".lm";
	setAttr -av -k on ".fir";
	setAttr -k on ".aap";
	setAttr -av -k on ".gh";
	setAttr -cb on ".sd";
lockNode -l 1 ;
connectAttr "groupId12.id" "eyeballShape.iog.og[0].gid";
connectAttr "eyeWhitePhongSG.mwc" "eyeballShape.iog.og[0].gco";
connectAttr "groupId13.id" "eyeballShape.iog.og[1].gid";
connectAttr "pupilPhongSG.mwc" "eyeballShape.iog.og[1].gco";
connectAttr "groupId14.id" "eyeballShape.iog.og[2].gid";
connectAttr "irisPhongSG.mwc" "eyeballShape.iog.og[2].gco";
connectAttr "groupParts6.og" "eyeballShape.i";
relationship "link" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "eyeWhitePhongSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "pupilPhongSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "irisPhongSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "eyeWhitePhongSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "pupilPhongSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "irisPhongSG.message" ":defaultLightSet.message";
connectAttr "layerManager.dli[0]" "defaultLayer.id";
connectAttr "renderLayerManager.rlmi[0]" "defaultRenderLayer.rlid";
connectAttr "eyeWhitePhong.oc" "eyeWhitePhongSG.ss";
connectAttr "groupId12.msg" "eyeWhitePhongSG.gn" -na;
connectAttr "eyeballShape.iog.og[0]" "eyeWhitePhongSG.dsm" -na;
connectAttr "eyeWhitePhongSG.msg" "materialInfo12.sg";
connectAttr "eyeWhitePhong.msg" "materialInfo12.m";
connectAttr "pupilPhong.oc" "pupilPhongSG.ss";
connectAttr "groupId13.msg" "pupilPhongSG.gn" -na;
connectAttr "eyeballShape.iog.og[1]" "pupilPhongSG.dsm" -na;
connectAttr "pupilPhongSG.msg" "materialInfo10.sg";
connectAttr "pupilPhong.msg" "materialInfo10.m";
connectAttr "irisPhong.oc" "irisPhongSG.ss";
connectAttr "groupId14.msg" "irisPhongSG.gn" -na;
connectAttr "eyeballShape.iog.og[2]" "irisPhongSG.dsm" -na;
connectAttr "irisPhongSG.msg" "materialInfo11.sg";
connectAttr "irisPhong.msg" "materialInfo11.m";
connectAttr "groupParts5.og" "groupParts6.ig";
connectAttr "groupId14.id" "groupParts6.gi";
connectAttr "groupParts4.og" "groupParts5.ig";
connectAttr "groupId13.id" "groupParts5.gi";
connectAttr "eyeballShapeOrig.w" "groupParts4.ig";
connectAttr "groupId12.id" "groupParts4.gi";
connectAttr "eyeWhitePhongSG.pa" ":renderPartition.st" -na;
connectAttr "pupilPhongSG.pa" ":renderPartition.st" -na;
connectAttr "irisPhongSG.pa" ":renderPartition.st" -na;
connectAttr "eyeWhitePhong.msg" ":defaultShaderList1.s" -na;
connectAttr "pupilPhong.msg" ":defaultShaderList1.s" -na;
connectAttr "irisPhong.msg" ":defaultShaderList1.s" -na;
connectAttr "defaultRenderLayer.msg" ":defaultRenderingList1.r" -na;
// End of headTopologyEyeBall.ma
