/* XPM */
static char *biped_250_43_OffK0[] = {
/* columns rows colors chars-per-pixel */
"21 21 192 2",
"   c #888888",
".  c #8B8B8B",
"X  c #8C8C8B",
"o  c gray55",
"O  c #8D8D8D",
"+  c #8E8E8E",
"@  c #8E8F8E",
"#  c gray56",
"$  c #909090",
"%  c #909091",
"&  c #929292",
"*  c gray58",
"=  c #959594",
"-  c #959595",
";  c gray59",
":  c #979796",
">  c #979797",
",  c #989898",
"<  c #989998",
"1  c #989999",
"2  c gray60",
"3  c #9A9A99",
"4  c #9A9A9A",
"5  c #9B9B9B",
"6  c gray61",
"7  c #9D9D9D",
"8  c gray62",
"9  c #9F9F9F",
"0  c #9FA0A0",
"q  c #A2A2A2",
"w  c #A4A3A4",
"e  c #A6A6A5",
"r  c gray65",
"t  c #A7A7A6",
"y  c #A7A7A7",
"u  c #A9A9A9",
"i  c #ADACAC",
"p  c gray68",
"a  c #AEAEAE",
"s  c #AEAEAF",
"d  c gray69",
"f  c #B1B0B0",
"g  c #B1B1B1",
"h  c #B3B4B4",
"j  c gray71",
"k  c #B7B7B6",
"l  c #B7B7B8",
"z  c gray72",
"x  c #B9B9B9",
"c  c gray73",
"v  c #BABBBB",
"b  c #BBBCBB",
"n  c #BDBDBC",
"m  c gray74",
"M  c #BFBEBF",
"N  c gray75",
"B  c #C0C0C0",
"V  c #C1C2C1",
"C  c gray76",
"Z  c #C3C3C3",
"A  c gray78",
"S  c #C8C8C8",
"D  c #C9C8C8",
"F  c #CBCBCC",
"G  c gray80",
"H  c #CDCDCD",
"J  c #CECDCE",
"K  c #CECECE",
"L  c gray81",
"P  c #CFD0D0",
"I  c #D1CFD0",
"U  c #D0D0D0",
"Y  c #D0D0D1",
"T  c gray82",
"R  c #D2D2D1",
"E  c #D2D2D2",
"W  c #D3D4D4",
"Q  c #D5D6D5",
"!  c #D6D5D5",
"~  c #D6D5D6",
"^  c #D7D7D6",
"/  c #D7D7D7",
"(  c #D8D8D8",
")  c #D9D9D8",
"_  c #D9D9DA",
"`  c #DBDBDC",
"'  c #DCDBDC",
"]  c gainsboro",
"[  c #DDDDDE",
"{  c #DDDEDE",
"}  c #DEDEDD",
"|  c gray87",
" . c #DFDFE0",
".. c #E1E0E1",
"X. c #E1E1E1",
"o. c #E3E2E2",
"O. c gray89",
"+. c #E3E2E4",
"@. c #E4E5E4",
"#. c #E5E4E4",
"$. c gray90",
"%. c #E6E6E5",
"&. c #E6E6E6",
"*. c #E6E6E7",
"=. c #E7E8E9",
"-. c gray91",
";. c #E8E9E9",
":. c #EAE9E8",
">. c #EAE9EA",
",. c #EAEAEA",
"<. c #EAEAEB",
"1. c #EBEAEA",
"2. c #EBEAEB",
"3. c gray92",
"4. c #EBEBEC",
"5. c #ECECED",
"6. c gray93",
"7. c #EDEDEE",
"8. c #ECEEED",
"9. c #EDEEED",
"0. c #EFEEEF",
"q. c #EFEFEF",
"w. c #EFEFF0",
"e. c #EFF0F0",
"r. c #F0F0F1",
"t. c #F0F1F1",
"y. c #F1F1F1",
"u. c #F3F2F1",
"i. c #F3F3F3",
"p. c #F3F4F3",
"a. c #F4F3F5",
"s. c #F4F4F3",
"d. c #F4F4F4",
"f. c #F4F5F4",
"g. c #F5F4F5",
"h. c gray96",
"j. c #F6F6F6",
"k. c gray97",
"l. c #F8F7F7",
"z. c #F8F9F9",
"x. c #F9F9F9",
"c. c #F9F9FA",
"v. c #F9FAF9",
"b. c #FAFAF9",
"n. c gray98",
"m. c #FAFBFA",
"M. c #FBFAFB",
"N. c #FBFBFA",
"B. c #FBFBFB",
"V. c #FBFCFC",
"C. c #FCFBFA",
"Z. c #FCFBFC",
"A. c #FDFBFD",
"S. c #FCFCFB",
"D. c #FDFCFB",
"F. c gray99",
"G. c #FCFDFD",
"H. c #FDFCFD",
"J. c #FDFDFC",
"K. c #FDFDFD",
"L. c #FCFDFE",
"P. c #FDFCFE",
"I. c #FDFDFE",
"U. c #FDFDFF",
"Y. c #FCFEFD",
"T. c #FDFEFC",
"R. c #FDFEFD",
"E. c #FDFFFC",
"W. c #FDFFFD",
"Q. c #FCFEFE",
"!. c #FDFEFE",
"~. c #FDFEFF",
"^. c #FDFFFE",
"/. c #FDFFFF",
"(. c #FEFDFD",
"). c #FEFCFE",
"_. c #FEFDFE",
"`. c #FEFDFF",
"'. c #FFFCFF",
"]. c #FFFDFE",
"[. c #FFFDFF",
"{. c #FEFEFD",
"}. c #FEFFFD",
"|. c #FFFEFD",
" X c #FFFFFD",
".X c #FEFEFE",
"XX c #FEFEFF",
"oX c #FEFFFE",
"OX c #FEFFFF",
"+X c #FFFEFE",
"@X c #FFFFFE",
"#X c gray100",
/* pixels */
"N N N N B N N N N N N N N V N N B B N N N ",
"N 2 O y H %.9.y.i.9.) j - - 8 8 8 8 8 8 N ",
"N - v  .g.E.z.2.V 0 2 2 > 8 8 5 2 $ O $ N ",
"N H 2.z.).).z.) 2       O O & 2 9 f V I N ",
"N g.).).E.).z.O.j s s h v N D I ) %.9.g.N ",
"N ).).).E.E.E.j.2. .+.2.9.i.z.M.g.3.| H N ",
"N ).).).E.).E.).).).).).).M.q.| D v t > N ",
"N ).).).).).).Y.).).).).).k.) N y 8 2 > N ",
"B ).).).).).Y.Y.).).).).).m.;.) I I P D M ",
"B ).).).).).).).).).).).).).Y.m.E.m.E.z.M ",
"N ).).).).).).).).).).).).z.y.2.:.&.%.| B ",
"B ).).).).).).).).).).).).p.W k y 5 * $ N ",
"N ).).).).).).).).).).).).m.e.X.I M s 7 N ",
"N ).).).).).).).).).).).E.).m.v.g.q.:.o.N ",
"N ).).Y.).m.).).).).).).E.2.) ^  .3.y.g.N ",
"N m.Y.Y.Y.Y.Y.).).).).).M.| k y u f k N N ",
"N P ] @.8.p.z.Y.).z.m.).m.2.H f 4 o   $ B ",
"N 6 t g N A I W W I W | %.y.y.@.F f 9 - N ",
"N 3 = $ O O @ & & @ @ 8 s Z ^ %.w.9.W s N ",
"N 8 8 8 8 8 8 8 8 8 8 2 & . $ y C  .4.<.N ",
"N N N N N N N N N N N N N N N N N N N N N "
};
