//Maya ASCII 2012 scene
//Name: hand.ma
//Last modified: Wed, Aug 01, 2018 11:14:44 AM
//Codeset: 1252
requires maya "2008";
currentUnit -l centimeter -a degree -t pal;
fileInfo "application" "maya";
fileInfo "product" "Maya 2012";
fileInfo "version" "2012 x64";
fileInfo "cutIdentifier" "201201172029-821146";
fileInfo "osv" "Microsoft Business Edition, 64-bit  (Build 9200)\n";
createNode transform -s -n "persp";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -8.1641831059978607 15.518733050039323 1.7126584773500535 ;
	setAttr ".r" -type "double3" -41.738352729644909 -32.599999999999838 0 ;
createNode camera -s -n "perspShape" -p "persp";
	setAttr -k off ".v" no;
	setAttr ".fl" 34.999999999999986;
	setAttr ".ncp" 1;
	setAttr ".coi" 2.7588936883280013;
	setAttr ".imn" -type "string" "persp";
	setAttr ".den" -type "string" "persp_depth";
	setAttr ".man" -type "string" "persp_mask";
	setAttr ".tp" -type "double3" -7.6248662779265022 13.586404414961269 -0.69509775748569391 ;
	setAttr ".hc" -type "string" "viewSet -p %camera";
createNode transform -s -n "top";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -6.1340808730217304 100.1 -0.099880376122548631 ;
	setAttr ".r" -type "double3" -89.999999999999986 0 0 ;
	setAttr ".rp" -type "double3" -8.8817841970012523e-016 0 -1.4210854715202004e-014 ;
	setAttr ".rpt" -type "double3" 0 -1.4210854715202007e-014 1.4210854715202007e-014 ;
createNode camera -s -n "topShape" -p "top";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 86.7333260018262;
	setAttr ".ow" 3.5769469860975529;
	setAttr ".imn" -type "string" "top";
	setAttr ".den" -type "string" "top_depth";
	setAttr ".man" -type "string" "top_mask";
	setAttr ".tp" -type "double3" -5.9391435011269715 13.366673998173795 1.0480841472576816 ;
	setAttr ".hc" -type "string" "viewSet -t %camera";
	setAttr ".o" yes;
createNode transform -s -n "front";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 0 0 100.1 ;
createNode camera -s -n "frontShape" -p "front";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 100.1;
	setAttr ".ow" 30;
	setAttr ".imn" -type "string" "front";
	setAttr ".den" -type "string" "front_depth";
	setAttr ".man" -type "string" "front_mask";
	setAttr ".hc" -type "string" "viewSet -f %camera";
	setAttr ".o" yes;
createNode transform -s -n "side";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 100.1 0 0 ;
	setAttr ".r" -type "double3" 0 89.999999999999986 0 ;
createNode camera -s -n "sideShape" -p "side";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 100.1;
	setAttr ".ow" 30;
	setAttr ".imn" -type "string" "side";
	setAttr ".den" -type "string" "side_depth";
	setAttr ".man" -type "string" "side_mask";
	setAttr ".hc" -type "string" "viewSet -s %camera";
	setAttr ".o" yes;
createNode transform -n "FitSkeleton";
	addAttr -ci true -sn "visCylinders" -ln "visCylinders" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "visBoxes" -ln "visBoxes" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "visBones" -ln "visBones" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "lockCenterJoints" -ln "lockCenterJoints" -dv 1 -min 0 -max 
		1 -at "bool";
	addAttr -ci true -sn "visGap" -ln "visGap" -dv 0.75 -min 0 -max 1 -at "double";
	addAttr -ci true -m -im false -sn "drivingSystem" -ln "drivingSystem" -at "message";
	addAttr -ci true -m -sn "drivingSystem_Fingers_R" -ln "drivingSystem_Fingers_R" 
		-dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -m -sn "drivingSystem_Fingers_L" -ln "drivingSystem_Fingers_L" 
		-dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visGeoType" -ln "visGeoType" -min 0 -max 3 -en "cylinders:boxes:spheres:bones" 
		-at "enum";
	addAttr -ci true -sn "visSpheres" -ln "visSpheres" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visGeo" -ln "visGeo" -dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visPoleVector" -ln "visPoleVector" -dv 1 -min 0 -max 
		1 -at "bool";
	addAttr -ci true -k true -sn "visJointOrient" -ln "visJointOrient" -dv 1 -min 0 
		-max 1 -at "bool";
	addAttr -ci true -k true -sn "visJointAxis" -ln "visJointAxis" -dv 1 -min 0 -max 
		1 -at "bool";
	addAttr -ci true -sn "preRebuildScript" -ln "preRebuildScript" -dt "string";
	addAttr -ci true -sn "postRebuildScript" -ln "postRebuildScript" -dt "string";
	setAttr -l on ".v";
	setAttr ".ove" yes;
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr ".visCylinders" yes;
	setAttr ".visGap" 1;
	setAttr -s 36 ".drivingSystem";
	setAttr -s 18 ".drivingSystem_Fingers_R";
	setAttr -s 18 ".drivingSystem_Fingers_R";
	setAttr -s 18 ".drivingSystem_Fingers_L";
	setAttr -s 18 ".drivingSystem_Fingers_L";
	setAttr -k on ".visJointAxis" no;
createNode nurbsCurve -n "FitSkeletonShape" -p "FitSkeleton";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 29;
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		2.3508348746736751 1.4394712022965405e-016 -2.3508348746736716
		-3.7929511823487981e-016 2.035719696933274e-016 -3.3245825626631635
		-2.3508348746736729 1.4394712022965413e-016 -2.3508348746736729
		-3.3245825626631635 5.899006384856358e-032 -9.6338085217116898e-016
		-2.3508348746736734 -1.4394712022965408e-016 2.350834874673672
		-1.0017616090771558e-015 -2.0357196969332745e-016 3.3245825626631644
		2.3508348746736716 -1.4394712022965413e-016 2.3508348746736729
		3.3245825626631635 -1.0933890203714376e-031 1.7856397797841755e-015
		2.3508348746736751 1.4394712022965405e-016 -2.3508348746736716
		-3.7929511823487981e-016 2.035719696933274e-016 -3.3245825626631635
		-2.3508348746736729 1.4394712022965413e-016 -2.3508348746736729
		;
createNode joint -n "Wrist" -p "FitSkeleton";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -6.4183734558094905 13.706785158268946 -0.1696690065040059 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 89.999999999797751 3.1221304621157175 179.99999999998897 ;
	setAttr ".dl" yes;
	setAttr ".typ" 12;
	setAttr -k on ".fat" 0.16999999999999987;
	setAttr -k on ".fatY" 2.3100000000000005;
	setAttr -k on ".fatZ";
createNode joint -n "MiddleFinger0" -p "Wrist";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.81552251075616233 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.14802744265737022 -8.0491169285323849e-016 -5.6843418860808015e-014 ;
	setAttr ".r" -type "double3" -2.2908561886248614e-013 -6.6144351242499304e-012 1.9083328088794328e-014 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.7624949642210076e-014 -1.0869789588730207e-014 3.9672003574184087 ;
	setAttr -k on ".fat" 0.2;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleFinger1" -p "MiddleFinger0";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.81552251075617122 4.9960036108132044e-016 9.9475983006414026e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -0.070036638869411028 4.7753179570062345 -1.6795505552291781 ;
	setAttr ".pa" -type "double3" -2.490303168013669e-017 3.8068719241856406 -4.0949047407001542 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "21";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleFinger2" -p "MiddleFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.31064094986492563 5.2588849941415106e-011 -9.1446850092324894e-012 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0.037241937627337546 -2.5199985606459374 -0.0025982402234920217 ;
	setAttr ".pa" -type "double3" 0 0 2.5199999009299203 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "20";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleFinger3" -p "MiddleFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.17127015324651307 -8.6042284408449632e-016 -1.9539925233402755e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0.0534548327756647 -3.671293638078525 -0.0014021136048468971 ;
	setAttr ".pa" -type "double3" 0 0 3.6712939054552742 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "19";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "MiddleFinger4" -p "MiddleFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.20934604463340634 4.7878367936959876e-016 6.7501559897209518e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 89.968442131915481 -2.8191128262803148e-016 -2.981770013872048e-016 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "18";
	setAttr -k on ".fat";
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "IndexFinger0" -p "Wrist";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.73384035427455085 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.14392912189822749 0.075135880584169062 2.3803181647963356e-013 ;
	setAttr ".r" -type "double3" -6.1543733086318333e-013 -1.5453520058560894e-012 -5.2900094307824302e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.3148122205060741e-026 1.3621255297242618 16.719488387420189 ;
	setAttr -k on ".fat" 0.2;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "IndexFinger1" -p "IndexFinger0";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.73384035427455885 -7.5495165674510645e-015 1.9539925233402755e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0.84882643250151524 1.8129986149473585 2.2993767045187279 ;
	setAttr ".pa" -type "double3" 0.065532877363568762 20.527688987272207 -2.5422327562497964 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "8";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "IndexFinger2" -p "IndexFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.26385832488508409 2.1463941735078151e-011 1.0188827559431957e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.13329671330404e-017 -3.0052893291660826e-016 0 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "7";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "IndexFinger3" -p "IndexFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.1755194130119424 -8.4376949871511897e-015 -5.1514348342607263e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.6418340242698568 -5.7596206078048446 0.066225387225870638 ;
	setAttr ".pa" -type "double3" 0 0 5.7600000490223469 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "6";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "IndexFinger4" -p "IndexFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.18547823185201295 -5.3290705182007514e-015 5.6843418860808015e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 90.981952553513963 6.4111228205251822e-016 -6.361109362927032e-015 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "5";
	setAttr -k on ".fat";
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "ThumbFinger1" -p "Wrist";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.19583042388726746 0.1557528386881035 -0.1009187113342076 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -52.263999999993672 19.323320728479711 41.56208636253583 ;
	setAttr ".pa" -type "double3" -34.462082586865911 -8.7285733235282201 -1.7903981777634761 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "4";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "ThumbFinger2" -p "ThumbFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.35088249761040902 1.5418244458942354e-009 2.0206076811746243e-009 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.5902773407317584e-015 0 4.7708320221952752e-015 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "3";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "ThumbFinger3" -p "ThumbFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.16868589271562751 7.1054273576010019e-015 3.0198066269804258e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.5902773407317584e-015 0 4.7708320221952752e-015 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "2";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "ThumbFinger4" -p "ThumbFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.20328945754907135 -3.6920998702782981e-007 -2.359402886398243e-007 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -12.991445460162398 3.1805546814635176e-015 3.1805546814635176e-015 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "1";
	setAttr -k on ".fat";
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyFinger0" -p "Wrist";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.74684065235472996 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.13300026654050967 -0.11924161827773719 -4.4408920985006262e-013 ;
	setAttr ".r" -type "double3" -3.219317691643845e-013 3.0257511215766685e-013 3.2837984681938278e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.3255713088536781e-026 5.6787710405157723 -16.916147405610989 ;
	setAttr -k on ".fat" 0.2;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyFinger1" -p "PinkyFinger0";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.74684065235474062 5.773159728050814e-015 -1.7763568394002505e-015 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.141629661878607 2.8755572217246521 -0.51765035392345282 ;
	setAttr ".pa" -type "double3" -0.21586850671656455 -15.856897343794616 -7.9762775885025459 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "12";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyFinger2" -p "PinkyFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.23273224634690948 -6.4392935428259079e-015 -8.5265128291212022e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0.26683083078457293 -0.7191758971237544 -0.034439123916509745 ;
	setAttr ".pa" -type "double3" 0 0 0.71999997359174039 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "11";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyFinger3" -p "PinkyFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.14230168593650028 -7.3274719625260332e-015 -5.3290705182007514e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.158876549890612 -5.7546090601921041 -0.24956300887638949 ;
	setAttr ".pa" -type "double3" 0 0 5.7599997887354624 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "10";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "PinkyFinger4" -p "PinkyFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.17792340227426173 -4.8849813083506888e-015 4.9737991503207013e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 90.328532512368341 -7.0211108801656143e-015 -2.2263882770244624e-014 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "9";
	setAttr -k on ".fat";
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "RingFinger0" -p "Wrist";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.76390651666884235 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.15110118322671617 -0.056351910438128539 -2.6822988274943782e-013 ;
	setAttr ".r" -type "double3" -9.8159868856667831e-013 -3.6002388109159909e-013 1.771295628657271e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0 3.4774074932464161 -9.4642768296362618 ;
	setAttr -k on ".fat" 0.2;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "RingFinger1" -p "RingFinger0";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.76390651666883969 2.6645352591003757e-015 5.3290705182007514e-015 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -0.58674107672314979 -1.3508121522853473 5.1040541840314182 ;
	setAttr ".pa" -type "double3" -0.07133019936876682 -2.835223641928581 -1.4417652325251511 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "16";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "RingFinger2" -p "RingFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.28952412379075554 9.7699626167013776e-015 -1.3677947663381929e-013 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -0.28317914938274708 2.1599955702211568 0.0043909097712978885 ;
	setAttr ".pa" -type "double3" 0 0 -2.1600000310934706 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "15";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "RingFinger3" -p "RingFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.1750602747689527 -2.55351295663786e-015 1.5809575870662229e-013 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 0.56783371127766979 -4.3198948128062407 -0.030192099090926613 ;
	setAttr ".pa" -type "double3" 0 0 4.3200001190538568 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "14";
	setAttr -k on ".fat" 0.11999999999999991;
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";
createNode joint -n "RingFinger4" -p "RingFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.11999999999999991 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.19369354822798623 -8.2156503822261584e-015 -3.0198066269804258e-014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 89.832984336315889 -1.2073509976626381e-015 1.5902773407317584e-015 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "13";
	setAttr -k on ".fat";
	setAttr -k on ".fatY";
	setAttr -k on ".fatZ";

createNode animCurveUA -n "SDK1FKIndexFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKIndexFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKIndexFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  0.98788672685623169 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0.15517689287662506 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 0.98788672685623169;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0.15517689287662506;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKRingFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKRingFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger3_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger2_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001309999998 0 0 10 50;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger3_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKThumbFinger2_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "thumbCurl" -ln "thumbCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 1;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -2 -18.000001313690912 0 0 10 90;
	setAttr -s 3 ".kit[0:2]"  2 1 1;
	setAttr -s 3 ".kot[0:2]"  2 1 1;
	setAttr -s 3 ".kix[1:2]"  1 0.98788672685623169;
	setAttr -s 3 ".kiy[1:2]"  0 0.15517690777778625;
	setAttr -s 3 ".kox[1:2]"  0.98788672685623169 1;
	setAttr -s 3 ".koy[1:2]"  0.15517690777778625 0;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -5 -20 0 0 10 40;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -5 29.999999999999996 0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -5 14.999999999999998 0 0 10 -29.999999999999996;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -5 -20 0 0 10 40;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -5 29.999999999999996 0 0 10 -59.999999999999993;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 3 ".ktv[0:2]"  -5 14.999999999999998 0 0 10 -29.999999999999996;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKCup_R_rotateX";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "cup" -ln "cup" -smn 0 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 65;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKCup_L_rotateX";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "cup" -ln "cup" -smn 0 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 65;
	setAttr ".pre" 4;
	setAttr ".pst" 4;

createNode multiplyDivide -n "WristFat";
createNode multiplyDivide -n "RingFinger0Fat";
createNode multiplyDivide -n "RingFinger1Fat";
createNode multiplyDivide -n "RingFinger2Fat";
createNode multiplyDivide -n "RingFinger3Fat";
createNode multiplyDivide -n "RingFinger4Fat";
createNode multiplyDivide -n "PinkyFinger0Fat";
createNode multiplyDivide -n "PinkyFinger1Fat";
createNode multiplyDivide -n "PinkyFinger2Fat";
createNode multiplyDivide -n "PinkyFinger3Fat";
createNode multiplyDivide -n "PinkyFinger4Fat";
createNode multiplyDivide -n "ThumbFinger1Fat";
createNode multiplyDivide -n "ThumbFinger2Fat";
createNode multiplyDivide -n "ThumbFinger3Fat";
createNode multiplyDivide -n "ThumbFinger4Fat";
createNode multiplyDivide -n "IndexFinger0Fat";
createNode multiplyDivide -n "IndexFinger1Fat";
createNode multiplyDivide -n "IndexFinger2Fat";
createNode multiplyDivide -n "IndexFinger3Fat";
createNode multiplyDivide -n "IndexFinger4Fat";
createNode multiplyDivide -n "MiddleFinger0Fat";
createNode multiplyDivide -n "MiddleFinger1Fat";
createNode multiplyDivide -n "MiddleFinger2Fat";
createNode multiplyDivide -n "MiddleFinger3Fat";
createNode multiplyDivide -n "MiddleFinger4Fat";

connectAttr "SDK2FKIndexFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKRingFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger2_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger3_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKCup_R_rotateX.drivingSystemOut" "FitSkeleton.drivingSystem" -na
		;
connectAttr "SDK2FKIndexFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKRingFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger2_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKThumbFinger3_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKCup_L_rotateX.drivingSystemOut" "FitSkeleton.drivingSystem" -na
		;
connectAttr "WristFat.oy" "Wrist.fatYabs";
connectAttr "WristFat.oz" "Wrist.fatZabs";
connectAttr "Wrist.s" "MiddleFinger0.is";
connectAttr "MiddleFinger0Fat.oy" "MiddleFinger0.fatYabs";
connectAttr "MiddleFinger0Fat.oz" "MiddleFinger0.fatZabs";
connectAttr "MiddleFinger0.s" "MiddleFinger1.is";
connectAttr "MiddleFinger1Fat.oy" "MiddleFinger1.fatYabs";
connectAttr "MiddleFinger1Fat.oz" "MiddleFinger1.fatZabs";
connectAttr "MiddleFinger1.s" "MiddleFinger2.is";
connectAttr "MiddleFinger2Fat.oy" "MiddleFinger2.fatYabs";
connectAttr "MiddleFinger2Fat.oz" "MiddleFinger2.fatZabs";
connectAttr "MiddleFinger2.s" "MiddleFinger3.is";
connectAttr "MiddleFinger3Fat.oy" "MiddleFinger3.fatYabs";
connectAttr "MiddleFinger3Fat.oz" "MiddleFinger3.fatZabs";
connectAttr "MiddleFinger3.s" "MiddleFinger4.is";
connectAttr "MiddleFinger4Fat.oy" "MiddleFinger4.fatYabs";
connectAttr "MiddleFinger4Fat.oz" "MiddleFinger4.fatZabs";
connectAttr "Wrist.s" "IndexFinger0.is";
connectAttr "IndexFinger0Fat.oy" "IndexFinger0.fatYabs";
connectAttr "IndexFinger0Fat.oz" "IndexFinger0.fatZabs";
connectAttr "IndexFinger0.s" "IndexFinger1.is";
connectAttr "IndexFinger1Fat.oy" "IndexFinger1.fatYabs";
connectAttr "IndexFinger1Fat.oz" "IndexFinger1.fatZabs";
connectAttr "IndexFinger1.s" "IndexFinger2.is";
connectAttr "IndexFinger2Fat.oy" "IndexFinger2.fatYabs";
connectAttr "IndexFinger2Fat.oz" "IndexFinger2.fatZabs";
connectAttr "IndexFinger2.s" "IndexFinger3.is";
connectAttr "IndexFinger3Fat.oy" "IndexFinger3.fatYabs";
connectAttr "IndexFinger3Fat.oz" "IndexFinger3.fatZabs";
connectAttr "IndexFinger3.s" "IndexFinger4.is";
connectAttr "IndexFinger4Fat.oy" "IndexFinger4.fatYabs";
connectAttr "IndexFinger4Fat.oz" "IndexFinger4.fatZabs";
connectAttr "Wrist.s" "ThumbFinger1.is";
connectAttr "ThumbFinger1Fat.oy" "ThumbFinger1.fatYabs";
connectAttr "ThumbFinger1Fat.oz" "ThumbFinger1.fatZabs";
connectAttr "ThumbFinger1.s" "ThumbFinger2.is";
connectAttr "ThumbFinger2Fat.oy" "ThumbFinger2.fatYabs";
connectAttr "ThumbFinger2Fat.oz" "ThumbFinger2.fatZabs";
connectAttr "ThumbFinger2.s" "ThumbFinger3.is";
connectAttr "ThumbFinger3Fat.oy" "ThumbFinger3.fatYabs";
connectAttr "ThumbFinger3Fat.oz" "ThumbFinger3.fatZabs";
connectAttr "ThumbFinger3.s" "ThumbFinger4.is";
connectAttr "ThumbFinger4Fat.oy" "ThumbFinger4.fatYabs";
connectAttr "ThumbFinger4Fat.oz" "ThumbFinger4.fatZabs";
connectAttr "Wrist.s" "PinkyFinger0.is";
connectAttr "PinkyFinger0Fat.oy" "PinkyFinger0.fatYabs";
connectAttr "PinkyFinger0Fat.oz" "PinkyFinger0.fatZabs";
connectAttr "PinkyFinger0.s" "PinkyFinger1.is";
connectAttr "PinkyFinger1Fat.oy" "PinkyFinger1.fatYabs";
connectAttr "PinkyFinger1Fat.oz" "PinkyFinger1.fatZabs";
connectAttr "PinkyFinger1.s" "PinkyFinger2.is";
connectAttr "PinkyFinger2Fat.oy" "PinkyFinger2.fatYabs";
connectAttr "PinkyFinger2Fat.oz" "PinkyFinger2.fatZabs";
connectAttr "PinkyFinger2.s" "PinkyFinger3.is";
connectAttr "PinkyFinger3Fat.oy" "PinkyFinger3.fatYabs";
connectAttr "PinkyFinger3Fat.oz" "PinkyFinger3.fatZabs";
connectAttr "PinkyFinger3.s" "PinkyFinger4.is";
connectAttr "PinkyFinger4Fat.oy" "PinkyFinger4.fatYabs";
connectAttr "PinkyFinger4Fat.oz" "PinkyFinger4.fatZabs";
connectAttr "Wrist.s" "RingFinger0.is";
connectAttr "RingFinger0Fat.oy" "RingFinger0.fatYabs";
connectAttr "RingFinger0Fat.oz" "RingFinger0.fatZabs";
connectAttr "RingFinger0.s" "RingFinger1.is";
connectAttr "RingFinger1Fat.oy" "RingFinger1.fatYabs";
connectAttr "RingFinger1Fat.oz" "RingFinger1.fatZabs";
connectAttr "RingFinger1.s" "RingFinger2.is";
connectAttr "RingFinger2Fat.oy" "RingFinger2.fatYabs";
connectAttr "RingFinger2Fat.oz" "RingFinger2.fatZabs";
connectAttr "RingFinger2.s" "RingFinger3.is";
connectAttr "RingFinger3Fat.oy" "RingFinger3.fatYabs";
connectAttr "RingFinger3Fat.oz" "RingFinger3.fatZabs";
connectAttr "RingFinger3.s" "RingFinger4.is";
connectAttr "RingFinger4Fat.oy" "RingFinger4.fatYabs";
connectAttr "RingFinger4Fat.oz" "RingFinger4.fatZabs";

connectAttr "FitSkeleton.drivingSystem_Fingers_L[6]" "SDK1FKIndexFinger3_L_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[5]" "SDK1FKIndexFinger2_L_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[4]" "SDK2FKIndexFinger1_L_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[5]" "SDK1FKIndexFinger2_R_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[6]" "SDK1FKIndexFinger3_R_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[4]" "SDK2FKIndexFinger1_R_rotateY.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[8]" "SDK1FKMiddleFinger3_L_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[7]" "SDK1FKMiddleFinger2_L_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[9]" "SDK1FKMiddleFinger1_L_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[8]" "SDK1FKMiddleFinger3_R_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[9]" "SDK1FKMiddleFinger1_R_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[7]" "SDK1FKMiddleFinger2_R_rotateY.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[11]" "SDK1FKRingFinger3_L_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[10]" "SDK1FKRingFinger2_L_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[12]" "SDK2FKRingFinger1_L_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[10]" "SDK1FKRingFinger2_R_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[11]" "SDK1FKRingFinger3_R_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[12]" "SDK2FKRingFinger1_R_rotateY.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[14]" "SDK1FKPinkyFinger3_L_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[13]" "SDK1FKPinkyFinger2_L_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[15]" "SDK2FKPinkyFinger1_L_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[13]" "SDK1FKPinkyFinger2_R_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[15]" "SDK2FKPinkyFinger1_R_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[14]" "SDK1FKPinkyFinger3_R_rotateY.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[17]" "SDK1FKThumbFinger3_L_rotateY.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[16]" "SDK1FKThumbFinger2_L_rotateY.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[17]" "SDK1FKThumbFinger3_R_rotateY.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[16]" "SDK1FKThumbFinger2_R_rotateY.thumbCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[1]" "SDK1FKIndexFinger1_R_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[0]" "SDK1FKPinkyFinger1_R_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[2]" "SDK1FKRingFinger1_R_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[1]" "SDK1FKIndexFinger1_L_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[0]" "SDK1FKPinkyFinger1_L_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[2]" "SDK1FKRingFinger1_L_rotateZ.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[3]" "SDK1FKCup_R_rotateX.cup";
connectAttr "FitSkeleton.drivingSystem_Fingers_L[3]" "SDK1FKCup_L_rotateX.cup";

connectAttr "Wrist.fat" "WristFat.i1y";
connectAttr "Wrist.fat" "WristFat.i1z";
connectAttr "Wrist.fatY" "WristFat.i2y";
connectAttr "Wrist.fatZ" "WristFat.i2z";
connectAttr "RingFinger0.fat" "RingFinger0Fat.i1y";
connectAttr "RingFinger0.fat" "RingFinger0Fat.i1z";
connectAttr "RingFinger0.fatY" "RingFinger0Fat.i2y";
connectAttr "RingFinger0.fatZ" "RingFinger0Fat.i2z";
connectAttr "RingFinger1.fat" "RingFinger1Fat.i1y";
connectAttr "RingFinger1.fat" "RingFinger1Fat.i1z";
connectAttr "RingFinger1.fatY" "RingFinger1Fat.i2y";
connectAttr "RingFinger1.fatZ" "RingFinger1Fat.i2z";
connectAttr "RingFinger2.fat" "RingFinger2Fat.i1y";
connectAttr "RingFinger2.fat" "RingFinger2Fat.i1z";
connectAttr "RingFinger2.fatY" "RingFinger2Fat.i2y";
connectAttr "RingFinger2.fatZ" "RingFinger2Fat.i2z";
connectAttr "RingFinger3.fat" "RingFinger3Fat.i1y";
connectAttr "RingFinger3.fat" "RingFinger3Fat.i1z";
connectAttr "RingFinger3.fatY" "RingFinger3Fat.i2y";
connectAttr "RingFinger3.fatZ" "RingFinger3Fat.i2z";
connectAttr "RingFinger4.fat" "RingFinger4Fat.i1y";
connectAttr "RingFinger4.fat" "RingFinger4Fat.i1z";
connectAttr "RingFinger4.fatY" "RingFinger4Fat.i2y";
connectAttr "RingFinger4.fatZ" "RingFinger4Fat.i2z";
connectAttr "PinkyFinger0.fat" "PinkyFinger0Fat.i1y";
connectAttr "PinkyFinger0.fat" "PinkyFinger0Fat.i1z";
connectAttr "PinkyFinger0.fatY" "PinkyFinger0Fat.i2y";
connectAttr "PinkyFinger0.fatZ" "PinkyFinger0Fat.i2z";
connectAttr "PinkyFinger1.fat" "PinkyFinger1Fat.i1y";
connectAttr "PinkyFinger1.fat" "PinkyFinger1Fat.i1z";
connectAttr "PinkyFinger1.fatY" "PinkyFinger1Fat.i2y";
connectAttr "PinkyFinger1.fatZ" "PinkyFinger1Fat.i2z";
connectAttr "PinkyFinger2.fat" "PinkyFinger2Fat.i1y";
connectAttr "PinkyFinger2.fat" "PinkyFinger2Fat.i1z";
connectAttr "PinkyFinger2.fatY" "PinkyFinger2Fat.i2y";
connectAttr "PinkyFinger2.fatZ" "PinkyFinger2Fat.i2z";
connectAttr "PinkyFinger3.fat" "PinkyFinger3Fat.i1y";
connectAttr "PinkyFinger3.fat" "PinkyFinger3Fat.i1z";
connectAttr "PinkyFinger3.fatY" "PinkyFinger3Fat.i2y";
connectAttr "PinkyFinger3.fatZ" "PinkyFinger3Fat.i2z";
connectAttr "PinkyFinger4.fat" "PinkyFinger4Fat.i1y";
connectAttr "PinkyFinger4.fat" "PinkyFinger4Fat.i1z";
connectAttr "PinkyFinger4.fatY" "PinkyFinger4Fat.i2y";
connectAttr "PinkyFinger4.fatZ" "PinkyFinger4Fat.i2z";
connectAttr "ThumbFinger1.fat" "ThumbFinger1Fat.i1y";
connectAttr "ThumbFinger1.fat" "ThumbFinger1Fat.i1z";
connectAttr "ThumbFinger1.fatY" "ThumbFinger1Fat.i2y";
connectAttr "ThumbFinger1.fatZ" "ThumbFinger1Fat.i2z";
connectAttr "ThumbFinger2.fat" "ThumbFinger2Fat.i1y";
connectAttr "ThumbFinger2.fat" "ThumbFinger2Fat.i1z";
connectAttr "ThumbFinger2.fatY" "ThumbFinger2Fat.i2y";
connectAttr "ThumbFinger2.fatZ" "ThumbFinger2Fat.i2z";
connectAttr "ThumbFinger3.fat" "ThumbFinger3Fat.i1y";
connectAttr "ThumbFinger3.fat" "ThumbFinger3Fat.i1z";
connectAttr "ThumbFinger3.fatY" "ThumbFinger3Fat.i2y";
connectAttr "ThumbFinger3.fatZ" "ThumbFinger3Fat.i2z";
connectAttr "ThumbFinger4.fat" "ThumbFinger4Fat.i1y";
connectAttr "ThumbFinger4.fat" "ThumbFinger4Fat.i1z";
connectAttr "ThumbFinger4.fatY" "ThumbFinger4Fat.i2y";
connectAttr "ThumbFinger4.fatZ" "ThumbFinger4Fat.i2z";
connectAttr "IndexFinger0.fat" "IndexFinger0Fat.i1y";
connectAttr "IndexFinger0.fat" "IndexFinger0Fat.i1z";
connectAttr "IndexFinger0.fatY" "IndexFinger0Fat.i2y";
connectAttr "IndexFinger0.fatZ" "IndexFinger0Fat.i2z";
connectAttr "IndexFinger1.fat" "IndexFinger1Fat.i1y";
connectAttr "IndexFinger1.fat" "IndexFinger1Fat.i1z";
connectAttr "IndexFinger1.fatY" "IndexFinger1Fat.i2y";
connectAttr "IndexFinger1.fatZ" "IndexFinger1Fat.i2z";
connectAttr "IndexFinger2.fat" "IndexFinger2Fat.i1y";
connectAttr "IndexFinger2.fat" "IndexFinger2Fat.i1z";
connectAttr "IndexFinger2.fatY" "IndexFinger2Fat.i2y";
connectAttr "IndexFinger2.fatZ" "IndexFinger2Fat.i2z";
connectAttr "IndexFinger3.fat" "IndexFinger3Fat.i1y";
connectAttr "IndexFinger3.fat" "IndexFinger3Fat.i1z";
connectAttr "IndexFinger3.fatY" "IndexFinger3Fat.i2y";
connectAttr "IndexFinger3.fatZ" "IndexFinger3Fat.i2z";
connectAttr "IndexFinger4.fat" "IndexFinger4Fat.i1y";
connectAttr "IndexFinger4.fat" "IndexFinger4Fat.i1z";
connectAttr "IndexFinger4.fatY" "IndexFinger4Fat.i2y";
connectAttr "IndexFinger4.fatZ" "IndexFinger4Fat.i2z";
connectAttr "MiddleFinger0.fat" "MiddleFinger0Fat.i1y";
connectAttr "MiddleFinger0.fat" "MiddleFinger0Fat.i1z";
connectAttr "MiddleFinger0.fatY" "MiddleFinger0Fat.i2y";
connectAttr "MiddleFinger0.fatZ" "MiddleFinger0Fat.i2z";
connectAttr "MiddleFinger1.fat" "MiddleFinger1Fat.i1y";
connectAttr "MiddleFinger1.fat" "MiddleFinger1Fat.i1z";
connectAttr "MiddleFinger1.fatY" "MiddleFinger1Fat.i2y";
connectAttr "MiddleFinger1.fatZ" "MiddleFinger1Fat.i2z";
connectAttr "MiddleFinger2.fat" "MiddleFinger2Fat.i1y";
connectAttr "MiddleFinger2.fat" "MiddleFinger2Fat.i1z";
connectAttr "MiddleFinger2.fatY" "MiddleFinger2Fat.i2y";
connectAttr "MiddleFinger2.fatZ" "MiddleFinger2Fat.i2z";
connectAttr "MiddleFinger3.fat" "MiddleFinger3Fat.i1y";
connectAttr "MiddleFinger3.fat" "MiddleFinger3Fat.i1z";
connectAttr "MiddleFinger3.fatY" "MiddleFinger3Fat.i2y";
connectAttr "MiddleFinger3.fatZ" "MiddleFinger3Fat.i2z";
connectAttr "MiddleFinger4.fat" "MiddleFinger4Fat.i1y";
connectAttr "MiddleFinger4.fat" "MiddleFinger4Fat.i1z";
connectAttr "MiddleFinger4.fatY" "MiddleFinger4Fat.i2y";
connectAttr "MiddleFinger4.fatZ" "MiddleFinger4Fat.i2z";
// End of hand.ma
