/* XPM */
static char *biped_48_80_OffK1[] = {
/* columns rows colors chars-per-pixel */
"11 15 48 1",
"  c #D47E7E",
". c #D67F7F",
"X c #D77F7F",
"o c #D87F7F",
"O c gray75",
"+ c #DA8080",
"@ c #DE8182",
"# c #DE8282",
"$ c #E08383",
"% c #E38484",
"& c #E68585",
"* c #EA8787",
"= c #EE8888",
"- c #EF8888",
"; c #F58B8B",
": c #FB8D8D",
"> c #FC8E8E",
", c #FE8E8E",
"< c #FF8F8F",
"1 c #FF9190",
"2 c #FF9191",
"3 c #FF9292",
"4 c #FF9494",
"5 c #FF9595",
"6 c #FF9696",
"7 c #FF9999",
"8 c #FF9A9A",
"9 c #FF9B9B",
"0 c #FF9B9C",
"q c #FF9D9D",
"w c #FF9E9E",
"e c #FF9F9F",
"r c #FFA0A0",
"t c #FFA0A1",
"y c #FFA1A0",
"u c #FFA2A1",
"i c #FFA2A2",
"p c #FFA3A3",
"a c #FFA4A3",
"s c #FFA4A4",
"d c #FFA5A5",
"f c #FFA6A5",
"g c #FFA6A7",
"h c #FFA7A7",
"j c #FFA8A8",
"k c #FFA9A9",
"l c #FFA9AA",
"z c #FFAAAA",
/* pixels */
"OOOOOOOOOOO",
"O+#=<1<$o%O",
"O ;0ugu<##O",
"O+4gkkk7=XO",
"O&9kkzzg<XO",
"O<ukkkkk7&O",
"O4gkzzkzu:O",
"Oqkzzzzzg1O",
"Oqkkzzzzg4O",
"Oezzzzzzk7O",
"OuzzzzzzkqO",
"OuzzzzzzkqO",
"OukkkzzzkqO",
"OukkzzzzkqO",
"OOOOOOOOOOO"
};
